<settings>

	<setting propertyKey="@Properties.AppVersion" title="@Strings.AppVersionTitle">
		<settingConfig type="alphaNumeric" readonly="true"/>
	</setting>

	<setting propertyKey="@Properties.Theme" title="@Strings.ThemeTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.ThemeBlueDark</listEntry>
			<listEntry value="1">@Strings.ThemePinkDark</listEntry>
			<listEntry value="7">@Strings.ThemeRedDark</listEntry>
			<listEntry value="2">@Strings.ThemeGreenDark</listEntry>
			<listEntry value="4">@Strings.ThemeCornflowerBlueDark</listEntry>
			<listEntry value="5">@Strings.ThemeLemonCreamDark</listEntry>
			<listEntry value="12">@Strings.ThemeVividYellowDark</listEntry>
			<listEntry value="14">@Strings.ThemeCornYellowDark</listEntry>
			<listEntry value="6">@Strings.ThemeDaygloOrangeDark</listEntry>
			<listEntry value="8">@Strings.ThemeMonoDark</listEntry>
			<listEntry value="3">@Strings.ThemeMonoLight</listEntry>
			<listEntry value="9">@Strings.ThemeBlueLight</listEntry>
			<listEntry value="10">@Strings.ThemeGreenLight</listEntry>
			<listEntry value="11">@Strings.ThemeRedLight</listEntry>
			<listEntry value="13">@Strings.ThemeDaygloOrangeLight</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.LeftGoalType" title="@Strings.LeftGoalMeterTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Steps</listEntry>
			<listEntry value="1">@Strings.FloorsClimbed</listEntry>
			<listEntry value="2">@Strings.ActiveMinutes</listEntry>
			<listEntry value="-1">@Strings.Battery</listEntry>
			<listEntry value="-2">@Strings.CaloriesManualGoal</listEntry>
			<listEntry value="-3">@Strings.Off</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.RightGoalType" title="@Strings.RightGoalMeterTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Steps</listEntry>
			<listEntry value="1">@Strings.FloorsClimbed</listEntry>
			<listEntry value="2">@Strings.ActiveMinutes</listEntry>
			<listEntry value="-1">@Strings.Battery</listEntry>
			<listEntry value="-2">@Strings.CaloriesManualGoal</listEntry>
			<listEntry value="-3">@Strings.Off</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.GoalMeterStyle" title="@Strings.GoalMeterStyleTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.AllSegments</listEntry>
			<listEntry value="1">@Strings.AllSegmentsMerged</listEntry>
			<listEntry value="3">@Strings.FilledSegments</listEntry>
			<listEntry value="4">@Strings.FilledSegmentsMerged</listEntry>
			<listEntry value="2">@Strings.Hidden</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.GoalMeterDigitsStyle" title="@Strings.MeterDigitsStyleTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.CurrentTarget</listEntry>
			<listEntry value="1">@Strings.Current</listEntry>
			<listEntry value="2">@Strings.Hidden</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.CaloriesGoal" title="@Strings.CaloriesGoalTitle">
		<settingConfig type="numeric" min="1" max="10000" required="true"/>
	</setting>

	<setting propertyKey="@Properties.FieldCount" title="@Strings.FieldCountTitle">
		<settingConfig type="numeric" min="0" max="3" required="true"/>
	</setting>

	<setting propertyKey="@Properties.Field1Type" title="@Strings.DataField1Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<listEntry value="9">@Strings.HeartRateLive5s</listEntry>
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<listEntry value="12">@Strings.Pressure</listEntry>
			<listEntry value="7">@Strings.Temperature</listEntry>
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<listEntry value="11">@Strings.Weather</listEntry>
			<listEntry value="13">@Strings.Humidity</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Field2Type" title="@Strings.DataField2Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<listEntry value="9">@Strings.HeartRateLive5s</listEntry>
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<listEntry value="12">@Strings.Pressure</listEntry>
			<listEntry value="7">@Strings.Temperature</listEntry>
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<listEntry value="11">@Strings.Weather</listEntry>
			<listEntry value="13">@Strings.Humidity</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Field3Type" title="@Strings.DataField3Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<listEntry value="9">@Strings.HeartRateLive5s</listEntry>
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<listEntry value="12">@Strings.Pressure</listEntry>
			<listEntry value="7">@Strings.Temperature</listEntry>
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<listEntry value="11">@Strings.Weather</listEntry>
			<listEntry value="13">@Strings.Humidity</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.IndicatorCount" title="@Strings.IndicatorCountTitle">
		<settingConfig type="numeric" min="0" max="3" required="true"/>
	</setting>

	<setting propertyKey="@Properties.Indicator1Type" title="@Strings.Indicator1Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Bluetooth</listEntry>
			<listEntry value="1">@Strings.Alarms</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.BluetoothOrNotifications</listEntry>
			<listEntry value="4">@Strings.Battery</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Indicator2Type" title="@Strings.Indicator2Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Bluetooth</listEntry>
			<listEntry value="1">@Strings.Alarms</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.BluetoothOrNotifications</listEntry>
			<listEntry value="4">@Strings.Battery</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Indicator3Type" title="@Strings.Indicator3Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Bluetooth</listEntry>
			<listEntry value="1">@Strings.Alarms</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.BluetoothOrNotifications</listEntry>
			<listEntry value="4">@Strings.Battery</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.HoursColourOverride" title="@Strings.HoursColourTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.FromTheme</listEntry>
			<listEntry value="1">@Strings.MonoHighlight</listEntry>
			<listEntry value="2">@Strings.Mono</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.MinutesColourOverride" title="@Strings.MinutesColourTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.FromTheme</listEntry>
			<listEntry value="1">@Strings.MonoHighlight</listEntry>
			<listEntry value="2">@Strings.Mono</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.HideHoursLeadingZero" title="@Strings.HideHoursLeadingZeroTitle">
		<settingConfig type="boolean"/>
	</setting>

	<setting propertyKey="@Properties.HideSeconds" title="@Strings.HideSecondsTitle">
		<settingConfig type="boolean"/>
	</setting>

	<setting propertyKey="@Properties.LocalTimeInCity" title="@Strings.LocalTimeInCityTitle">
		<settingConfig type="alphaNumeric"/>
	</setting>

	<setting propertyKey="@Properties.MoveBarStyle" title="@Strings.MoveBarStyleTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.AllSegments</listEntry>
			<listEntry value="1">@Strings.FilledSegments</listEntry>
			<listEntry value="2">@Strings.Hidden</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.OWMKeyOverride" title="@Strings.OWMKeyOverride">
		<settingConfig type="alphaNumeric" maxLength="32"/>
	</setting>

</settings>
