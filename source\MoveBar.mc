// Import required Garmin Connect IQ SDK modules for move bar functionality
using Toybox.WatchUi as Ui;                    // User interface and drawable components
using Toybox.System as Sys;                    // System services and device information
using Toybox.Application as App;               // Application context and property access
using Toybox.ActivityMonitor as ActivityMonitor; // Activity monitoring and move bar data
using Toybox.Graphics;                         // Graphics drawing and rendering

import Toybox.Lang;

/**
 * MoveBar class renders the Garmin Move Bar indicator on the watch face.
 *
 * The Move Bar is a visual representation of the user's inactivity level, encouraging
 * movement throughout the day. It consists of segmented bars that fill up as the user
 * remains inactive, with the goal being to keep the bars empty through regular activity.
 *
 * Key Features:
 * - Segmented bar visualization with customizable styling
 * - Support for both buffered and unbuffered rendering modes
 * - Adaptive width for different watch face layouts
 * - Hexagonal/chevron-shaped bar segments for visual appeal
 * - Multiple display styles (all segments, filled only, hidden)
 * - Memory-optimized rendering with selective buffer updates
 *
 * Visual Design:
 * - Bars are drawn as hexagonal/chevron shapes with angled edges
 * - First bar is double-width to emphasize the initial inactivity level
 * - Bars fill from left to right as inactivity increases
 * - Color coding: theme color for filled bars, muted color for empty bars
 * - Separators between bars for clear visual distinction
 *
 * Performance Optimization:
 * - Buffered mode: Uses bitmap buffer for efficient rendering
 * - Selective updates: Only redraws when move bar level changes
 * - Memory management: Optimized color palette for buffer efficiency
 * - Adaptive sizing: Calculates dimensions based on available space
 */
class MoveBar extends Ui.Drawable {

	// Layout and positioning properties
	private var mX, mY, mBaseWidth, mHeight, mSeparator; // Position and dimensions
	private var mTailWidth;                              // Width of hexagonal tail/chevron point

	// Buffered rendering resources (only available in buffered mode)
	(:buffered) private var mBuffer;                     // Bitmap buffer for efficient rendering
	(:buffered) private var mBufferNeedsRedraw = true;   // Flag indicating buffer content needs update
	(:buffered) private var mLastMoveBarLevel;           // Last rendered move bar level for change detection

	// Buffer management (used in both buffered and unbuffered modes)
	private var mBufferNeedsRecreate = true;             // Flag indicating buffer needs recreation

	// Adaptive width management
	// When true, move bar expands to full width (used when seconds display is hidden in sleep mode)
	private var mIsFullWidth = false;

	// Current calculated width (either mBaseWidth or full width based on mIsFullWidth)
	private var mCurrentWidth;

	// Move bar visual styles enumeration (commented out but values used directly)
	// These correspond to user-selectable display styles in watch face settings
	// private enum /* MOVE_BAR_STYLE */ {
	// 	ALL_SEGMENTS,    // 0: Show all segments (filled and empty) for complete visualization
	// 	FILLED_SEGMENTS, // 1: Show only filled segments to minimize visual clutter
	// 	HIDDEN           // 2: Hide move bar completely
	// };

	/**
	 * Type definition for MoveBar constructor parameters.
	 * Defines the layout properties needed to position and size the move bar.
	 */
	typedef MoveBarParams as {
		:x as Number,        // X coordinate of move bar left edge
		:y as Number,        // Y coordinate of move bar center line
		:width as Number,    // Base width of move bar (may be expanded in full-width mode)
		:height as Number,   // Height of move bar segments
		:separator as Number // Width of separators between bar segments
	};

	/**
	 * Constructor for MoveBar drawable component.
	 * Initializes the move bar with specified layout parameters and calculates derived dimensions.
	 *
	 * @param params MoveBarParams containing layout configuration
	 */
	function initialize(params as MoveBarParams) {
		Drawable.initialize(params);

		// Store layout parameters
		mX = params[:x];
		mY = params[:y];
		mBaseWidth = params[:width];     // Base width (mCurrentWidth calculated in draw() when DC available)
		mHeight = params[:height];
		mSeparator = params[:separator];

		// Calculate tail width for hexagonal bar shape (half the height creates proper proportions)
		mTailWidth = mHeight / 2;
	}

	/**
	 * Handles changes to move bar style settings.
	 * Called when user changes move bar style in watch face settings.
	 * Marks buffer for recreation due to potential color palette changes.
	 */
	function onSettingsChanged() {
		// Mark buffer for recreation to handle color palette changes
		mBufferNeedsRecreate = true;
	}

	/**
	 * Sets the move bar to full width mode or returns it to base width.
	 *
	 * Full width mode is used when the seconds display is hidden (typically in sleep mode),
	 * allowing the move bar to expand and fill the available space for better visibility.
	 *
	 * @param fullWidth Boolean indicating whether to use full width (true) or base width (false)
	 */
	function setFullWidth(fullWidth) {
		if (mIsFullWidth != fullWidth) {
			mIsFullWidth = fullWidth;
			// Buffer needs recreation due to width change affecting buffer dimensions
			mBufferNeedsRecreate = true;
		}
	}

	/**
	 * UNBUFFERED DRAWING MODE
	 *
	 * Renders the move bar directly to the screen without using bitmap buffers.
	 * This mode uses less memory but requires redrawing all segments on each update.
	 *
	 * Process:
	 * 1. Check if move bar should be hidden based on style setting
	 * 2. Get current move bar level from activity monitor
	 * 3. Calculate current width (base width or full width)
	 * 4. Draw all bar segments directly to screen
	 *
	 * Performance: Direct drawing, no buffer management overhead
	 * Memory: Minimal memory usage, no bitmap buffers
	 */
	(:unbuffered)
	function draw(dc) {
		// Early exit if move bar is hidden
		if (getPropertyValue("MoveBarStyle") == 2 /* HIDDEN */) {
			return;
		}

		// Get current inactivity level from activity monitor
		var info = ActivityMonitor.getInfo();
		var currentMoveBarLevel = info.moveBarLevel;

		// Calculate current width based on full-width mode setting
		// Full width: expand to fill available space minus margins, accounting for tail width
		// Base width: use configured width from layout
		mCurrentWidth = mIsFullWidth ? (dc.getWidth() - (2 * mX) + mTailWidth) : mBaseWidth;

		// Draw all bar segments directly to screen, vertically centered on mY
		drawBars(dc, mX, mY - (mHeight / 2), currentMoveBarLevel);
	}

	/**
	 * BUFFERED DRAWING MODE
	 *
	 * High-performance rendering using pre-computed bitmap buffers.
	 * This method manages a bitmap buffer containing the move bar segments and only
	 * updates the buffer when the move bar level changes, providing efficient rendering.
	 *
	 * Process:
	 * 1. Check if move bar should be hidden
	 * 2. Get current move bar level and calculate width
	 * 3. Recreate buffer if needed (first draw, color changes, width changes)
	 * 4. Update buffer content only if move bar level changed
	 * 5. Draw buffer to screen with clipping
	 *
	 * Performance: Efficient - only updates buffer when data changes
	 * Memory: Uses bitmap buffer but provides better performance for frequent updates
	 */
	(:buffered)
	function draw(dc) {
		// Early exit if move bar is hidden
		if (getPropertyValue("MoveBarStyle") == 2 /* HIDDEN */) {
			return;
		}

		// Get current inactivity level from activity monitor
		var info = ActivityMonitor.getInfo();
		var currentMoveBarLevel = info.moveBarLevel;

		// Calculate current width based on full-width mode setting
		// Full width: expand to fill available space minus margins, accounting for tail width
		// Base width: use configured width from layout
		mCurrentWidth = mIsFullWidth ? (dc.getWidth() - (2 * mX) + mTailWidth) : mBaseWidth;

		// BUFFER RECREATION: When first draw, color palette changes, or width changes
		if (mBufferNeedsRecreate) {
			recreateBuffer();
		}

		// CHANGE DETECTION: Only redraw buffer content when move bar level changes
		// Issue #7: Optimize performance by avoiding unnecessary buffer updates
		if (currentMoveBarLevel != mLastMoveBarLevel) {
			mLastMoveBarLevel = currentMoveBarLevel;
			mBufferNeedsRedraw = true;
		}

		// BUFFER UPDATE: Redraw buffer content when needed
		if (mBufferNeedsRedraw) {
			var bufferDc = mBuffer.getDc();

			// Issue #85: Clear buffer before redraw for proper "Filled Segments" mode behavior
			// When move bar clears completely, no bars are drawn in filled mode, so buffer must be cleared
			// Note: COLOR_TRANSPARENT doesn't work for clearing, so use background color
			bufferDc.setColor(Graphics.COLOR_TRANSPARENT, gBackgroundColour);
			bufferDc.clear();

			// Draw all bar segments to buffer at origin (0,0)
			drawBars(bufferDc, 0, 0, currentMoveBarLevel);
			mBufferNeedsRedraw = false;
		}

		// SCREEN RENDERING: Draw buffer to screen with clipping for precise positioning
		dc.setClip(mX, mY - (mHeight / 2), mCurrentWidth, mHeight);
		dc.drawBitmap(mX, mY - (mHeight / 2), mBuffer);
		dc.clearClip();
	}

	/**
	 * Creates a new bitmap buffer for move bar rendering with memory-optimized color palette.
	 *
	 * This method is called when the buffer needs to be recreated due to:
	 * - First-time initialization
	 * - Color palette changes (theme changes)
	 * - Width changes (full-width mode toggle)
	 *
	 * The buffer uses a restricted 3-color palette to minimize memory usage while
	 * supporting all necessary move bar colors.
	 *
	 * @note Only available in buffered drawing mode
	 */
	(:buffered)
	function recreateBuffer() {
		var options = {
			:width => mCurrentWidth,    // Buffer width matches current move bar width
			:height => mHeight,         // Buffer height matches move bar height

			// Optimized 3-color palette for memory efficiency
			// First palette color determines initial buffer background color
			:palette => [
				gBackgroundColour,      // Background/clear color
				gMeterBackgroundColour, // Empty segment color
				gThemeColour           // Filled segment color
			]
		};

		// Use newer API if available, fall back to legacy constructor
		if ((Graphics has :createBufferedBitmap)) {
			mBuffer = Graphics.createBufferedBitmap(options).get();
		} else {
			mBuffer = new Graphics.BufferedBitmap(options);
		}

		mBufferNeedsRecreate = false;
		mBufferNeedsRedraw = true; // Ensure newly-created buffer gets content drawn
	}

	/**
	 * Draws all move bar segments to the specified drawing context.
	 *
	 * This is the core rendering method that draws individual bar segments based on
	 * the current move bar level and style settings. It handles both filled and empty
	 * segments according to the user's style preference.
	 *
	 * Bar Characteristics:
	 * - First bar is double-width to emphasize initial inactivity
	 * - Bars are numbered 1-based to match move bar level (0 = no bars)
	 * - Filled bars use theme color, empty bars use muted background color
	 * - Separators provide visual distinction between segments
	 *
	 * Style Handling:
	 * - ALL_SEGMENTS: Show both filled and empty bars
	 * - FILLED_SEGMENTS: Show only filled bars (empty bars are skipped)
	 * - HIDDEN: Not handled here (caller should check before calling)
	 *
	 * @param dc Drawing context (screen DC or buffer DC depending on mode)
	 * @param x X coordinate of top-left corner of move bar area
	 * @param y Y coordinate of top-left corner of move bar area
	 * @param currentMoveBarLevel Current inactivity level (0 to MOVE_BAR_LEVEL_MAX)
	 */
	function drawBars(dc, x, y, currentMoveBarLevel) {
		var barWidth = getBarWidth();                    // Calculate width for standard bars
		var thisBarWidth;                                // Width of current bar being drawn
		var thisBarColour = 0;                          // Color of current bar being drawn
		var barX = x + mTailWidth;                      // X position of current bar (offset by tail width)
		var moveBarStyle = getPropertyValue("MoveBarStyle"); // Current style setting

		// Iterate through all possible move bar levels (1-based indexing)
		for (var i = 1; i <= ActivityMonitor.MOVE_BAR_LEVEL_MAX; ++i) {

			// First bar is double-width to emphasize the initial inactivity level
			thisBarWidth = (i == 1) ? (2 * barWidth) : barWidth;

			// Determine bar color and visibility based on level and style
			if (i <= currentMoveBarLevel) {
				// Bar represents current inactivity level - always show with theme color
				thisBarColour = gThemeColour;

			} else if (moveBarStyle == 0 /* ALL_SEGMENTS */) {
				// Bar is above current level - show as empty with background color
				thisBarColour = gMeterBackgroundColour;

			} else {
				// FILLED_SEGMENTS mode: don't show empty bars, break early for efficiency
				break;
			}

			// Draw the individual bar segment
			//Sys.println("drawBar " + i + " at x=" + barX);
			drawBar(dc, thisBarColour, barX, y + (mHeight / 2), thisBarWidth);

			// Move to next bar position (current bar width + separator)
			barX += thisBarWidth + mSeparator;
		}
	}

	/**
	 * Calculates the width of standard move bar segments.
	 *
	 * This method determines the optimal width for individual bar segments based on
	 * the available space and the number of bars to be displayed. It accounts for
	 * the double-width first bar and separator spacing.
	 *
	 * Calculation Process:
	 * 1. Determine number of bars to display (MOVE_BAR_LEVEL_MAX - MOVE_BAR_LEVEL_MIN)
	 * 2. Calculate available width (total width - tail width - separator space)
	 * 3. Distribute width among bars (accounting for double-width first bar)
	 *
	 * @return Number The calculated width for standard bar segments
	 */
	function getBarWidth() {
		// Calculate maximum number of bars that can be displayed
		var numBars = ActivityMonitor.MOVE_BAR_LEVEL_MAX - ActivityMonitor.MOVE_BAR_LEVEL_MIN;

		// Calculate available width for bar segments
		// Total width minus tail width minus space needed for separators
		var availableWidth = mCurrentWidth - mTailWidth - ((numBars - 1) * mSeparator);

		// Distribute available width among bars
		// Add 1 to numBars because first bar is double-width (counts as 2 standard bars)
		var barWidth = availableWidth / (numBars + /* First bar is double width */ 1);

		//Sys.println("barWidth " + barWidth);
		return barWidth;
	}

	/**
	 * Draws an individual move bar segment as a hexagonal/chevron shape.
	 *
	 * The bar is drawn as a 6-sided polygon with angled edges that create a
	 * chevron or arrow-like appearance. This design provides visual direction
	 * and makes the move bar more engaging than simple rectangles.
	 *
	 * Shape Diagram:
	 * ----------
	 *  \        \
	 *   x        x + width
	 *  /        /
	 * ----------
	 *
	 * The 'x' marks the bar origin point (center-left edge).
	 * The shape extends leftward by mTailWidth to create the angled tail.
	 *
	 * Polygon Points (clockwise from origin):
	 * 0: Origin point (center-left)
	 * 1: Top-left corner (tail)
	 * 2: Top-right corner
	 * 3: Center-right edge
	 * 4: Bottom-right corner
	 * 5: Bottom-left corner (tail)
	 *
	 * @param dc Drawing context to render the bar
	 * @param colour Color to fill the bar polygon
	 * @param x X coordinate of bar origin (center-left point)
	 * @param y Y coordinate of bar origin (center line)
	 * @param width Width of the bar segment
	 */
	function drawBar(dc, colour, x, y, width) {
		var points = new [6];                           // Array to hold polygon vertices
		var halfHeight = (mHeight / 2);                 // Half height for top/bottom calculations
		width -= 1; // Adjust width for pixel coverage (e.g., width 5 covers pixels 0-4)

		// Define hexagonal polygon points (clockwise from origin)
		points[0] = [x                     , y];                    // Origin: center-left point
		points[1] = [x - mTailWidth        , y - halfHeight];       // Top-left: tail top
		points[2] = [x - mTailWidth + width, y - halfHeight];       // Top-right: bar top
		points[3] = [x              + width, y];                    // Center-right: bar right edge
		points[4] = [x - mTailWidth + width, y + halfHeight];       // Bottom-right: bar bottom
		points[5] = [x - mTailWidth        , y + halfHeight];       // Bottom-left: tail bottom

		// Draw filled polygon with specified color
		dc.setColor(colour, Graphics.COLOR_TRANSPARENT);
		dc.fillPolygon(points);
	}
}