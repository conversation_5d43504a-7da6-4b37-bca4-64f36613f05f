<layout id="WatchFace">
	<drawable class="Background" />

	<drawable id="LeftGoalMeter" class="GoalMeter">
		<param name="side">:left</param>
		<param name="stroke">10</param>
		<param name="height">194</param>
		<param name="separator">2</param>
	</drawable>

	<drawable id="RightGoalMeter" class="GoalMeter">
		<param name="side">:right</param>
		<param name="stroke">10</param>
		<param name="height">194</param>
		<param name="separator">2</param>
	</drawable>

	<drawable id="DataArea" class="DataArea">
		<param name="locX">94</param>
		<param name="width">92</param>
		<param name="row1Y">228</param>
		<param name="row2Y">246</param>
		<param name="goalIconY">223</param>
		<param name="goalIconLeftX">62</param>
		<param name="goalIconRightX">218</param>
	</drawable>

	<drawable id="DataFields" class="DataFields">
		<param name="left">93</param>
		<param name="right">187</param>
		<param name="top">26</param>
		<param name="bottom">51</param>
		<param name="batteryWidth">32</param>
	</drawable>

	<drawable id="Date" class="DateLine">
		<param name="y">83</param>
	</drawable>

	<drawable id="Indicators" class="Indicators">
		<param name="locX">34</param>
		<param name="locY">140</param>
		<param name="spacingY">34</param>
		<param name="batteryWidth">26</param>
	</drawable>

	<drawable id="Time" class="ThickThinTime">
        <param name="adjustY">-4</param>
		<param name="secondsX">197</param>
		<param name="secondsY">171</param>
		<param name="secondsClipY">185</param>
		<param name="secondsClipWidth">31</param>
		<param name="secondsClipHeight">20</param>
	</drawable>

	<drawable id="MoveBar" class="MoveBar">
		<param name="x">55</param>
		<param name="y">194</param>
		<param name="width">133</param>
		<param name="height">11</param>
		<param name="separator">3</param>
	</drawable>
	
</layout>