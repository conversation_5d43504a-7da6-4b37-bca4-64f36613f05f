# Connect IQ Development Workflow

This guide explains the most efficient workflow for developing and debugging Connect IQ applications.

## 🚀 Quick Answer

**Yes, you need to rebuild every time you change MonkeyC code.** But here are the most efficient ways to do it:

## ⚡ **Fastest Development Workflows**

### **Method 1: Quick Development Command (Recommended)**

```powershell
# One-time setup (start simulator)
.\build.ps1 simulator

# Then for each code change:
.\build.ps1 dev                          # Default: venu2
.\build.ps1 dev instinct3amoled45mm      # Specific device
.\build.ps1 dev fenix7                   # Any supported device
```

**What `dev` command does:**
- ✅ Auto-starts simulator if not running
- ✅ Quick build for specified device (default: venu2)
- ✅ Shows clear debug output locations
- ✅ Optimized for fast iteration
- ✅ **NEW: Dynamic device support**

### **Method 2: VS Code + Connect IQ Extension (Best for Debugging)**

```
1. Install "Connect IQ" extension in VS Code
2. Open your project in VS Code
3. Press F5 to build + debug
4. Debug output appears directly in VS Code Debug Console
```

**Advantages:**
- ✅ Build + run in one step (F5)
- ✅ Debug output immediately visible
- ✅ Breakpoints support
- ✅ Auto-reload on save
- ✅ No need for separate terminal commands

### **Method 3: Traditional Build Scripts**

```powershell
# Start simulator once
.\build.ps1 simulator

# For each code change:
.\build.ps1 run-sim          # Build and run in simulator
# OR
.\build.ps1 run instinct3amoled45mm  # Build for specific device
```

## 🔄 **Complete Development Cycle**

### **1. Initial Setup (Once per session)**

```powershell
# Check if you have developer key
.\build.ps1 validate

# Generate key if needed
.\build.ps1 gen-key

# Start simulator
.\build.ps1 simulator

# Check simulator status
.\build.ps1 sim-status
```

### **2. Development Loop (Repeat for each change)**

```powershell
# Edit your MonkeyC code in VS Code or any editor
# Add debug prints: Sys.println("Debug message");

# Quick build and test
.\build.ps1 dev

# Check simulator console for debug output
# (NOT in this terminal!)
```

### **3. Testing on Different Devices**

```powershell
# Test on your target device
.\build.ps1 build instinct3amoled45mm

# Test on multiple devices
.\build.ps1 test
```

### **4. Release Build**

```powershell
# When ready for release
.\build.ps1 publish
```

## 🐛 **Debug Workflow**

### **Adding Debug Prints**

```monkeyc
// In your MonkeyC code
using Toybox.System as Sys;

function onUpdate(dc) {
    Sys.println("onUpdate called");

    var time = Sys.getClockTime();
    Sys.println("Time: " + time.hour + ":" + time.min);

    // Your code here
}
```

### **Seeing Debug Output**

```powershell
# 1. Build with debug
.\build.ps1 dev instinct3amoled45mm

# 2. Check debug output locations (in order of reliability):

# A. Connect IQ Simulator Console (MOST RELIABLE)
#    - Open Connect IQ Simulator window
#    - Look for 'Output', 'Console', or 'Debug' panel
#    - Usually at bottom of simulator window

# B. Log Files (if available)
.\build.ps1 logs                         # Check log files

# C. VS Code Debug Console (if using extension)
#    - Install Connect IQ extension
#    - Press F5 to debug
#    - Debug output appears in VS Code Debug Console

# 3. Get comprehensive help
.\build.ps1 debug-help
```

### **Debug Output Priority**

**🥇 Primary (Most Reliable):** Connect IQ Simulator Console
- Always works when simulator is running
- Real-time output display
- Easy to access

**🥈 Secondary:** VS Code Debug Console
- Requires Connect IQ extension
- Better debugging tools (breakpoints, etc.)
- Integrated development experience

**🥉 Tertiary:** Log Files
- May not exist on all systems
- Useful for post-mortem debugging
- Check with `.\build.ps1 logs`

## ⚡ **Speed Optimization Tips**

### **1. Keep Simulator Running**
```powershell
# Start once, keep running
.\build.ps1 simulator

# Don't restart between builds
# Simulator will auto-reload your app
```

### **2. Use Quick Commands**
```powershell
# Instead of full command
.\build.ps1 run-sim

# Use quick dev command
.\build.ps1 dev
```

### **3. Create PowerShell Aliases**
Add to your PowerShell profile (`$PROFILE`):
```powershell
function ciq-dev { .\build.ps1 dev }
function ciq-build { .\build.ps1 build }
function ciq-sim { .\build.ps1 simulator }
function ciq-debug { .\build.ps1 debug-help }
```

Then use:
```powershell
ciq-dev      # Quick development build
ciq-sim      # Start simulator
ciq-debug    # Show debug help
```

### **4. Multiple Terminal Setup**
- **Terminal 1:** For build commands (`.\build.ps1 dev`)
- **Terminal 2:** For simulator management
- **VS Code:** For code editing
- **Simulator Window:** For testing and debug output

## 🔧 **Development Commands Reference**

| Command | Use Case | Speed |
|---------|----------|-------|
| `.\build.ps1 dev` | Quick development iteration | ⚡⚡⚡ Fastest |
| `.\build.ps1 run-sim` | Build and run in simulator | ⚡⚡ Fast |
| `.\build.ps1 build` | Build only (no run) | ⚡⚡ Fast |
| `.\build.ps1 run device` | Test on specific device | ⚡ Medium |
| `.\build.ps1 test` | Test multiple devices | 🐌 Slow |

## 🎯 **Recommended Workflow for Different Scenarios**

### **Quick Feature Development**
```powershell
# 1. Start simulator
.\build.ps1 simulator

# 2. Code + test loop
# Edit code → .\build.ps1 dev → Check simulator → Repeat
```

### **Debugging Issues**
```powershell
# 1. Add debug prints to your code
Sys.println("Debug: variable = " + variable);

# 2. Build and check output
.\build.ps1 dev

# 3. Check simulator console for debug output
# 4. Repeat until issue found
```

### **Multi-Device Testing**
```powershell
# 1. Develop with simulator
.\build.ps1 dev

# 2. Test on target device
.\build.ps1 run instinct3amoled45mm

# 3. Test compatibility
.\build.ps1 test
```

### **Release Preparation**
```powershell
# 1. Final testing
.\build.ps1 test

# 2. Clean build
.\build.ps1 clean

# 3. Release build
.\build.ps1 publish
```

## 🚨 **Common Development Issues**

### **Issue: "I don't see my debug output"**
**Solution:** Debug output appears in simulator console, not terminal.
```powershell
.\build.ps1 debug-help  # Get detailed help
```

### **Issue: "Simulator not connecting"**
**Solution:** Start simulator first.
```powershell
.\build.ps1 sim-status  # Check status
.\build.ps1 simulator   # Start if needed
```

### **Issue: "Build is slow"**
**Solution:** Use quick development command.
```powershell
.\build.ps1 dev  # Faster than full build+run
```

### **Issue: "App not updating in simulator"**
**Solution:** Make sure you're rebuilding after code changes.
```powershell
# Always rebuild after code changes
.\build.ps1 dev
```

## 📚 **Best Practices**

1. **Always rebuild after code changes** - MonkeyC is compiled, not interpreted
2. **Keep simulator running** - Faster than restarting each time
3. **Use tagged debug messages** - `Sys.println("[VIEW] message")`
4. **Test on target device regularly** - Simulator behavior may differ
5. **Use VS Code for complex debugging** - Better debugging tools
6. **Clean build before release** - Ensure no artifacts

## 🎉 **Summary**

**For fastest development:**
1. Use `.\build.ps1 dev` for quick iterations
2. Keep simulator running
3. Check debug output in simulator console
4. Use VS Code + F5 for advanced debugging

**Remember:** You must rebuild every time you change MonkeyC code, but these workflows make it as fast as possible!
