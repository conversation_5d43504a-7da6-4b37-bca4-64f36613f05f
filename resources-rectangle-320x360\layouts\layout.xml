<resources>
	<layout id="WatchFace">
		<drawable class="Background" />

		<drawable id="LeftGoalMeter" class="GoalMeter">
			<param name="side">:left</param>
			<param name="stroke">12</param>
			<param name="height">360</param>
			<param name="separator">3</param>
		</drawable>

		<drawable id="RightGoalMeter" class="GoalMeter">
			<param name="side">:right</param>
			<param name="stroke">12</param>
			<param name="height">360</param>
			<param name="separator">3</param>
		</drawable>

		<drawable id="DataArea" class="DataArea">
			<param name="locX">66</param>
			<param name="width">188</param>
			<param name="row1Y">318</param>
			<param name="row2Y">344</param>
			<param name="goalIconY">312</param>
			<param name="goalIconLeftX">22</param>
			<param name="goalIconRightX">298</param>
		</drawable>

		<drawable id="DataFields" class="DataFields">
			<param name="left">80</param>
			<param name="right">240</param>
			<param name="top">25</param>
			<param name="bottom">58</param>
			<param name="batteryWidth">42</param>
		</drawable>

		<drawable id="Date" class="DateLine">
			<param name="y">97</param>
		</drawable>

		<drawable id="Indicators" class="Indicators">
			<param name="locX">160</param>
			<param name="locY">278</param>
			<param name="spacingX">48</param>
			<param name="batteryWidth">38</param>
		</drawable>

		<drawable id="Time" class="ThickThinTime">
			<param name="adjustY">-16</param>
			<param name="secondsX">232</param>
			<param name="secondsY">205</param>
			<param name="amPmOffset">5</param>
		</drawable>

		<drawable id="MoveBar" class="MoveBar">
			<param name="x">46</param>
			<param name="y">234</param>
			<param name="width">171</param>
			<param name="height">13</param>
			<param name="separator">3</param>
		</drawable>
	</layout>

	<!-- Shown in low power mode -->
	<layout id="AlwaysOn">
		<drawable id="AlwaysOnDisplay" class="AlwaysOnDisplay">
			<param name="burnInYOffsets">[-56, -28, 28, 56]</param>
			<param name="adjustY">-2</param>
			<param name="timeY">154</param>
			<param name="lineY">202</param>
			<!-- Allow room for Chinese date -->	
			<param name="lineWidth">268</param>
			<!-- param name="lineStroke">2</param -->	
			<param name="dataY">228</param>
			<param name="dataLeft">46</param>
		</drawable>
	</layout>
</resources>
