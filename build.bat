@echo off
REM Build script for Madre Face Connect IQ Project (Windows)
setlocal enabledelayedexpansion

REM Project configuration
set PROJECT_NAME=madreface
set APP_NAME=madre-face
set DEVELOPER_KEY=developer_key.der
set DEFAULT_PRODUCT=instinct3amoled45mm

REM Connect IQ SDK path (update this to match your installation)
set SDK_PATH=C:\Users\<USER>\AppData\Roaming\Garmin\ConnectIQ\Sdks\connectiq-sdk-win-8.1.1-2025-03-27-66dae750f
set MONKEYC="%SDK_PATH%\bin\monkeyc.bat"
set MONKEYDO="%SDK_PATH%\bin\monkeydo.bat"
set SIMULATOR="%SDK_PATH%\bin\simulator.exe"

REM Build directories
set BIN_DIR=bin
set OUTPUT_PRG=%BIN_DIR%\%PROJECT_NAME%.prg
set OUTPUT_IQ=%BIN_DIR%\%APP_NAME%.iq
set SETTINGS_FILE=%BIN_DIR%\%PROJECT_NAME%-settings.json

REM Compiler flags
set DEBUG_FLAGS=-w --debug-log-level=3
set RELEASE_FLAGS=-e -r -w -O=3z

REM Get command and product from arguments
set COMMAND=%1
set PRODUCT=%2
if "%PRODUCT%"=="" set PRODUCT=%DEFAULT_PRODUCT%

REM Create bin directory if it doesn't exist
if not exist "%BIN_DIR%" mkdir "%BIN_DIR%"

REM Command dispatcher
if "%COMMAND%"=="build" goto :build
if "%COMMAND%"=="run" goto :run
if "%COMMAND%"=="simulator" goto :simulator
if "%COMMAND%"=="publish" goto :publish
if "%COMMAND%"=="release" goto :release
if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="clean-all" goto :clean_all
if "%COMMAND%"=="test" goto :test_devices
if "%COMMAND%"=="info" goto :info
if "%COMMAND%"=="validate" goto :validate
if "%COMMAND%"=="run-sim" goto :run_sim
if "%COMMAND%"=="build-sim" goto :build_sim
if "%COMMAND%"=="start-sim" goto :start_sim
if "%COMMAND%"=="help" goto :help
if "%COMMAND%"=="" goto :help

echo Unknown command: %COMMAND%
goto :help

:build
echo Building %PROJECT_NAME% for %PRODUCT%...
%MONKEYC% -d %PRODUCT% -f monkey.jungle -o %OUTPUT_PRG% -y %DEVELOPER_KEY% %DEBUG_FLAGS%
if %ERRORLEVEL% equ 0 (
    echo Build completed: %OUTPUT_PRG%
) else (
    echo Build failed!
    exit /b 1
)
goto :end

:run
echo Building and running %PROJECT_NAME% for %PRODUCT%...
call :build
if %ERRORLEVEL% equ 0 (
    echo Running in simulator...
    %MONKEYDO% %OUTPUT_PRG% %PRODUCT% -a "%SETTINGS_FILE%;GARMIN/Settings/%PROJECT_NAME%-settings.json"
)
goto :end

:simulator
echo Launching Connect IQ Simulator...
REM Check if simulator is already running
tasklist /FI "IMAGENAME eq simulator.exe" 2>NUL | find /I /N "simulator.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Simulator is already running.
) else (
    echo Starting simulator...
    start "" %SIMULATOR%
    timeout /t 3 /nobreak >nul
    echo Simulator started. You can now run apps with 'build.bat run [device]'
)
goto :end

:build_sim
echo Building for simulator (venu2)...
call :build venu2
goto :end

:run_sim
echo Building and running in simulator...
call :build venu2
if %ERRORLEVEL% equ 0 (
    echo Starting simulator if not running...
    call :start_sim
    timeout /t 2 /nobreak >nul
    echo Running app in simulator...
    %MONKEYDO% %OUTPUT_PRG% venu2 -a "%SETTINGS_FILE%;GARMIN/Settings/%PROJECT_NAME%-settings.json"
)
goto :end

:start_sim
REM Start simulator without showing message (internal function)
tasklist /FI "IMAGENAME eq simulator.exe" 2>NUL | find /I /N "simulator.exe">NUL
if not "%ERRORLEVEL%"=="0" (
    start "" %SIMULATOR%
    timeout /t 3 /nobreak >nul
)
goto :end

:publish
echo Building release version...
call :clean
%MONKEYC% -f monkey.jungle -o %BIN_DIR%\ -p %APP_NAME%.iq -y %DEVELOPER_KEY% %RELEASE_FLAGS%
if %ERRORLEVEL% equ 0 (
    echo Release build completed: %OUTPUT_IQ%
) else (
    echo Release build failed!
    exit /b 1
)
goto :end

:release
call :clean
call :publish
goto :end

:clean
echo Cleaning build artifacts...
if exist "%BIN_DIR%\*.prg" del /Q "%BIN_DIR%\*.prg"
if exist "%BIN_DIR%\*.prg.debug.xml" del /Q "%BIN_DIR%\*.prg.debug.xml"
if exist "%BIN_DIR%\*.iq" del /Q "%BIN_DIR%\*.iq"
echo Clean completed.
goto :end

:clean_all
echo Cleaning all generated files...
if exist "%BIN_DIR%" rmdir /S /Q "%BIN_DIR%" 2>nul
if exist "gen" rmdir /S /Q "gen" 2>nul
echo Deep clean completed.
goto :end

:test_devices
echo Testing build for common devices...
call :build instinct3amoled45mm
call :build venu2
call :build round-240x240
call :build fenix7
echo All test builds completed!
goto :end

:info
echo Project: %PROJECT_NAME%
echo Target Device: %PRODUCT%
echo SDK Path: %SDK_PATH%
echo Developer Key: %DEVELOPER_KEY%
echo Output PRG: %OUTPUT_PRG%
echo Output IQ: %OUTPUT_IQ%
goto :end

:validate
if not exist "%DEVELOPER_KEY%" (
    echo Error: Developer key not found: %DEVELOPER_KEY%
    exit /b 1
)
if not exist "monkey.jungle" (
    echo Error: monkey.jungle not found
    exit /b 1
)
if not exist "manifest.xml" (
    echo Error: manifest.xml not found
    exit /b 1
)
if not exist "source" (
    echo Error: source directory not found
    exit /b 1
)
echo Project structure validated successfully.
goto :end

:help
echo Madre Face Connect IQ Build System (Windows)
echo.
echo Usage: build.bat [command] [device]
echo.
echo Available commands:
echo   build [device]     - Build for development (default: %DEFAULT_PRODUCT%)
echo   run [device]       - Build and run in simulator
echo   build-sim          - Build for simulator (venu2)
echo   run-sim            - Build and run in simulator
echo   simulator          - Launch Connect IQ simulator
echo   publish            - Build release version (.iq file)
echo   release            - Clean and build release
echo   clean              - Clean build artifacts
echo   clean-all          - Clean all generated files
echo   test               - Build for common test devices
echo   info               - Show project information
echo   validate           - Validate project structure
echo   help               - Show this help
echo.
echo Examples:
echo   build.bat build                              # Build for default device
echo   build.bat build venu2                       # Build for Venu 2
echo   build.bat run round-240x240                  # Build and run simulator
echo   build.bat publish                            # Build release .iq file
echo.
echo Common devices:
echo   instinct3amoled45mm, instinct3amoled50mm, venu2, venu3
echo   round-240x240 (simulator), fenix7, fr965, epix2
goto :end

:end
endlocal
