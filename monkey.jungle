project.manifest = manifest.xml

# Exclude other source directories (default behaviour is to include).
base.sourcePath = source

base.excludeAnnotations = unbuffered;object_store;double_line_date;horizontal_indicators

# #122: fr245/fr245m are CIQ 3.x, but do not support floors climbed.
# fr245.resourcePath = $(fr245.resourcePath);resources-no_floors
# fr245m.resourcePath = $(fr245m.resourcePath);resources-no_floors

# # Some newer watches also do not support floors climbed.
# vivoactive5.resourcePath = $(vivoactive5.resourcePath);resources-no_floors
# venusq2.resourcePath = $(venusq2.resourcePath);resources-no_floors
# venusq2m.resourcePath = $(venusq2m.resourcePath);resources-no_floors

# Smaller round watches use small time font.
round-218x218.resourcePath = $(round-218x218.resourcePath);resources-small-time

# Flat tyre watches use small time font and small icons.
# Ditto smallest round watches.
semiround-215x180.resourcePath = $(semiround-215x180.resourcePath);resources-small-time;resources-small-icons
round-208x208.resourcePath = $(round-208x208.resourcePath);resources-small-time;resources-small-icons

# Rectangle watches generally same as flat tyre, but have some overrides.
rectangle.resourcePath = $(rectangle.resourcePath);resources-small-time;resources-small-icons

# CIQ 1.x watches only have 16 colours, so cannot support all themes.
# No Floors or Active Minutes support, so default to showing Battery in right meter, and Calories in the centre field.
# If a watch also has no HR support, show Distance in left field.
# No BufferedBitmap support, so exclude buffer-specific variables/functions.
# No clip support, so goal meter mask required for round/semiround watches.

# d2bravo.resourcePath = $(d2bravo.resourcePath);resources-round-218x218-ciq_1.x;resources-ciq_1.x-no_hr
d2bravo.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# d2bravo_titanium.resourcePath = $(d2bravo_titanium.resourcePath);resources-round-218x218-ciq_1.x;resources-ciq_1.x
d2bravo_titanium.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# fenix3.resourcePath = $(fenix3.resourcePath);resources-round-218x218-ciq_1.x;resources-ciq_1.x-no_hr
fenix3.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# fenix3_hr.resourcePath = $(fenix3_hr.resourcePath);resources-round-218x218-ciq_1.x;resources-ciq_1.x
fenix3_hr.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# fr230.resourcePath = $(fr230.resourcePath);resources-semiround-215x180-ciq_1.x;resources-ciq_1.x-no_hr
fr230.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# fr235.resourcePath = $(fr235.resourcePath);resources-semiround-215x180-ciq_1.x;resources-ciq_1.x
fr235.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# #124: fr45's settings.xml repeats whole of resources-ciq_1.x, then overrides, as settings were not correct inherited.
#       Exclude background methods as memory is very limited (extend to other devices as necessary).
fr45.excludeAnnotations = buffered;properties_and_storage;background_method;double_line_date;horizontal_indicators

# fr630.resourcePath = $(fr630.resourcePath);resources-semiround-215x180-ciq_1.x;resources-ciq_1.x-no_hr
fr630.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# fr920xt.resourcePath = $(fr920xt.resourcePath);resources-ciq_1.x-no_hr
fr920xt.excludeAnnotations = buffered;properties_and_storage;single_line_date;horizontal_indicators

# garminswim2 has same limitations as fr45 (CIQ 1.x, low memory, low colours).
garminswim2.excludeAnnotations = buffered;properties_and_storage;background_method;double_line_date;horizontal_indicators

# Reverse order of resources so that 64-colour themes are permitted.
# #25: Repeat ciq_1.x-no_hr properties within resources-vivoactive, as properties were not correctly inherited.
#vivoactive.resourcePath = resources-ciq_1.x-no_hr;$(vivoactive.resourcePath)
vivoactive.excludeAnnotations = buffered;properties_and_storage;double_line_date;horizontal_indicators

# #21: Treat fr735xt as CIQ 1.x i.e. unbuffered drawing, due to insufficient memory.
# Continue to use fr735xt-specific properties and settings, however, as does support active minutes.
# fr735xt.resourcePath = $(fr735xt.resourcePath);resources-semiround-215x180-ciq_1.x
fr735xt.excludeAnnotations = buffered;object_store;double_line_date;horizontal_indicators

vivoactive_hr.excludeAnnotations = unbuffered;object_store;double_line_date;vertical_indicators

# #144: Always on display.
venu.sourcePath = $(venu.sourcePath);always-on-source
venu2.sourcePath = $(venu2.sourcePath);always-on-source
venu2s.sourcePath = $(venu2s.sourcePath);always-on-source
venud.sourcePath = $(venud.sourcePath);always-on-source
venusq.sourcePath = $(venusq.sourcePath);always-on-source
venusqm.sourcePath = $(venusqm.sourcePath);always-on-source
d2air.sourcePath = $(d2air.sourcePath);always-on-source
fr265s.sourcePath = $(fr265s.sourcePath);always-on-source
approachs7042mm.sourcePath = $(approachs7042mm.sourcePath);always-on-source
descentmk343mm.sourcePath = $(descentmk343mm.sourcePath);always-on-source
epix2pro42mm.sourcePath = $(epix2pro42mm.sourcePath);always-on-source
marq2.sourcePath = $(marq2.sourcePath);always-on-source
marq2aviator.sourcePath = $(marq2aviator.sourcePath);always-on-source
venu3s.sourcePath = $(venu3s.sourcePath);always-on-source
vivoactive5.sourcePath = $(vivoactive5.sourcePath);always-on-source
d2airx10.sourcePath = $(d2airx10.sourcePath);always-on-source
d2mach1.sourcePath = $(d2mach1.sourcePath);always-on-source
epix2.sourcePath = $(epix2.sourcePath);always-on-source
epix2pro47mm.sourcePath = $(epix2pro47mm.sourcePath);always-on-source
fr265.sourcePath = $(fr265.sourcePath);always-on-source
venu2plus.sourcePath = $(venu2plus.sourcePath);always-on-source
venusq2.sourcePath = $(venusq2.sourcePath);always-on-source
venusq2m.sourcePath = $(venusq2m.sourcePath);always-on-source
approachs7047mm.sourcePath = $(approachs7047mm.sourcePath);always-on-source
descentmk351mm.sourcePath = $(descentmk351mm.sourcePath);always-on-source
epix2pro51mm.sourcePath = $(epix2pro51mm.sourcePath);always-on-source
fr965.sourcePath = $(fr965.sourcePath);always-on-source
venu3.sourcePath = $(venu3.sourcePath);always-on-source
fenix843mm.sourcePath = $(fenix843mm.sourcePath);always-on-source
fenix847mm.sourcePath = $(fenix847mm.sourcePath);always-on-source
fenixe.sourcePath = $(fenixe.sourcePath);always-on-source
fr165.sourcePath = $(fr165.sourcePath);always-on-source
fr165m.sourcePath = $(fr165m.sourcePath);always-on-source
instinct3amoled45mm.sourcePath = $(instinct3amoled45mm.sourcePath);always-on-source
instinct3amoled50mm.sourcePath = $(instinct3amoled50mm.sourcePath);always-on-source
vivoactive6.sourcePath = $(vivoactive6.sourcePath);always-on-source

rectangle-320x360.excludeAnnotations = unbuffered;object_store;double_line_date;vertical_indicators
