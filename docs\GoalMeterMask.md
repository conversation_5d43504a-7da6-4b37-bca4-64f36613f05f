# GoalMeterMask Documentation

## Overview

The `GoalMeterMask` class is a specialized drawable component designed to provide visual masking for goal meters on round watch faces. It creates a circular overlay that hides rectangular goal meter segments in the center area, ensuring a clean circular aesthetic.

## Purpose and Context

### Problem Statement
On round watch faces, goal meters are rendered as rectangular segments along the left and right edges. Without proper masking, these rectangular segments would be visible in the center area of the screen, breaking the circular design aesthetic and creating visual artifacts.

### Solution
The `GoalMeterMask` provides a circular mask filled with the background color that covers the center area of the screen, effectively "cutting out" any goal meter rectangles that would otherwise be visible in the center.

## Architecture

### Class Hierarchy
```
Ui.Drawable
    └── GoalMeterMask
```

### Key Components
- **Circular Mask**: A filled circle drawn in the center of the screen
- **Background Color Fill**: Uses the watch face background color for seamless integration
- **Radius Calculation**: Dynamically calculated based on goal meter stroke width

## Usage Patterns

### Unbuffered Drawing Mode
The `GoalMeterMask` is primarily used in unbuffered drawing mode where:
1. Goal meters are drawn as rectangular segments
2. The mask is applied as an overlay to hide center portions
3. Creates the visual appearance of curved arc segments

### Buffered Drawing Mode
In buffered drawing mode, the mask is typically not needed because:
- Circular masking is applied directly to the goal meter buffers
- No center area cleanup is required
- More memory-efficient approach

## Technical Implementation

### Constructor Parameters
```monkey-c
typedef GoalMeterMaskParams as {
    :stroke as Number    // Goal meter stroke width
};
```

### Key Methods

#### `initialize(params)`
- **Purpose**: Initializes the mask with stroke width configuration
- **Parameters**: `GoalMeterMaskParams` containing stroke width
- **Behavior**: Stores stroke width for radius calculation

#### `draw(dc)`
- **Purpose**: Renders the circular mask overlay
- **Parameters**: Graphics drawing context
- **Behavior**: Draws filled circle with calculated radius

### Radius Calculation
```
radius = (screenWidth / 2) - strokeWidth
```

This calculation ensures:
- The mask covers the center area completely
- Goal meters remain visible as arc segments around the edge
- Proper visual separation between center and goal meter areas

## Visual Design

### Appearance
- **Shape**: Perfect circle centered on screen
- **Color**: Matches watch face background color exactly
- **Size**: Covers center area while preserving goal meter visibility
- **Effect**: Creates clean circular boundary for goal meter arcs

### Integration
The mask integrates seamlessly with:
- Watch face background
- Goal meter positioning
- Theme color schemes
- Different screen sizes

## Performance Characteristics

### Rendering Performance
- **Operation**: Single `fillCircle()` call per frame
- **Complexity**: O(1) - constant time operation
- **Memory**: No additional memory allocation during rendering

### Memory Usage
- **Footprint**: Minimal - only stores single stroke width value
- **Buffers**: No bitmap buffers required
- **Allocation**: Static allocation during initialization

## Configuration

### Stroke Width
The stroke width parameter must match the goal meter stroke width to ensure:
- Proper radius calculation
- Correct visual alignment
- Seamless integration with goal meter appearance

### Color Coordination
The mask automatically uses the global background color (`gBackgroundColour`) to:
- Match the watch face background exactly
- Adapt to theme changes automatically
- Maintain visual consistency

## Use Cases

### Primary Use Case: Round Watch Faces
- **Scenario**: Unbuffered goal meter rendering on circular screens
- **Benefit**: Clean circular aesthetic without center artifacts
- **Implementation**: Applied as overlay after goal meter drawing

### Secondary Use Case: Visual Cleanup
- **Scenario**: Hiding unwanted rectangular segments in center area
- **Benefit**: Professional appearance on round devices
- **Implementation**: Single draw operation for complete coverage

## Best Practices

### When to Use
✅ **Recommended for:**
- Round watch faces with unbuffered goal meters
- Situations requiring center area cleanup
- Maintaining circular design consistency

❌ **Not recommended for:**
- Rectangular watch faces (unnecessary)
- Buffered drawing mode (handled internally)
- Performance-critical scenarios where every draw call matters

### Implementation Guidelines
1. **Stroke Width Matching**: Ensure stroke width matches goal meter configuration
2. **Drawing Order**: Apply mask after goal meters are drawn
3. **Color Consistency**: Rely on global background color for automatic theme support
4. **Performance**: Use only when necessary (round screens, unbuffered mode)

## Integration Example

```monkey-c
// In layout.xml
<drawable id="GoalMeterMask" class="GoalMeterMask">
    <param name="stroke">8</param>  <!-- Match goal meter stroke -->
</drawable>

// Drawing order in view
1. Draw background
2. Draw goal meters (unbuffered)
3. Draw goal meter mask (covers center)
4. Draw other UI elements
```

## Troubleshooting

### Common Issues

#### Mask Too Small/Large
- **Symptom**: Goal meter rectangles visible in center or goal meters cut off
- **Cause**: Incorrect stroke width parameter
- **Solution**: Match stroke width exactly to goal meter configuration

#### Color Mismatch
- **Symptom**: Visible circle outline or color difference
- **Cause**: Background color inconsistency
- **Solution**: Ensure `gBackgroundColour` is properly set and consistent

#### Performance Impact
- **Symptom**: Frame rate drops on round devices
- **Cause**: Unnecessary mask usage in buffered mode
- **Solution**: Use conditional compilation or runtime checks to apply only when needed

## Future Considerations

### Potential Enhancements
- **Adaptive Sizing**: Dynamic radius adjustment based on screen size
- **Shape Variants**: Support for different mask shapes (oval, rounded rectangle)
- **Performance Optimization**: Conditional rendering based on drawing mode

### Maintenance Notes
- Monitor performance impact on lower-end devices
- Consider integration with buffered drawing optimizations
- Evaluate necessity as SDK capabilities evolve

## Technical Deep Dive

### Geometric Calculations

The mask radius calculation is critical for proper visual appearance:

```
Given:
- screenWidth: Total width of the watch screen
- strokeWidth: Width of goal meter bars

Calculation:
- screenRadius = screenWidth / 2
- maskRadius = screenRadius - strokeWidth
- centerX = screenWidth / 2
- centerY = screenHeight / 2
```

### Drawing Context Integration

The mask integrates with the Garmin Connect IQ graphics system:

```monkey-c
// Color setup
dc.setColor(gBackgroundColour, Gfx.COLOR_TRANSPARENT);

// Circle rendering
dc.fillCircle(centerX, centerY, radius);
```

### Memory and Performance Analysis

#### Memory Footprint
- **Instance Variables**: 1 Number (mStroke) ≈ 4 bytes
- **Method Overhead**: Minimal virtual method table entries
- **Total**: < 50 bytes per instance

#### Rendering Performance
- **fillCircle() Complexity**: O(r²) where r is radius
- **Typical Execution Time**: < 1ms on modern devices
- **Frame Impact**: Negligible for single mask per frame

### Screen Shape Compatibility

#### Round Screens
- **Primary Use Case**: Essential for clean appearance
- **Visual Impact**: High - prevents center artifacts
- **Performance**: Single draw operation

#### Rectangular Screens
- **Use Case**: Generally unnecessary
- **Visual Impact**: None (no center area to mask)
- **Performance**: Wasted draw operation

#### Semi-Round Screens
- **Use Case**: May be beneficial depending on design
- **Visual Impact**: Moderate - depends on goal meter positioning
- **Performance**: Consider cost vs. benefit

## Code Examples

### Basic Implementation
```monkey-c
class MyWatchFace extends WatchUi.WatchFace {
    private var mGoalMeterMask;

    function initialize() {
        WatchFace.initialize();

        // Initialize mask with matching stroke width
        var params = {
            :stroke => 8  // Match goal meter stroke
        };
        mGoalMeterMask = new GoalMeterMask(params);
    }

    function onUpdate(dc) {
        // 1. Draw background
        dc.setColor(Graphics.COLOR_BLACK, Graphics.COLOR_WHITE);
        dc.clear();

        // 2. Draw goal meters (unbuffered)
        drawGoalMeters(dc);

        // 3. Apply mask (round screens only)
        if (System.getDeviceSettings().screenShape != System.SCREEN_SHAPE_RECTANGLE) {
            mGoalMeterMask.draw(dc);
        }

        // 4. Draw other UI elements
        drawTimeAndData(dc);
    }
}
```

### Conditional Usage
```monkey-c
// Only use mask when necessary
(:unbuffered)
function drawGoalMeterMask(dc) {
    if (System.getDeviceSettings().screenShape != System.SCREEN_SHAPE_RECTANGLE) {
        mGoalMeterMask.draw(dc);
    }
}

(:buffered)
function drawGoalMeterMask(dc) {
    // No mask needed in buffered mode
    // Masking handled in goal meter buffers
}
```

## Testing and Validation

### Visual Testing
1. **Center Area**: Verify no goal meter rectangles visible in center
2. **Edge Alignment**: Confirm goal meters appear as proper arcs
3. **Color Matching**: Ensure mask color matches background exactly
4. **Theme Changes**: Test with different color themes

### Performance Testing
1. **Frame Rate**: Monitor FPS with and without mask
2. **Memory Usage**: Check for memory leaks during theme changes
3. **Device Compatibility**: Test on various round watch models

### Edge Cases
1. **Very Small Screens**: Ensure mask doesn't hide entire goal meters
2. **Very Large Stroke**: Verify mask still covers center adequately
3. **Color Transitions**: Test during theme or background color changes

## Related Components

### GoalMeter Class
- **Relationship**: Mask is applied after goal meter rendering
- **Coordination**: Stroke width must match between components
- **Dependencies**: Mask relies on goal meter positioning

### Watch Face Layout
- **Integration**: Mask is typically defined in layout.xml
- **Ordering**: Must be drawn after goal meters in z-order
- **Parameters**: Stroke width should match goal meter configuration

### Theme System
- **Color Coordination**: Automatically uses global background color
- **Dynamic Updates**: Responds to theme changes without reconfiguration
- **Consistency**: Maintains visual harmony with overall design
