# .gitignore for Garmin Connect IQ Projects

This document explains the `.gitignore` configuration for this Connect IQ project and why certain files should never be committed to version control.

## 🔒 Security Critical Files (NEVER COMMIT!)

### Developer Keys
```
developer_key.der
developer_key.pem
developer_key
*.der
*.pem
*_key.*
*key.*
test_key.*
my_key.*
backup_key.*
*_backup.*
```

**Why these are ignored:**
- **Security Risk**: Private keys can be used to sign and distribute malicious apps under your developer identity
- **Personal**: Each developer should have their own unique signing key
- **Irreversible**: Once a private key is committed to git, it's permanently in the history even if deleted later

### What to do instead:
1. Generate keys locally using the provided scripts
2. Keep keys secure and backed up privately
3. Never share keys via email, chat, or version control
4. Each team member generates their own keys for development

## 🏗️ Build Artifacts (Auto-generated)

### Build Directories
```
build/
bin/
gen/
```

### Compiled Files
```
*.prg
*.prg.debug.xml
*.iq
*.debug.xml
*.symbols.xml
log.zip
*.mir
external-mir/
internal-mir/
mir/
optimized/
```

**Why these are ignored:**
- **Auto-generated**: These files are created by the build process
- **Platform-specific**: May differ between development environments
- **Large files**: Can bloat repository size unnecessarily
- **Temporary**: Should be regenerated for each build

## 💻 Development Environment Files

### IDE Configuration
```
.vscode/settings.json
.vscode/launch.json.bak
.idea/
*.sublime-workspace
```

**Why these are ignored:**
- **Personal preferences**: Each developer may have different IDE settings
- **Environment-specific**: Paths and configurations may differ between machines
- **Frequently changing**: IDE settings change often and create noise in commits

### Operating System Files
```
# macOS
.DS_Store
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
.directory
.Trash-*
```

## 🔧 Configuration Files (May Contain Secrets)

```
.env
.env.local
config.json
settings.json
secrets.json
```

**Why these are ignored:**
- **API Keys**: May contain sensitive API keys or tokens
- **Environment-specific**: Different settings for dev/staging/production
- **Personal data**: May contain personal paths or preferences

## ✅ Files That SHOULD Be Committed

### Source Code
- `source/*.mc` - MonkeyC source files
- `resources/` - App resources (layouts, drawables, strings)
- `always-on-source/` - Always-on display source code

### Project Configuration
- `manifest.xml` - App manifest and metadata
- `monkey.jungle` - Build configuration
- `.vscode/tasks.json` - Shared build tasks (if using VS Code)
- `.vscode/launch.json` - Shared debug configurations (if using VS Code)

### Build Scripts
- `build.bat` - Windows batch build script
- `build.ps1` - PowerShell build script
- `Makefile` - Make build script
- `generate-key.sh` - Key generation scripts (without actual keys)

### Documentation
- `README.md` - Project documentation
- `BUILD.md` - Build instructions
- `docs/` - Additional documentation

## 🚀 Best Practices

### For New Developers
1. **Clone the repository**
2. **Generate your own developer key**: `.\build.ps1 gen-key`
3. **Build the project**: `.\build.ps1 build`
4. **Never commit your keys**: They're already ignored

### For Team Collaboration
1. **Share build scripts**: Commit build automation
2. **Share project configuration**: Commit manifest.xml and monkey.jungle
3. **Share documentation**: Keep README and docs updated
4. **Don't share personal files**: Let .gitignore handle this

### Security Checklist
- [ ] Developer keys are generated locally
- [ ] Keys are not in version control
- [ ] Build artifacts are ignored
- [ ] Environment files are ignored
- [ ] Team members have their own keys

## 🔍 Checking What's Ignored

To see what files are being ignored:
```bash
# See all ignored files
git status --ignored

# Check if a specific file is ignored
git check-ignore developer_key.der

# See what would be added (dry run)
git add --dry-run .
```

## 🆘 If You Accidentally Committed Keys

If you accidentally committed private keys:

1. **Immediately revoke the keys** (if possible)
2. **Remove from git history**:
   ```bash
   git filter-branch --force --index-filter \
   'git rm --cached --ignore-unmatch developer_key.der' \
   --prune-empty --tag-name-filter cat -- --all
   ```
3. **Generate new keys**
4. **Force push** (if working with a team, coordinate this)
5. **Notify team members** to re-clone the repository

## 📝 Customizing .gitignore

To add project-specific ignores:
1. Edit `.gitignore` in the project root
2. Test with `git status` to ensure files are ignored
3. Commit the updated `.gitignore`

To ignore files globally (for all your projects):
```bash
git config --global core.excludesfile ~/.gitignore_global
```

## 📚 References

- [Git Documentation - gitignore](https://git-scm.com/docs/gitignore)
- [GitHub's gitignore templates](https://github.com/github/gitignore)
- [Garmin Connect IQ Developer Guide](https://developer.garmin.com/connect-iq/)
