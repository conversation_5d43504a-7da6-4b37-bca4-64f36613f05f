# PowerShell Script to Generate Garmin Connect IQ Developer Key
# Based on: https://medium.com/@bgallois/garmin-app-development-without-the-visual-studio-code-85628e4b6ba1

param(
    [Parameter(Position=0)]
    [string]$KeyName = "developer_key",

    [Parameter()]
    [switch]$Force,

    [Parameter()]
    [switch]$Help
)

# Configuration
$PemFile = "$KeyName.pem"
$DerFile = "$KeyName.der"
$KeySize = 4096

function Show-Help {
    Write-Host "Garmin Connect IQ Developer Key Generator" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\generate-key.ps1 [key_name] [-Force] [-Help]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Green
    Write-Host "  key_name       - Base name for key files (default: developer_key)"
    Write-Host "  -Force         - Overwrite existing key files"
    Write-Host "  -Help          - Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\generate-key.ps1                    # Generate developer_key.pem and developer_key.der"
    Write-Host "  .\generate-key.ps1 my_key             # Generate my_key.pem and my_key.der"
    Write-Host "  .\generate-key.ps1 -Force             # Overwrite existing keys"
    Write-Host ""
    Write-Host "Requirements:" -ForegroundColor Magenta
    Write-Host "  - OpenSSL must be installed and available in PATH"
    Write-Host "  - Or use Git Bash (includes OpenSSL)"
    Write-Host "  - Or use WSL (Windows Subsystem for Linux)"
    Write-Host ""
    Write-Host "Output files:" -ForegroundColor Green
    Write-Host "  $PemFile    - Private key in PEM format (intermediate)"
    Write-Host "  $DerFile    - Private key in DER format (for Garmin SDK)"
}

function Test-OpenSSL {
    try {
        $null = & openssl version 2>$null
        return $true
    } catch {
        return $false
    }
}

function Test-GitBash {
    $gitBashPaths = @(
        "C:\Program Files\Git\bin\openssl.exe",
        "C:\Program Files (x86)\Git\bin\openssl.exe",
        "$env:ProgramFiles\Git\bin\openssl.exe",
        "${env:ProgramFiles(x86)}\Git\bin\openssl.exe"
    )

    foreach ($path in $gitBashPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    return $null
}

function Find-OpenSSL {
    Write-Host "Checking for OpenSSL..." -ForegroundColor Yellow

    # Check if openssl is in PATH
    if (Test-OpenSSL) {
        Write-Host "✓ OpenSSL found in PATH" -ForegroundColor Green
        return "openssl"
    }

    # Check for Git Bash OpenSSL
    $gitOpenSSL = Test-GitBash
    if ($gitOpenSSL) {
        Write-Host "✓ OpenSSL found in Git installation: $gitOpenSSL" -ForegroundColor Green
        return $gitOpenSSL
    }

    # Check for WSL
    try {
        $null = & wsl --list --quiet 2>$null
        Write-Host "✓ WSL detected. You can use: wsl openssl" -ForegroundColor Green
        Write-Host "  Run this script in WSL for better compatibility" -ForegroundColor Yellow
        return $null
    } catch {
        # WSL not available
    }

    Write-Host "✗ OpenSSL not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install one of the following:" -ForegroundColor Yellow
    Write-Host "  1. OpenSSL for Windows: https://slproweb.com/products/Win32OpenSSL.html"
    Write-Host "  2. Git for Windows (includes OpenSSL): https://git-scm.com/download/win"
    Write-Host "  3. Windows Subsystem for Linux (WSL)"
    Write-Host "  4. Use Chocolatey: choco install openssl"
    Write-Host ""
    return $null
}

function New-DeveloperKey {
    param([string]$OpenSSLPath)

    Write-Host "Generating Garmin Connect IQ Developer Key..." -ForegroundColor Cyan
    Write-Host "Key size: $KeySize bits" -ForegroundColor White
    Write-Host "Output files: $PemFile, $DerFile" -ForegroundColor White
    Write-Host ""

    # Check if files exist
    if ((Test-Path $PemFile) -or (Test-Path $DerFile)) {
        if (-not $Force) {
            Write-Host "Key files already exist!" -ForegroundColor Yellow
            Write-Host "  ${PemFile}: $(if (Test-Path $PemFile) { "EXISTS" } else { "NOT FOUND" })"
            Write-Host "  ${DerFile}: $(if (Test-Path $DerFile) { "EXISTS" } else { "NOT FOUND" })"
            Write-Host ""
            Write-Host "Use -Force to overwrite existing files" -ForegroundColor Red
            return $false
        } else {
            Write-Host "Overwriting existing key files..." -ForegroundColor Yellow
        }
    }

    try {
        # Step 1: Generate private key in PEM format
        Write-Host "Step 1: Generating RSA private key ($KeySize bits)..." -ForegroundColor Green
        $step1Args = @(
            "genpkey",
            "-algorithm", "RSA",
            "-out", $PemFile,
            "-outform", "PEM",
            "-pkeyopt", "rsa_keygen_bits:$KeySize"
        )

        Write-Host "Command: $OpenSSLPath $($step1Args -join ' ')" -ForegroundColor Gray
        & $OpenSSLPath @step1Args

        if ($LASTEXITCODE -ne 0) {
            throw "Failed to generate PEM key"
        }

        if (-not (Test-Path $PemFile)) {
            throw "PEM file was not created"
        }

        Write-Host "✓ PEM key generated: $PemFile" -ForegroundColor Green

        # Step 2: Convert to DER format
        Write-Host "Step 2: Converting to DER format..." -ForegroundColor Green
        $step2Args = @(
            "pkcs8",
            "-topk8",
            "-inform", "PEM",
            "-outform", "DER",
            "-in", $PemFile,
            "-out", $DerFile,
            "-nocrypt"
        )

        Write-Host "Command: $OpenSSLPath $($step2Args -join ' ')" -ForegroundColor Gray
        & $OpenSSLPath @step2Args

        if ($LASTEXITCODE -ne 0) {
            throw "Failed to convert to DER format"
        }

        if (-not (Test-Path $DerFile)) {
            throw "DER file was not created"
        }

        Write-Host "✓ DER key generated: $DerFile" -ForegroundColor Green
        Write-Host ""

        # Show file information
        $pemInfo = Get-Item $PemFile
        $derInfo = Get-Item $DerFile

        Write-Host "Key Generation Successful!" -ForegroundColor Green
        Write-Host "Files created:" -ForegroundColor Cyan
        Write-Host "  $PemFile - $($pemInfo.Length) bytes (PEM format - keep secure)" -ForegroundColor White
        Write-Host "  $DerFile - $($derInfo.Length) bytes (DER format - for Garmin SDK)" -ForegroundColor White
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "  1. Keep both files secure and backed up"
        Write-Host "  2. Use $DerFile with MonkeyC compiler (-y parameter)"
        Write-Host "  3. Never share your private keys publicly"
        Write-Host ""
        Write-Host "Usage in build scripts:" -ForegroundColor Cyan
        Write-Host "  monkeyc -d device -f monkey.jungle -o app.prg -y $DerFile"

        return $true

    } catch {
        Write-Host "✗ Error: $_" -ForegroundColor Red

        # Cleanup on failure
        if (Test-Path $PemFile) {
            Remove-Item $PemFile -Force
            Write-Host "Cleaned up: $PemFile" -ForegroundColor Yellow
        }
        if (Test-Path $DerFile) {
            Remove-Item $DerFile -Force
            Write-Host "Cleaned up: $DerFile" -ForegroundColor Yellow
        }

        return $false
    }
}

function Show-KeyInfo {
    if (Test-Path $DerFile) {
        Write-Host "Existing key information:" -ForegroundColor Cyan
        $derInfo = Get-Item $DerFile
        Write-Host "  File: $DerFile"
        Write-Host "  Size: $($derInfo.Length) bytes"
        Write-Host "  Created: $($derInfo.CreationTime)"
        Write-Host "  Modified: $($derInfo.LastWriteTime)"

        if (Test-Path $PemFile) {
            $pemInfo = Get-Item $PemFile
            Write-Host "  PEM file: $PemFile ($($pemInfo.Length) bytes)"
        }
    } else {
        Write-Host "No existing developer key found." -ForegroundColor Yellow
        Write-Host "Run .\generate-key.ps1 to create one." -ForegroundColor Green
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "Garmin Connect IQ Developer Key Generator" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Find OpenSSL
$openSSLPath = Find-OpenSSL
if (-not $openSSLPath) {
    exit 1
}

# Generate key
$success = New-DeveloperKey -OpenSSLPath $openSSLPath

if ($success) {
    Write-Host "Key generation completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "Key generation failed!" -ForegroundColor Red
    exit 1
}
