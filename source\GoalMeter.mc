// Import required Garmin Connect IQ SDK modules for goal meter functionality
using Toybox.WatchUi as Ui;
using Toybox.System as Sys;
using Toybox.Application as App;
using Toybox.Graphics;

import Toybox.Lang;

// Minimum height in pixels for a whole segment to be visually distinguishable
// const MIN_WHOLE_SEGMENT_HEIGHT = 5;

/**
 * Enumeration defining all available goal types that can be displayed in the goal meters.
 * These correspond to different types of fitness and system metrics.
 */
enum /* GOAL_TYPES */ {
	// Custom goal types (negative values to avoid conflicts with system goal types)
	GOAL_TYPE_BATTERY = -1,         // Battery level percentage
	GOAL_TYPE_CALORIES = -2,        // Calories burned today
	GOAL_TYPE_OFF = -3,             // Goal meter disabled/hidden

	// System goal types (match Garmin's built-in goal type constants)
	GOAL_TYPE_STEPS = 0,            // Daily step count goal (App.GOAL_TYPE_STEPS)
	GOAL_TYPE_FLOORS_CLIMBED,       // Floors climbed goal (App.GOAL_TYPE_FLOORS_CLIMBED)
	GOAL_TYPE_ACTIVE_MINUTES        // Active minutes goal (App.GOAL_TYPE_ACTIVE_MINUTES)
}

/**
 * GoalMeter class renders vertical progress meters on the left and right sides of the watch face.
 * These meters display progress toward various fitness goals using segmented bars.
 *
 * The class supports two rendering modes:
 * 1. Buffered drawing (memory-intensive but efficient):
 *    - On initialization: Calculate clip width (complex for arc-shaped screens); create buffers for empty and filled segments
 *    - On value changes: If max changes, recalculate segment layout and mark buffers dirty; if current changes, recalculate fill height
 *    - On draw: If buffers dirty, redraw them and clear flag; clip appropriate portions of each buffer to screen
 *    - Each buffer contains all segments in appropriate color with separators; maximum 2 draws per cycle
 *
 * 2. Unbuffered drawing (memory-efficient but slower):
 *    - Direct drawing to screen without intermediate buffers
 *    - Recalculates and redraws segments on each draw cycle
 *
 * Features:
 * - Adaptive segment sizing based on goal values
 * - Support for both rectangular and circular screen shapes
 * - Multiple visual styles (all segments, filled only, merged, hidden)
 * - Automatic separator spacing adjustment
 * - Memory-optimized color palettes for buffered mode
 */
class GoalMeter extends Ui.Drawable {

	// Layout and positioning properties
	private var mSide;              // Side of screen (:left or :right)
	private var mStroke;            // Stroke width of the meter bar
	private var mWidth;             // Calculated clip width of meter (complex for circular screens)
	private var mHeight;            // Total height of meter area
	private var mSeparator;         // Current stroke width of separator bars between segments
	private var mLayoutSeparator;   // Original separator width specified in layout configuration

	// Segment calculation and rendering data
	private var mSegments;          // Array of individual segment heights in pixels (excluding separators)
	private var mFillHeight;        // Total height of filled portion in pixels (including separators)

	// Buffered rendering resources (only available in buffered mode)
	(:buffered) private var mFilledBuffer;   // Bitmap buffer containing all segments in filled color
	(:buffered) private var mEmptyBuffer;    // Bitmap buffer containing all segments in empty color

	// Buffer management flags
	private var mBuffersNeedRecreate = true; // Buffers need complete recreation (color palette changes)
	private var mBuffersNeedRedraw = true;   // Buffers need content redraw (segment layout changes)

	// Goal data and state
	private var mCurrentValue;      // Current progress value
	private var mMaxValue;          // Maximum/target value for the goal
	private var mIsOff = false;     // Issue #114: Flag to hide entire meter when goal type is OFF

	// Goal meter visual styles enumeration (commented out but values used directly)
	// These correspond to user-selectable display styles in watch face settings
	// private enum /* GOAL_METER_STYLES */ {
	// 	ALL_SEGMENTS,           // 0: Show all segments (filled and empty) with separators
	// 	ALL_SEGMENTS_MERGED,    // 1: Show all segments without separators (merged appearance)
	// 	HIDDEN,                 // 2: Hide goal meter completely
	// 	FILLED_SEGMENTS,        // 3: Show only filled segments with separators
	// 	FILLED_SEGMENTS_MERGED  // 4: Show only filled segments without separators
	// }

	/**
	 * Type definition for GoalMeter constructor parameters.
	 * Defines the layout properties needed to position and size the goal meter.
	 */
	typedef GoalMeterParams as {
		:side as Symbol,        // Which side of screen (:left or :right)
		:stroke as Number,      // Width of the meter bar in pixels
		:height as Number,      // Total height of meter area in pixels
		:separator as Number    // Width of separator bars between segments in pixels
	};

	/**
	 * Constructor for GoalMeter drawable component.
	 * Initializes the goal meter with specified layout parameters and calculates dimensions.
	 *
	 * @param params GoalMeterParams containing layout configuration
	 */
	function initialize(params as GoalMeterParams) {
		Drawable.initialize(params);

		// Store layout parameters
		mSide = params[:side];
		mStroke = params[:stroke];
		mHeight = params[:height];
		mLayoutSeparator = params[:separator];

		// Apply current meter style settings to determine active separator width
		onSettingsChanged();

		// Calculate actual meter width (complex for circular screens)
		mWidth = getWidth();
	}

	/**
	 * Calculates the actual width of the goal meter based on screen shape.
	 * For rectangular screens, width equals stroke width.
	 * For circular screens, uses geometric calculations to determine the width of the arc segment.
	 *
	 * @return Number The calculated width of the goal meter in pixels
	 */
	function getWidth() {
		var width;

		var halfScreenWidth;
		var innerRadius;

		// Simple case: rectangular screens use stroke width directly
		if (Sys.getDeviceSettings().screenShape == Sys.SCREEN_SHAPE_RECTANGLE) {
			width = mStroke;
		} else {
			// Complex case: circular screens require geometric calculation
			// Calculate the width of the arc segment at the specified height
			halfScreenWidth = Sys.getDeviceSettings().screenWidth / 2;
			innerRadius = halfScreenWidth - mStroke;

			// Use Pythagorean theorem to find the chord width at half the meter height
			// width = halfScreenWidth - sqrt(innerRadius² - (height/2)²)
			width = halfScreenWidth - Math.sqrt(Math.pow(innerRadius, 2) - Math.pow(mHeight / 2, 2));
			width = Math.ceil(width).toNumber(); // Round up to ensure full pixel coverage
		}

		return width;
	}

	/**
	 * Updates the goal meter with new current and maximum values.
	 * Handles efficient recalculation of segments and fill height based on what has changed.
	 *
	 * @param current The current progress value
	 * @param max The maximum/target value for the goal
	 * @param isOff Boolean flag indicating if this meter should be hidden
	 */
	function setValues(current, max, isOff) {

		// Handle maximum value changes (requires segment layout recalculation)
		if (max != mMaxValue) {
			mMaxValue = max;
			mCurrentValue = null; // Force recalculation of fill height

			// Recalculate segment layout based on new maximum value
			mSegments = getSegments();

			// Mark buffers as needing redraw (can't redraw here as we don't have screen DC reference)
			// Actual redraw happens in draw() method where screen dimensions are available
			mBuffersNeedRedraw = true;
		}

		// Handle current value changes (requires fill height recalculation)
		if (current != mCurrentValue) {
			mCurrentValue = current;
			// Recalculate how much of the meter should be filled
			mFillHeight = getFillHeight(mSegments);
		}

		// Update visibility state
		mIsOff = isOff;
	}

	/**
	 * Handles changes to goal meter style settings.
	 * Updates separator width based on selected visual style and manages buffer recreation.
	 * Called when user changes goal meter style in watch face settings.
	 */
	function onSettingsChanged() {
		// Mark buffers for recreation due to potential color palette changes
		mBuffersNeedRecreate = true;

		// Issue #18: Only use separator width from layout for multi-segment styles
		// Issue #62: Also apply separators for filled segment styles
		var goalMeterStyle = getPropertyValue("GoalMeterStyle");

		// Styles that show separators between segments
		if ((goalMeterStyle == 0 /* ALL_SEGMENTS */) || (goalMeterStyle == 3 /* FILLED_SEGMENTS */)) {

			// Check if separator width is changing (requires segment recalculation)
			if (mSeparator != mLayoutSeparator) {
				mMaxValue = null; // Force segment recalculation in next setValues() call
			}

			mSeparator = mLayoutSeparator; // Use configured separator width

		} else {
			// Styles that merge segments (no separators)
			// ALL_SEGMENTS_MERGED, FILLED_SEGMENTS_MERGED, or HIDDEN

			// Check if separator width is changing (requires segment recalculation)
			if (mSeparator != 0) {
				mMaxValue = null; // Force segment recalculation in next setValues() call
			}

			mSeparator = 0; // No separators for merged styles
		}
	}

	/**
	 * UNBUFFERED DRAWING MODE
	 *
	 * Different drawing algorithms have been evaluated for performance and memory usage:
	 *
	 * 1. Circle-based segments: Draw each segment as a circle clipped to rectangle of desired height, direct to screen DC.
	 *    - Pros: Intuitive approach
	 *    - Cons: Expensive performance-wise
	 *
	 * 2. Buffered drawing: Separate buffers for filled and unfilled segments (full height). Each buffer drawn as single circle
	 *    with only overlapping parts visible. Segments created by drawing horizontal background lines.
	 *    Screen DC drawn from combination of two buffers, clipped to desired fill height.
	 *    - Pros: Efficient rendering, smooth appearance
	 *    - Cons: High memory usage (4 buffers per watchface)
	 *
	 * 3. Unbuffered drawing: No buffers, no clip support. Draw each segment as rectangle, then draw circular background
	 *    mask between meters. Requires extra drawable in layout.
	 *    - Pros: Low memory usage
	 *    - Cons: More expensive per frame, requires additional layout elements
	 */
	(:unbuffered)
	function draw(dc) {

		// Issue #114: Early exit if meter is hidden or disabled
		// TODO: Reclaim any allocated buffers when goal meter is set to off
		if ((getPropertyValue("GoalMeterStyle") == 2 /* HIDDEN */) || mIsOff) {
			return;
		}

		// Calculate meter position on screen
		var left = (mSide == :left) ? 0 : (dc.getWidth() - mWidth);
		var top = (dc.getHeight() - mHeight) / 2;

		// Draw filled portion of the meter (from bottom up to fill height)
		drawSegments(dc, left, top, gThemeColour, mSegments, 0, mFillHeight);

		// Draw unfilled portion of the meter (from fill height to top)
		// Issue #62: Only draw unfilled segments for ALL_SEGMENTS or ALL_SEGMENTS_MERGED styles
		if (getPropertyValue("GoalMeterStyle") <= 1) {
			drawSegments(dc, left, top, gMeterBackgroundColour, mSegments, mFillHeight, mHeight);
		}
	}

	/**
	 * BUFFERED DRAWING MODE
	 *
	 * High-performance rendering using pre-computed bitmap buffers.
	 * This method manages two bitmap buffers (filled and empty segments) and clips appropriate
	 * portions to the screen based on current fill level.
	 *
	 * Process:
	 * 1. Check if buffers need recreation (color changes) or redrawing (layout changes)
	 * 2. Recreate/redraw buffers as needed
	 * 3. Clip and draw filled portion from filled buffer
	 * 4. Clip and draw unfilled portion from empty buffer (if style requires it)
	 *
	 * Performance: Maximum 2 draw operations to screen per frame
	 * Memory: Uses 4 buffers total (2 per goal meter)
	 */
	(:buffered)
	function draw(dc) {

		// Issue #114: Early exit if meter is hidden or disabled
		// TODO: Reclaim any allocated buffers when goal meter is set to off
		if ((getPropertyValue("GoalMeterStyle") == 2 /* HIDDEN */) || mIsOff) {
			return;
		}

		// Calculate meter position on screen
		var left = (mSide == :left) ? 0 : (dc.getWidth() - mWidth);
		var top = (dc.getHeight() - mHeight) / 2;

		// Prepare variables for buffer management and clipping calculations
		var emptyBufferDc;      // Drawing context for empty segments buffer
		var filledBufferDc;     // Drawing context for filled segments buffer

		var clipBottom;         // Bottom edge of clipping rectangle
		var clipTop;            // Top edge of clipping rectangle
		var clipHeight;         // Height of clipping rectangle

		// Screen geometry calculations for circular mask positioning
		var halfScreenDcWidth = (dc.getWidth() / 2);
		var x;                  // X position for circular mask
		var radius;             // Radius for circular mask

		// BUFFER RECREATION: Only when first draw() or color palette changes (e.g., theme change)
		if (mBuffersNeedRecreate) {
			// Create new buffers with optimized color palettes
			mEmptyBuffer = createSegmentBuffer(gMeterBackgroundColour);
			mFilledBuffer = createSegmentBuffer(gThemeColour);
			mBuffersNeedRecreate = false;
			mBuffersNeedRedraw = true; // Ensure newly-created buffers get content drawn
		}

		// BUFFER REDRAWING: Only when segment layout changes (max value changes)
		if (mBuffersNeedRedraw) {

			// Clear both buffers to background color
			emptyBufferDc = mEmptyBuffer.getDc();
			emptyBufferDc.setColor(Graphics.COLOR_TRANSPARENT, gBackgroundColour);
			emptyBufferDc.clear();

			filledBufferDc = mFilledBuffer.getDc();
			filledBufferDc.setColor(Graphics.COLOR_TRANSPARENT, gBackgroundColour);
			filledBufferDc.clear();

			// Draw complete segment layout to each buffer (full height)
			drawSegments(emptyBufferDc, 0, 0, gMeterBackgroundColour, mSegments, 0, mHeight);

			// Issue #62: Could optimize by skipping filled buffer for certain styles
			// but keeping for consistency and potential future style changes
			drawSegments(filledBufferDc, 0, 0, gThemeColour, mSegments, 0, mHeight);

			// Apply circular mask for round screens to create arc appearance
			if (Sys.getDeviceSettings().screenShape != Sys.SCREEN_SHAPE_RECTANGLE) {

				// Calculate mask position: beyond screen edge for proper arc clipping
				x = (mSide == :left) ? halfScreenDcWidth : (mWidth - halfScreenDcWidth - 1);
				radius = halfScreenDcWidth - mStroke;

				// Apply circular mask to both buffers
				emptyBufferDc.setColor(gBackgroundColour, Graphics.COLOR_TRANSPARENT);
				emptyBufferDc.fillCircle(x, (mHeight / 2), radius);

				filledBufferDc.setColor(gBackgroundColour, Graphics.COLOR_TRANSPARENT);
				filledBufferDc.fillCircle(x, (mHeight / 2), radius);
			}

			mBuffersNeedRedraw = false;
		}

		// DRAW FILLED PORTION: Clip and draw from filled buffer up to fill height
		clipBottom = dc.getHeight() - top;           // Bottom of meter area
		clipTop = clipBottom - mFillHeight;          // Top of filled area
		clipHeight = clipBottom - clipTop;           // Height of filled area

		if (clipHeight > 0) {
			// Set clipping rectangle to show only filled portion
			dc.setClip(left, clipTop, mWidth, clipHeight);
			dc.drawBitmap(left, top, mFilledBuffer);
		}

		// DRAW UNFILLED PORTION: Clip and draw from empty buffer above fill height
		// Issue #62: Only draw unfilled segments for ALL_SEGMENTS or ALL_SEGMENTS_MERGED styles
		if (getPropertyValue("GoalMeterStyle") <= 1) {
			clipBottom = clipTop;                     // Bottom of unfilled area (top of filled area)
			clipTop = top;                            // Top of meter area
			clipHeight = clipBottom - clipTop;        // Height of unfilled area

			if (clipHeight > 0) {
				// Set clipping rectangle to show only unfilled portion
				dc.setClip(left, clipTop, mWidth, clipHeight);
				dc.drawBitmap(left, top, mEmptyBuffer);
			}
		}

		// Clean up clipping state
		dc.clearClip();
	}

	/**
	 * Creates a bitmap buffer for segment rendering with memory-optimized color palette.
	 * Uses restricted 2-color palette to conserve memory (4 buffers total per watchface).
	 *
	 * @param fillColour The primary color for segments in this buffer
	 * @return Graphics.BufferedBitmap The created bitmap buffer
	 */
	(:buffered)
	function createSegmentBuffer(fillColour) {
		var options = {
			:width => mWidth,                                    // Buffer width matches meter width
			:height => mHeight,                                  // Buffer height matches meter height

			// Restricted 2-color palette for memory efficiency
			// First palette color determines initial buffer background color
			:palette => [gBackgroundColour, fillColour]
		};

		// Use newer API if available, fall back to legacy constructor
		if ((Graphics has :createBufferedBitmap)) {
			return Graphics.createBufferedBitmap(options).get();
		}

		return new Graphics.BufferedBitmap(options);
	}

	/**
	 * Draws individual meter segments as filled rectangles within a specified fill range.
	 * This is the core rendering function used by both buffered and unbuffered drawing modes.
	 *
	 * @param dc Drawing context (can be screen DC or buffer DC depending on mode)
	 * @param x X coordinate of top-left corner of meter area
	 * @param y Y coordinate of top-left corner of meter area
	 * @param fillColour Color to use for filled portions of segments
	 * @param segments Array of segment heights in pixels (excluding separators)
	 * @param startFillHeight Starting fill height in pixels (including separators, from bottom)
	 * @param endFillHeight Ending fill height in pixels (including separators, from bottom)
	 */
	function drawSegments(dc, x, y, fillColour, segments as Array<Number>, startFillHeight, endFillHeight) {
		var segmentStart = 0;       // Current segment's bottom position
		var segmentEnd;             // Current segment's top position

		var fillStart;              // Start of filled portion within current segment
		var fillEnd;                // End of filled portion within current segment
		var fillHeight;             // Height of filled portion to draw

		y += mHeight; // Adjust Y to start drawing from bottom of meter

		dc.setColor(fillColour, Graphics.COLOR_TRANSPARENT);

		// Iterate through segments, drawing filled rectangles with separators
		for (var i = 0; i < segments.size(); ++i) {
			segmentEnd = segmentStart + segments[i];

			// Determine what portion of this segment should be filled
			if ((segmentStart >= startFillHeight) && (segmentEnd <= endFillHeight)) {
				// Case 1: Entire segment is within fill range
				fillStart = segmentStart;
				fillEnd = segmentEnd;

			} else if (segmentStart >= startFillHeight) {
				// Case 2: Bottom of segment is within fill range, top extends beyond
				fillStart = segmentStart;
				fillEnd = endFillHeight;

			} else if (segmentEnd <= endFillHeight) {
				// Case 3: Top of segment is within fill range, bottom extends below
				fillStart = startFillHeight;
				fillEnd = segmentEnd;

			} else {
				// Case 4: Segment is completely outside fill range
				fillStart = 0;
				fillEnd = 0;
			}

			// Debug output (commented out for performance)
			//Sys.println("segment     : " + segmentStart + "-->" + segmentEnd);
			//Sys.println("segment fill: " + fillStart + "-->" + fillEnd);

			// Draw the filled portion if it has positive height
			fillHeight = fillEnd - fillStart;
			if (fillHeight) {
				//Sys.println("draw segment: " + x + ", " + (y - fillStart - fillHeight) + ", " + mWidth + ", " + fillHeight);
				dc.fillRectangle(x, y - fillStart - fillHeight, mWidth, fillHeight);
			}

			// Move to next segment position (including separator space)
			segmentStart = segmentEnd + mSeparator;
		}
	}

	/**
	 * Calculates the height of each individual segment based on the maximum goal value.
	 * Segments are sized to provide good visual granularity while maintaining readability.
	 *
	 * Algorithm:
	 * 1. Determine appropriate scale (value per segment) using getSegmentScale()
	 * 2. Calculate total number of segments needed (including partial segments)
	 * 3. Distribute available height among segments, accounting for separators
	 * 4. Round segment heights to nearest pixel for clean rendering
	 *
	 * @return Array<Number> Array of segment heights in pixels (excluding separators)
	 *
	 * Notes:
	 * - Last segment may be partial if max value doesn't align with segment scale
	 * - Partial segments are ensured to have at least 1 pixel height for visibility
	 * - Neighboring segments may differ by 1 pixel due to rounding
	 */
	function getSegments() {
		var segmentScale = getSegmentScale(); // Determine value each whole segment represents

		// Calculate segment layout parameters
		var numSegments = mMaxValue * 1.0 / segmentScale; // Total segments including partial (force float division)
		var numSeparators = Math.ceil(numSegments) - 1;   // Number of separator bars needed

		// Calculate available height for segments (excluding separators)
		var totalSegmentHeight = mHeight - (numSeparators * mSeparator);
		var segmentHeight = totalSegmentHeight * 1.0 / numSegments; // Height per segment (force float division)
		//Sys.println("segmentHeight " + segmentHeight);

		// Create array to hold calculated segment heights
		var segments = new [Math.ceil(numSegments)];
		var start, end, height;

		// Calculate individual segment heights with pixel-perfect rounding
		for (var i = 0; i < segments.size(); ++i) {
			start = Math.round(i * segmentHeight);           // Start position of this segment
			end = Math.round((i + 1) * segmentHeight);       // End position of this segment

			// Handle partial last segment (don't exceed total available height)
			if (end > totalSegmentHeight) {
				end = totalSegmentHeight;
			}

			height = end - start;
			segments[i] = height.toNumber();
			//Sys.println("segment " + i + " height " + height);
		}

		return segments;
	}

	/**
	 * Calculates the total height that should be filled based on current progress.
	 * This determines how much of the meter should be rendered in the filled color.
	 *
	 * Algorithm:
	 * 1. Calculate total available segment height (excluding separators)
	 * 2. Determine proportional fill height based on current/max ratio
	 * 3. Add separator heights for segments that are completely filled
	 * 4. Stop adding separators when reaching a partially filled segment
	 *
	 * @param segments Array of segment heights in pixels
	 * @return Number Total fill height in pixels (including separators for filled segments)
	 */
	function getFillHeight(segments as Array<Number>) {
		var fillHeight;
		var i;

		// Calculate total height available for segments (excluding separators)
		var totalSegmentHeight = 0;
		for (i = 0; i < segments.size(); ++i) {
			totalSegmentHeight += segments[i];
		}

		// Calculate proportional fill height based on progress ratio
		var remainingFillHeight = Math.floor((mCurrentValue * 1.0 / mMaxValue) * totalSegmentHeight).toNumber();
		fillHeight = remainingFillHeight; // Start with base fill height

		// Add separator heights for completely filled segments
		for (i = 0; i < segments.size(); ++i) {
			remainingFillHeight -= segments[i];

			if (remainingFillHeight > 0) {
				// This segment is completely filled, so include separator height
				fillHeight += mSeparator;
			} else {
				// This segment is partially filled or empty, stop adding separators
				break;
			}
		}

		//Sys.println("fillHeight " + fillHeight);
		return fillHeight;
	}

	/**
	 * Determines the optimal value that each whole segment should represent.
	 * Uses an iterative approach to find a scale that provides good visual granularity
	 * while ensuring segments are large enough to be clearly visible.
	 *
	 * Algorithm:
	 * 1. Try each scale from SEGMENT_SCALES array (1, 10, 100, 1000, 10000)
	 * 2. For each scale, calculate resulting segment height
	 * 3. Stop when segment height exceeds minimum threshold (5 pixels)
	 * 4. Return the scale that provides optimal visual balance
	 *
	 * @return Number The value that each whole segment represents
	 *
	 * Examples:
	 * - For step goal of 8000: might use scale of 1000 (8 segments)
	 * - For step goal of 50000: might use scale of 10000 (5 segments)
	 * - For battery 0-100: might use scale of 10 (10 segments)
	 */
	function getSegmentScale() {
		var segmentScale;

		var tryScaleIndex = 0;
		var segmentHeight;
		var numSegments;
		var numSeparators;
		var totalSegmentHeight;

		// Available scales in ascending order (provides different granularity levels)
		var SEGMENT_SCALES = [1, 10, 100, 1000, 10000];

		// Iterate through scales until finding one that produces adequately sized segments
		do {
			segmentScale = SEGMENT_SCALES[tryScaleIndex];

			// Calculate segment dimensions for this scale
			numSegments = mMaxValue * 1.0 / segmentScale;                    // Number of segments needed
			numSeparators = Math.ceil(numSegments);                          // Number of separators needed
			totalSegmentHeight = mHeight - (numSeparators * mSeparator);     // Height available for segments
			segmentHeight = Math.floor(totalSegmentHeight / numSegments);    // Height per segment

			tryScaleIndex++;
		} while (segmentHeight <= /* MIN_WHOLE_SEGMENT_HEIGHT */ 5);

		//Sys.println("scale " + segmentScale);
		return segmentScale;
	}
}
