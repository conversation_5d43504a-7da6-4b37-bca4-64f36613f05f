<fonts>
	<font id="HoursFont" filename="titillium-web-bold-68-tall.fnt" antialias="true"/>
	<font id="MinutesFont" filename="titillium-web-light-68-tall.fnt" antialias="true"/>
	<font id="SecondsFont" filename="titillium-web-semibold-24.fnt" antialias="true" filter="0123456789AP"/>
	<font id="DateFont" filename="titillium-web-semibold-24.fnt" antialias="true" filter=" 0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZÁÄÅÉÍÖØÚČĚŘŚŠŹŽ"/>
	<font id="NormalFont" filename="titillium-web-semibold-16.fnt" antialias="true" filter="%-.0123456789!:?CFabefgikmpsty°"/>
	<font id="NormalFontCities" filename="titillium-web-semibold-16.fnt" antialias="true" filter=" %',-.0123456789!:?ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz°ÅÍÎÖáãäåæèéíðñóôöøûüž"/>
	<font id="IconsFont" filename="crystal-icons.fnt" antialias="true"/>

	<!-- Font size chosen such that height of sunshine icon is 24px -->
	<!-- .fnt file modified such that day icons start from "A", night icons start from "a", "broken clouds" ("I") is shared -->
	<font id="WeatherIconsFontDay" filename="weather-icons-24.fnt" antialias="true" filter="ABCDEFGHI"/>
	<font id="WeatherIconsFontNight" filename="weather-icons-24.fnt" antialias="true" filter="abcdefghI"/>

	<!-- Locale-specific manual date font overrides, because fonts cannot automatically be overridden by locale -->
	<font id="DateFontOverrideZHS" filename="noto-sans-cjk-sc-medium-24.fnt" antialias="true"/>
	<font id="DateFontOverrideZHT" filename="noto-sans-cjk-tc-medium-24.fnt" antialias="true"/>
	<font id="DateFontOverrideRUS" filename="noto-sans-rus-bold-24.fnt" antialias="true"/>
	<font id="DateFontOverrideKOR" filename="namum-gothic-kor-24.fnt" antialias="true"/>
</fonts>
