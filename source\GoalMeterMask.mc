// Import required Garmin Connect IQ SDK modules for goal meter mask functionality
using Toybox.WatchUi as Ui;
using Toybox.Application as App;
using Toybox.Graphics as Gfx;

import Toybox.Lang;

/**
 * GoalMeterMask class provides a circular mask overlay for round watch faces.
 *
 * Purpose:
 * This class creates a circular mask filled with the background color to hide the rectangular
 * goal meter segments that extend beyond the circular screen boundary. It's specifically used
 * in unbuffered drawing mode where goal meters are drawn as rectangles and need to be visually
 * clipped to create the appearance of curved arc segments on round screens.
 *
 * Visual Effect:
 * - Creates a filled circle in the center of the screen using the background color
 * - The circle radius is calculated to leave a border (stroke width) around the edge
 * - This effectively "masks out" any rectangular goal meter segments that would otherwise
 *   appear in the center area of round watch faces
 *
 * Usage Context:
 * - Only used in unbuffered drawing mode (buffered mode handles masking internally)
 * - Applied as an overlay after goal meters are drawn
 * - Essential for maintaining the circular aesthetic on round watch faces
 *
 * Performance:
 * - Lightweight single circle draw operation
 * - Minimal memory footprint (no buffers required)
 * - Executed once per frame after goal meter rendering
 */
class GoalMeterMask extends Ui.Drawable {

    // Stroke width of the goal meters (determines mask radius)
    private var mStroke;

    /**
     * Type definition for GoalMeterMask constructor parameters.
     * Defines the stroke width needed to calculate the appropriate mask radius.
     */
    typedef GoalMeterMaskParams as {
        :stroke as Number    // Width of goal meter stroke (used to calculate mask radius)
    };

    /**
     * Constructor for GoalMeterMask drawable component.
     * Initializes the mask with the stroke width parameter needed for radius calculation.
     *
     * @param params GoalMeterMaskParams containing the stroke width configuration
     */
    function initialize(params as GoalMeterMaskParams) {
        Drawable.initialize(params);

        // Store stroke width for radius calculation in draw method
        mStroke = params[:stroke];
    }

    /**
     * Draws the circular mask overlay on round watch faces.
     *
     * This method creates a filled circle in the center of the screen using the background
     * color to hide any goal meter rectangles that would otherwise be visible in the center
     * area. The radius is calculated to leave the goal meter stroke width as a border.
     *
     * Calculation:
     * - Center: (screenWidth/2, screenHeight/2)
     * - Radius: (screenWidth/2) - strokeWidth
     *
     * This ensures that goal meters appear as curved arcs around the edge while the center
     * remains clean with the background color.
     *
     * @param dc Graphics drawing context for rendering the mask
     */
    function draw(dc) {
        // Set drawing color to background (mask color) with transparent background
        dc.setColor(gBackgroundColour, Gfx.COLOR_TRANSPARENT);

        // Draw filled circle at screen center with calculated radius
        // Radius = half screen width minus stroke width (leaves border for goal meters)
        dc.fillCircle(
            dc.getWidth() / 2,                    // X center coordinate
            dc.getHeight() / 2,                   // Y center coordinate
            (dc.getWidth() / 2) - mStroke         // Radius (screen radius minus stroke width)
        );
    }

}