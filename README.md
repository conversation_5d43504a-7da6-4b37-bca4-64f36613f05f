# Madre Face - Connect IQ Watch Face

A sophisticated Connect IQ watch face with comprehensive build system and development tools.

## 🚀 **Quick Start**

### **Prerequisites**
- Garmin Connect IQ SDK installed
- Connect IQ IDE or VS Code with Connect IQ extension
- Git for version control

### **Setup**
```bash
# 1. Generate developer key using Connect IQ SDK
# Follow official Garmin documentation for key generation

# 2. Build using Connect IQ IDE or VS Code
# Use F5 in VS Code with Connect IQ extension
# Or use Connect IQ IDE build commands

# 3. Deploy to simulator or device
```

## 🏗️ **Architecture & Flow Diagrams**

This project includes comprehensive flow diagrams showing how Connect IQ MonkeyC applications work:

### **📊 Available Diagrams:**
1. **Connect IQ MonkeyC Build Flow** - Complete build process from source to deployment
2. **MonkeyC Compilation Process Detail** - Detailed compilation stages and error handling
3. **Connect IQ Application Architecture** - Three-layer architecture (app/framework/hardware)
4. **Connect IQ Development Workflow** - Complete development cycle with our build system

**📖 See [ARCHITECTURE.md](ARCHITECTURE.md) for detailed explanations and interactive diagrams.**

## 🔧 **Build Methods**

### **VS Code with Connect IQ Extension (Recommended)**
```bash
# 1. Install Connect IQ extension in VS Code
# 2. Open project in VS Code
# 3. Press F5 to build and debug
# 4. Debug output appears in VS Code Debug Console
```

### **Connect IQ IDE**
```bash
# 1. Open project in Connect IQ IDE
# 2. Select target device
# 3. Build and run using IDE commands
# 4. Debug output appears in IDE console
```

### **Command Line (SDK Tools)**
```bash
# Build for simulator
monkeyc -f monkey.jungle -o bin/app.prg -y developer_key.der

# Build for device
monkeyc -f monkey.jungle -o bin/app.iq -y developer_key.der -r

# Run in simulator
monkeydo bin/app.prg instinct3amoled45mm
```

## 🐛 **Debugging**

### **Debug Output Location**
**According to [Official Garmin Documentation](https://developer.garmin.com/connect-iq/core-topics/debugging/):**
- Debug output appears **ONLY** in Connect IQ Simulator Console
- Debug output **NEVER** appears in terminal/command prompt
- Use `System.println()` or `Sys.println()` for debug output

### **Debug Code Example**
```monkeyc
using Toybox.System as System;

class MadreView extends Ui.WatchFace {
    function onUpdate(dc) {
        System.println("[DEBUG] onUpdate called");

        var battery = System.getSystemStats().battery;
        System.println("[DEBUG] Battery: " + battery + "%");

        // Your code here
    }
}
```

### **Debug Workflow**
1. Add debug prints to your MonkeyC code
2. Build: `.\build.ps1 dev instinct3amoled45mm`
3. Check **Connect IQ Simulator Console** for output
4. Iterate until issue is resolved

## 📚 **Documentation**

### **Comprehensive Guides:**
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Architecture diagrams and flow charts
- **[DEBUG.md](DEBUG.md)** - Comprehensive debugging guide (official methods)
- **[DEBUGGING_EXAMPLE.md](DEBUGGING_EXAMPLE.md)** - Practical debugging examples
- **[DEVELOPMENT.md](DEVELOPMENT.md)** - Development workflow guide
- **[MONKEY_JUNGLE_GUIDE.md](MONKEY_JUNGLE_GUIDE.md)** - monkey.jungle configuration guide
- **[GITIGNORE.md](GITIGNORE.md)** - Git ignore configuration guide

### **Quick References:**
- **[Official Garmin Debugging Docs](https://developer.garmin.com/connect-iq/core-topics/debugging/)**
- **[Connect IQ API Documentation](https://developer.garmin.com/connect-iq/api-docs/)**

## 📁 **Project Structure**

```
madre-face/
├── 📄 manifest.xml                 # App metadata and configuration
├── 📄 monkey.jungle                # Build configuration
├── 🔑 developer_key.der            # Code signing key (generated)
├── 📁 source/                      # MonkeyC source code
│   ├── 📄 MandreApp.mc            # Main application
│   ├── 📄 MadreView.mc            # Watch face view
│   └── 📄 *.mc                    # Other components
├── 📁 resources/                   # UI resources
│   ├── 📁 layouts/                # XML layouts
│   ├── 📁 drawables/              # Images and icons
│   ├── 📁 fonts/                  # Custom fonts
│   └── 📁 strings/                # Text resources
├── 📁 always-on-source/           # Always-on display code
├── 📁 bin/                        # Build output (ignored)
└── 📁 docs/                       # Component documentation
```

## 🎯 **Supported Devices & sourcePath**

### **Always-On Display Devices (36 devices):**
These devices support always-on display and use **`source;always-on-source`**:
- **Instinct 3 AMOLED 45mm/50mm** - `instinct3amoled45mm`, `instinct3amoled50mm`
- **Venu Series** - `venu`, `venu2`, `venu2s`, `venu3`, `venu3s`
- **Fenix 8 Series** - `fenix843mm`, `fenix847mm`, `fenixe`
- **Forerunner Series** - `fr165`, `fr265`, `fr965`
- **Epix 2 Series** - `epix2`, `epix2pro42mm`, `epix2pro47mm`, `epix2pro51mm`

### **Standard Devices:**
These devices use **`source`** only:
- **Fenix 7** - `fenix7`
- **Forerunner 245/945** - `fr245`, `fr945`
- **Vivoactive 4** - `vivoactive4`
- **And many more older devices...**

### **🔍 Check Any Device:**
```powershell
# Check which sourcePath a device uses
.\analyze-jungle.ps1 instinct3amoled45mm
.\analyze-jungle.ps1 fenix7

# List all always-on devices
.\analyze-jungle.ps1 -ListAll
```

## 🔄 **Development Workflow**

### **Efficient Development Loop:**

#### **Method 1: VS Code (Recommended)**
1. **Setup** (once):
   - Install Connect IQ extension in VS Code
   - Open project in VS Code

2. **Development** (repeat for each change):
   ```bash
   # Edit MonkeyC code
   # Add debug prints: System.println("[DEBUG] message");

   # Press F5 to build and debug
   # Debug output appears in VS Code Debug Console
   ```

#### **Method 2: Connect IQ IDE**
1. **Setup** (once):
   - Open project in Connect IQ IDE
   - Configure target device

2. **Development** (repeat for each change):
   ```bash
   # Edit MonkeyC code
   # Build and run using IDE commands
   # Debug output appears in IDE console
   ```

#### **Method 3: Command Line**
1. **Setup** (once):
   ```bash
   # Start Connect IQ Simulator
   connectiq
   ```

2. **Development** (repeat for each change):
   ```bash
   # Edit MonkeyC code
   # Build: monkeyc -f monkey.jungle -o bin/app.prg -y developer_key.der
   # Run: monkeydo bin/app.prg instinct3amoled45mm
   # Check Connect IQ Simulator Console for debug output
   ```

## 🛠️ **Project Features**

### **✅ What's Included:**
- **Comprehensive architecture documentation** - Flow diagrams and detailed explanations
- **Multiple development methods** - VS Code, Connect IQ IDE, command line
- **Official debugging guidance** - Based on Garmin's official documentation
- **Security best practices** - Developer keys properly ignored in git
- **Multi-device support** - Optimized layouts for different screen sizes
- **Always-on display support** - Separate source for always-on mode

### **📚 Documentation Features:**
- **Interactive flow diagrams** - Visual representation of build process
- **Step-by-step debugging guide** - Official Garmin methods
- **Practical examples** - Real-world debugging scenarios
- **Architecture explanations** - Three-layer app architecture
- **Development workflows** - Efficient development practices

## 🎉 **Getting Started**

1. **Clone the repository**
2. **Install Connect IQ SDK** from Garmin Developer website
3. **Generate developer key** using Connect IQ SDK tools
4. **Choose your development method:**
   - **VS Code**: Install Connect IQ extension, press F5 to build/debug
   - **Connect IQ IDE**: Open project, build and run
   - **Command Line**: Use monkeyc and monkeydo commands
5. **Check simulator console** for debug output (not terminal)
6. **Read the comprehensive documentation** in the files listed above

## 🔗 **References**

- [Official Garmin Connect IQ Documentation](https://developer.garmin.com/connect-iq/)
- [MonkeyC Language Reference](https://developer.garmin.com/connect-iq/reference-guides/monkey-c-reference/)
- [Connect IQ API Documentation](https://developer.garmin.com/connect-iq/api-docs/)
- [Debugging Guide](https://developer.garmin.com/connect-iq/core-topics/debugging/)

---

**Happy coding! 🚀**
