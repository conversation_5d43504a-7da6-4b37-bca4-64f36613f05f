# MandreApp Documentation

## Overview

The `MandreApp` class serves as the main application controller for the Madre watch face. It extends `AppBase` and manages the overall application lifecycle, settings, background data processing, and coordination between different watch face components.

## Purpose and Architecture

### Application Controller Pattern
The MandreApp follows the application controller pattern where:
- **Centralized Management**: Single point of control for application-wide concerns
- **Component Coordination**: Manages interactions between view, data, and background services
- **Settings Management**: Handles user configuration and propagates changes
- **Data Flow Control**: Coordinates data flow between background services and UI components

### Key Responsibilities
1. **Application Lifecycle**: Initialization, settings changes, and component coordination
2. **Data Field Configuration**: Management of user-selected data field types
3. **Background Processing**: Coordination with BackgroundService for external data
4. **Location Management**: GPS coordinate tracking and persistence
5. **Time Formatting**: Centralized time display logic with user preferences
6. **Storage Abstraction**: Unified interface for data persistence

## Technical Implementation

### Class Hierarchy
```
App.AppBase
    └── MandreApp
```

### Storage Architecture

#### Issue #86 Workaround
The application implements a dual storage strategy to work around App.Storage firmware bugs:

**Legacy Implementation (Excluded)**:
- Properties API: User settings and configuration
- Storage API: Runtime data and cached responses
- Issues: Data corruption on certain firmware versions

**Current Implementation (Active)**:
- Object Store: Unified storage for all data types
- Better reliability across firmware versions
- Simplified data management and debugging

#### Conditional Compilation
```monkey-c
(:properties_and_storage,:background,:exclude)  // Legacy implementation
(:object_store)                                 // Current implementation
```

### Key Methods

#### `initialize()`
- **Purpose**: Initializes the application base class
- **Execution**: Called once during application startup
- **Resources**: Minimal setup, prepares for view creation

#### `getInitialView()`
- **Purpose**: Creates and returns the main watch face view
- **Process**: Creates MadreView, applies initial settings, returns view array
- **Integration**: Called by system to establish main UI

#### `onSettingsChanged()`
- **Purpose**: Handles user settings changes
- **Process**: Updates field types, propagates to view, triggers web request checks
- **Performance**: Requests immediate UI update to show changes

#### `checkPendingWebRequests()`
- **Purpose**: Central coordinator for background data fetching
- **Analysis**: Location tracking, data freshness, request prioritization
- **Scheduling**: Temporal event registration for background processing

#### `onBackgroundData(data)`
- **Purpose**: Processes results from background web requests
- **Error Handling**: Distinguishes HTTP errors from API errors
- **Data Management**: Updates storage and triggers UI refresh

#### `getFormattedTime(hour, min)`
- **Purpose**: Centralized time formatting with user preferences
- **Features**: 12/24-hour support, AM/PM indicators, leading zero handling
- **Special Cases**: Noon/midnight display, timezone considerations

## Data Flow Architecture

### Settings Flow
```
User Changes Settings → onSettingsChanged() → View Updates → Web Request Checks → UI Refresh
```

### Background Data Flow
```
checkPendingWebRequests() → Background Service → onBackgroundData() → Storage Update → UI Refresh
```

### Location Management Flow
```
GPS Location → checkPendingWebRequests() → Storage Persistence → Background Service Usage
```

## Background Processing Integration

### Temporal Event Coordination
- **Registration**: Schedules background processing based on data needs
- **Priority System**: City time requests processed before weather requests
- **Retry Logic**: Failed requests remain flagged for retry
- **Resource Management**: Respects background execution time limits

### Data Requirements Analysis
The application analyzes current configuration to determine data needs:

#### City Local Time
- **Trigger**: User has configured a city name
- **Freshness**: Checks for DST transition expiration
- **Validation**: Handles city name changes and error responses

#### Weather Data
- **Trigger**: Weather or humidity fields configured AND location available
- **Freshness**: 30-minute expiration for weather data
- **Location**: Validates data location against current position

### Error Handling Strategy
- **HTTP Errors**: Ignored (no UI update, retry on next temporal event)
- **API Errors**: Stored and displayed to user with appropriate messaging
- **Network Issues**: Automatic retry through pending request flags

## Memory Management

### Storage Optimization
- **Object Store**: Unified storage reduces memory fragmentation
- **Data Filtering**: Background service filters large responses to essential data
- **Cache Management**: Intelligent caching of location and web response data

### Performance Optimizations
- **Property Caching**: Frequently accessed settings cached in memory
- **Type Safety**: Robust property access with type validation and defaults
- **Efficient Updates**: Minimal UI updates only when data actually changes

## Configuration Management

### Data Field Types
```monkey-c
mFieldTypes[0] = Field1Type  // Left data field
mFieldTypes[1] = Field2Type  // Center data field  
mFieldTypes[2] = Field3Type  // Right data field
```

### Property Access
- **Safe Retrieval**: `getIntProperty()` with type validation and defaults
- **Error Handling**: Graceful handling of null or invalid property values
- **Backward Compatibility**: Maintains compatibility across settings versions

## Time Formatting Features

### Format Support
- **12-Hour Mode**: Automatic AM/PM indicators with proper noon/midnight handling
- **24-Hour Mode**: Based on device settings
- **Leading Zeros**: User-configurable for both 12 and 24-hour modes

### Special Cases
- **Issue #6**: Noon (12:00) correctly shown as PM
- **Issue #27**: Midnight (00:00) shown as 12:00 AM
- **Issue #69**: Leading zero setting applies to both time formats

## Integration Points

### View Integration
- **Settings Propagation**: Automatic propagation of settings to view components
- **Update Coordination**: Centralized UI update requests
- **Component Access**: Provides view reference for other components

### Background Service Integration
- **Service Delegate**: Provides BackgroundService instance for temporal events
- **Data Reception**: Processes background data and updates storage
- **Request Management**: Coordinates pending request flags and scheduling

### Storage Integration
- **Unified Interface**: Single API for both properties and storage operations
- **Location Persistence**: Automatic GPS coordinate caching
- **Data Validation**: Type checking and error handling for stored data

## Best Practices

### Settings Management
✅ **Recommended:**
- Use `getIntProperty()` for type-safe property access
- Apply settings changes immediately with UI updates
- Validate property values and provide sensible defaults

### Background Processing
✅ **Recommended:**
- Check data freshness before requesting updates
- Handle both HTTP and API errors appropriately
- Respect temporal event timing and resource limits

### Memory Usage
✅ **Recommended:**
- Use Object Store for all persistent data
- Cache frequently accessed properties
- Filter background data to essential information only

## Troubleshooting

### Common Issues

#### Settings Not Applying
**Symptoms:** Changes in settings don't appear on watch face
**Causes:** Settings change handling not triggering properly
**Solutions:** Verify `onSettingsChanged()` is called and UI update requested

#### Background Data Not Updating
**Symptoms:** Weather or city time not refreshing
**Causes:** Temporal events not registering or background service issues
**Solutions:** Check pending request flags and temporal event registration

#### Location-Based Features Not Working
**Symptoms:** Weather or sunrise/sunset not available
**Causes:** GPS location not available or not being persisted
**Solutions:** Verify location tracking in `checkPendingWebRequests()`

### Debugging Strategies
1. **Settings Flow**: Trace settings changes through `onSettingsChanged()`
2. **Background Processing**: Monitor pending request flags and temporal events
3. **Data Flow**: Verify data storage and retrieval operations
4. **Location Tracking**: Check GPS availability and persistence
