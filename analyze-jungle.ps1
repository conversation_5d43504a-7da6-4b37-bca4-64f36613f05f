# Analyze monkey.jungle sourcePath for specific devices
param(
    [string]$Device = "",
    [switch]$ListAll,
    [switch]$Help
)

function Show-Help {
    Write-Host ""
    Write-Host "MONKEY.JUNGLE SOURCEPATH ANALYZER" -ForegroundColor Cyan
    Write-Host "==================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\analyze-jungle.ps1 [device_name]     # Check specific device"
    Write-Host "  .\analyze-jungle.ps1 -ListAll          # List all devices with custom sourcePath"
    Write-Host "  .\analyze-jungle.ps1 -Help             # Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Green
    Write-Host "  .\analyze-jungle.ps1 instinct3amoled45mm"
    Write-Host "  .\analyze-jungle.ps1 venu2"
    Write-Host "  .\analyze-jungle.ps1 fenix7"
    Write-Host "  .\analyze-jungle.ps1 -ListAll"
    Write-Host ""
}

function Get-SourcePath {
    param([string]$DeviceName)
    
    if (-not (Test-Path "monkey.jungle")) {
        Write-Host "Error: monkey.jungle file not found!" -ForegroundColor Red
        return
    }
    
    $content = Get-Content "monkey.jungle"
    $baseSourcePath = ""
    $deviceSourcePath = ""
    
    # Find base sourcePath
    foreach ($line in $content) {
        if ($line -match "^base\.sourcePath\s*=\s*(.+)$") {
            $baseSourcePath = $matches[1].Trim()
            break
        }
    }
    
    # Find device-specific sourcePath
    foreach ($line in $content) {
        if ($line -match "^$DeviceName\.sourcePath\s*=\s*(.+)$") {
            $deviceSourcePath = $matches[1].Trim()
            break
        }
    }
    
    Write-Host ""
    Write-Host "SOURCEPATH ANALYSIS FOR: $DeviceName" -ForegroundColor Cyan
    Write-Host "======================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "Base sourcePath:" -ForegroundColor Yellow
    if ($baseSourcePath) {
        Write-Host "  $baseSourcePath" -ForegroundColor Green
    } else {
        Write-Host "  (not defined - using default)" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "Device-specific sourcePath:" -ForegroundColor Yellow
    if ($deviceSourcePath) {
        Write-Host "  $deviceSourcePath" -ForegroundColor Green
        
        # Parse the device sourcePath
        if ($deviceSourcePath -match '\$\([^)]+\)(.*)') {
            $addition = $matches[1]
            Write-Host ""
            Write-Host "Parsed device sourcePath:" -ForegroundColor Yellow
            Write-Host "  Base: $baseSourcePath" -ForegroundColor White
            Write-Host "  Addition: $addition" -ForegroundColor White
            Write-Host "  Final: $baseSourcePath$addition" -ForegroundColor Green
        }
    } else {
        Write-Host "  (not defined - using base only)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Final sourcePath:" -ForegroundColor Yellow
        Write-Host "  $baseSourcePath" -ForegroundColor Green
    }
    Write-Host ""
}

function List-AllDevices {
    if (-not (Test-Path "monkey.jungle")) {
        Write-Host "Error: monkey.jungle file not found!" -ForegroundColor Red
        return
    }
    
    $content = Get-Content "monkey.jungle"
    $devices = @()
    
    foreach ($line in $content) {
        if ($line -match "^([^.]+)\.sourcePath\s*=\s*(.+)$") {
            $deviceName = $matches[1].Trim()
            $sourcePath = $matches[2].Trim()
            
            if ($deviceName -ne "base") {
                $devices += [PSCustomObject]@{
                    Device = $deviceName
                    SourcePath = $sourcePath
                }
            }
        }
    }
    
    Write-Host ""
    Write-Host "ALL DEVICES WITH CUSTOM SOURCEPATH" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    Write-Host ""
    
    if ($devices.Count -eq 0) {
        Write-Host "No devices with custom sourcePath found." -ForegroundColor Yellow
    } else {
        Write-Host "Found $($devices.Count) devices with custom sourcePath:" -ForegroundColor Green
        Write-Host ""
        
        foreach ($device in $devices | Sort-Object Device) {
            Write-Host "  $($device.Device)" -ForegroundColor Yellow
            Write-Host "    $($device.SourcePath)" -ForegroundColor White
            Write-Host ""
        }
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit
}

if ($ListAll) {
    List-AllDevices
    exit
}

if ($Device) {
    Get-SourcePath -DeviceName $Device
} else {
    Show-Help
}
