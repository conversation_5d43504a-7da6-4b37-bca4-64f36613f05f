// Import required Garmin Connect IQ SDK modules for date line functionality
using Toybox.WatchUi as Ui;           // User interface components and drawable base class
using Toybox.System as Sys;           // System services and device information
using Toybox.Application as App;      // Application context and resource access
using Toybox.Time;                    // Time utilities and current time access
using Toybox.Time.Gregorian;          // Gregorian calendar date formatting

import Toybox.Lang;

/**
 * DateLine class renders the date information on the watch face.
 *
 * This class provides flexible date display with support for:
 * - Multiple layout modes (single line vs. double line)
 * - Internationalization with locale-specific fonts
 * - Custom date formatting with abbreviated day/month names
 * - Visual styling with alternating colors for different date components
 * - Memory-optimized string caching to avoid repeated resource loading
 *
 * Layout Modes:
 * 1. Double Line (:double_line_date): Day of week and month on separate lines
 * 2. Single Line (:single_line_date): All date components on one centered line
 *
 * Internationalization:
 * - Supports locale-specific font overrides for CJK and Cyrillic scripts
 * - Uses abbreviated day and month names for consistent formatting
 * - Handles right-to-left and complex script rendering
 *
 * Performance Features:
 * - Caches day of week and month strings to avoid repeated resource loading
 * - Just-in-time string loading when date components change
 * - Efficient text width calculations for precise positioning
 */
class DateLine extends Ui.Drawable {

	// Layout positioning variables
	private var mX;                     // X coordinate for date text positioning
	private var mY;                     // Y coordinate for first line of date text
	private var mYLine2;                // Y coordinate for second line (double line mode only)

	// Cached date component data (to avoid repeated resource loading)
	private var mDayOfWeek;             // Current day of week number (1-7, Sunday=1)
	private var mDayOfWeekString;       // Cached localized day of week string (e.g., "MON")

	private var mMonth;                 // Current month number (1-12)
	private var mMonthString;           // Cached localized month string (e.g., "JAN")

	// Font resource for date text rendering
	private var mFont;                  // Loaded font resource (may be locale-specific override)

	/**
	 * Type definition for DateLine constructor parameters.
	 * Defines the positioning coordinates for different layout modes.
	 */
	typedef DateLineParams as {
		:x as Number,           // X coordinate for date text positioning
		:y as Number,           // Y coordinate for first line of date text
		:yLine2 as Number       // Y coordinate for second line (used in double line mode)
	};

	/**
	 * Constructor for DateLine drawable component.
	 *
	 * Initializes the date line with positioning parameters and sets up locale-specific
	 * font handling. The font selection process handles internationalization by checking
	 * for locale-specific font overrides for scripts that require special handling.
	 *
	 * Font Override System:
	 * Due to Connect IQ limitations, fonts cannot be automatically selected based on
	 * system locale. Instead, we use a manual override system where the locale is
	 * specified in resources and mapped to appropriate fonts in code.
	 *
	 * Supported Locale Overrides:
	 * - ZHS: Simplified Chinese (requires specific CJK font)
	 * - ZHT: Traditional Chinese (requires specific CJK font)
	 * - RUS: Russian (requires Cyrillic font support)
	 * - KOR: Korean (requires Hangul font support)
	 *
	 * @param params DateLineParams containing positioning coordinates
	 */
	function initialize(params as DateLineParams) {
		Drawable.initialize(params);

		// Set up locale-specific font override mapping
		var rezFonts = Rez.Fonts;
		var resourceMap = {
			"ZHS" => rezFonts.DateFontOverrideZHS,    // Simplified Chinese font
			"ZHT" => rezFonts.DateFontOverrideZHT,    // Traditional Chinese font
			"RUS" => rezFonts.DateFontOverrideRUS,    // Russian/Cyrillic font
			"KOR" => rezFonts.DateFontOverrideKOR     // Korean/Hangul font
		};

		// Load locale override setting and select appropriate font
		// This is a workaround for Connect IQ's lack of automatic locale-based font selection
		var dateFontOverride = Ui.loadResource(Rez.Strings.DATE_FONT_OVERRIDE);
		var dateFont = (resourceMap.hasKey(dateFontOverride)) ? resourceMap[dateFontOverride] : rezFonts.DateFont;
		mFont = Ui.loadResource(dateFont);

		// Store positioning parameters for text rendering
		mX = params[:x];
		mY = params[:y];
		mYLine2 = params[:yLine2];
	}

	/**
	 * Main drawing method for the date line component.
	 *
	 * This method handles the complete date rendering process including:
	 * - Efficient caching of date component strings to minimize resource loading
	 * - Custom day of week and month abbreviations for consistent formatting
	 * - Just-in-time string loading when date components change
	 * - Delegation to appropriate layout-specific drawing method
	 *
	 * Performance Optimizations:
	 * - Caches day of week and month strings to avoid repeated resource loading
	 * - Only loads new strings when the actual date components change
	 * - Uses abbreviated names for consistent cross-locale formatting
	 *
	 * String Formatting:
	 * - Uses custom abbreviated strings instead of system FORMAT_MEDIUM
	 * - Ensures consistent abbreviation length (e.g., "THU" not "Thurs")
	 * - Converts all strings to uppercase for visual consistency
	 *
	 * @param dc Graphics drawing context for rendering the date
	 */
	function draw(dc) {
		var rezStrings = Rez.Strings;
		var resourceArray;

		// Get current date information using short format for efficiency
		// Custom string handling provides better consistency than system FORMAT_MEDIUM
		// which can return inconsistent abbreviations like "Thurs" instead of "Thu"
		var now = Gregorian.info(Time.now(), Time.FORMAT_SHORT);

		// Handle day of week string caching and loading
		var dayOfWeek = now.day_of_week;
		if (dayOfWeek != mDayOfWeek) {
			mDayOfWeek = dayOfWeek;

			// Load day of week abbreviations (Sunday = index 0, Saturday = index 6)
			resourceArray = [
				rezStrings.Sun,     // Sunday
				rezStrings.Mon,     // Monday
				rezStrings.Tue,     // Tuesday
				rezStrings.Wed,     // Wednesday
				rezStrings.Thu,     // Thursday
				rezStrings.Fri,     // Friday
				rezStrings.Sat      // Saturday
			];
			// Convert to uppercase for visual consistency (day_of_week is 1-based, array is 0-based)
			mDayOfWeekString = Ui.loadResource(resourceArray[mDayOfWeek - 1]).toUpper();
		}

		// Handle month string caching and loading
		var month = now.month;
		if (month != mMonth) {
			mMonth = month;

			// Load month abbreviations (January = index 0, December = index 11)
			resourceArray = [
				rezStrings.Jan,     // January
				rezStrings.Feb,     // February
				rezStrings.Mar,     // March
				rezStrings.Apr,     // April
				rezStrings.May,     // May
				rezStrings.Jun,     // June
				rezStrings.Jul,     // July
				rezStrings.Aug,     // August
				rezStrings.Sep,     // September
				rezStrings.Oct,     // October
				rezStrings.Nov,     // November
				rezStrings.Dec      // December
			];
			// Convert to uppercase for visual consistency (month is 1-based, array is 0-based)
			mMonthString = Ui.loadResource(resourceArray[mMonth - 1]).toUpper();
		}

		// Format day of month as integer string
		var day = now.day.format(INTEGER_FORMAT);

		// Delegate to layout-specific drawing method
		// The actual method called depends on conditional compilation flags
		// (:double_line_date or :single_line_date)
		drawDate(dc, day);
	}

	/**
	 * DOUBLE LINE DATE LAYOUT
	 *
	 * Renders date information in a two-line format with day of week and day number
	 * on the first line, and month on the second line. This layout provides more
	 * vertical space usage and is suitable for watch faces with available vertical space.
	 *
	 * Layout Structure:
	 * Line 1: [DAY_OF_WEEK] [DAY_NUMBER]  (e.g., "MON 15")
	 * Line 2: [MONTH]                     (e.g., "JAN")
	 *
	 * Visual Styling:
	 * - Day of week and month: Dark mono color (less prominent)
	 * - Day number: Light mono color (more prominent, primary information)
	 * - Left-aligned positioning for consistent text flow
	 *
	 * Text Positioning:
	 * - Day of week: Left-aligned at (mX, mY)
	 * - Day number: Positioned after day of week with space separator
	 * - Month: Left-aligned at (mX, mYLine2)
	 *
	 * @param dc Graphics drawing context for rendering
	 * @param day Formatted day of month string (e.g., "15")
	 */
	(:double_line_date)
	function drawDate(dc, day) {
		// Draw day of week on first line with dark color (less prominent)
		dc.setColor(gMonoDarkColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			mX,                                                         // X position (left-aligned)
			mY,                                                         // Y position (first line)
			mFont,                                                      // Date font
			mDayOfWeekString,                                          // Day of week string (e.g., "MON")
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER  // Left-aligned, vertically centered
		);

		// Draw month on second line with dark color (less prominent)
		dc.drawText(
			mX,                                                         // X position (left-aligned)
			mYLine2,                                                    // Y position (second line)
			mFont,                                                      // Date font
			mMonthString,                                              // Month string (e.g., "JAN")
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER  // Left-aligned, vertically centered
		);

		// Draw day number after day of week with light color (more prominent)
		dc.setColor(gMonoLightColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			mX + dc.getTextWidthInPixels(mDayOfWeekString + " ", mFont), // X position (after day of week + space)
			mY,                                                         // Y position (first line)
			mFont,                                                      // Date font
			day,                                                       // Day number string (e.g., "15")
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER  // Left-aligned, vertically centered
		);
	}

	/**
	 * SINGLE LINE DATE LAYOUT
	 *
	 * Renders all date information on a single horizontal line with center alignment.
	 * This layout is more compact and suitable for watch faces with limited vertical
	 * space or when a horizontal date display is preferred.
	 *
	 * Layout Structure:
	 * [DAY_OF_WEEK] [DAY_NUMBER] [MONTH]  (e.g., "MON 15 JAN")
	 *
	 * Visual Styling:
	 * - Day of week and month: Dark mono color (less prominent)
	 * - Day number: Light mono color (more prominent, primary information)
	 * - Center-aligned as a complete unit on the screen
	 *
	 * Text Positioning Algorithm:
	 * 1. Calculate total width of complete date string
	 * 2. Center the entire string horizontally on screen
	 * 3. Draw each component sequentially with appropriate colors
	 * 4. Advance X position after each component using precise text width measurements
	 *
	 * Performance Considerations:
	 * - Uses precise text width calculations for pixel-perfect positioning
	 * - Minimizes drawing operations by calculating positions once
	 * - Efficient color switching between components
	 *
	 * @param dc Graphics drawing context for rendering
	 * @param day Formatted day of month string (e.g., "15")
	 */
	(:single_line_date)
	function drawDate(dc, day) {
		mX = mX; mYLine2 = mYLine2; // Prevent compiler warning for unused variables

		// Calculate total width of complete date string for center alignment
		var dateString = Lang.format("$1$ $2$ $3$", [mDayOfWeekString, day, mMonthString]);
		var length = dc.getTextWidthInPixels(dateString, mFont);
		var x = (dc.getWidth() / 2) - (length / 2);  // Center horizontally

		// Draw day of week with dark color (less prominent)
		dc.setColor(gMonoDarkColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			x,                                                          // X position (calculated for centering)
			mY,                                                         // Y position (single line)
			mFont,                                                      // Date font
			mDayOfWeekString,                                          // Day of week string (e.g., "MON")
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER  // Left-aligned, vertically centered
		);
		x += dc.getTextWidthInPixels(mDayOfWeekString + " ", mFont);    // Advance X position

		// Draw day number with light color (more prominent)
		dc.setColor(gMonoLightColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			x,                                                          // X position (after day of week)
			mY,                                                         // Y position (single line)
			mFont,                                                      // Date font
			day,                                                       // Day number string (e.g., "15")
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER  // Left-aligned, vertically centered
		);
		x += dc.getTextWidthInPixels(day + " ", mFont);                // Advance X position

		// Draw month with dark color (less prominent)
		dc.setColor(gMonoDarkColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			x,                                                          // X position (after day number)
			mY,                                                         // Y position (single line)
			mFont,                                                      // Date font
			mMonthString,                                              // Month string (e.g., "JAN")
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER  // Left-aligned, vertically centered
		);
	}
}