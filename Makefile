# Makefile for Madre Face Connect IQ Project
# Windows version with PowerShell support

# Default product/device for building
PRODUCT ?= instinct3amoled45mm

# Project configuration
PROJECT_NAME = madreface
APP_NAME = madre-face
DEVELOPER_KEY = developer_key.der
SETTINGS_FILE = bin/$(PROJECT_NAME)-settings.json

# Connect IQ SDK paths (Windows)
# Update this path to match your Connect IQ SDK installation
SDK_PATH = C:/Users/<USER>/AppData/Roaming/Garmin/ConnectIQ/Sdks/connectiq-sdk-win-8.1.1-2025-03-27-66dae750f
MONKEYC = "$(SDK_PATH)/bin/monkeyc.bat"
MONKEYDO = "$(SDK_PATH)/bin/monkeydo.bat"
SIMULATOR = "$(SDK_PATH)/bin/simulator.exe"

# Build directories
BIN_DIR = bin
GEN_DIR = gen
OUTPUT_PRG = $(BIN_DIR)/$(PROJECT_NAME).prg
OUTPUT_IQ = $(BIN_DIR)/$(APP_NAME).iq

# Compiler flags
DEBUG_FLAGS = -w --debug-log-level=3
RELEASE_FLAGS = -e -r -w -O=3z

# Default target
.PHONY: all
all: build

# Help target
.PHONY: help
help:
	@echo "Madre Face Connect IQ Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  build          - Build for development (default product: $(PRODUCT))"
	@echo "  build-device   - Build for specific device (use PRODUCT=device_name)"
	@echo "  run            - Build and run in simulator"
	@echo "  simulator      - Launch Connect IQ simulator"
	@echo "  publish        - Build release version (.iq file)"
	@echo "  release        - Clean and build release"
	@echo "  clean          - Clean build artifacts"
	@echo "  clean-all      - Clean all generated files"
	@echo "  test-devices   - Build for common test devices"
	@echo ""
	@echo "Usage examples:"
	@echo "  make build                              # Build for default device"
	@echo "  make build PRODUCT=venu2               # Build for Venu 2"
	@echo "  make run PRODUCT=round-240x240         # Build and run simulator"
	@echo "  make publish                           # Build release .iq file"
	@echo ""
	@echo "Common devices:"
	@echo "  instinct3amoled45mm, instinct3amoled50mm, venu2, venu3"
	@echo "  round-240x240 (simulator), fenix7, fr965, epix2"

# Create directories
$(BIN_DIR):
	@if not exist "$(BIN_DIR)" mkdir "$(BIN_DIR)"

$(GEN_DIR):
	@if not exist "$(GEN_DIR)" mkdir "$(GEN_DIR)"

# Development build
.PHONY: build
build: $(BIN_DIR)
	@echo "Building $(PROJECT_NAME) for $(PRODUCT)..."
	$(MONKEYC) -d $(PRODUCT) -f monkey.jungle -o $(OUTPUT_PRG) -y $(DEVELOPER_KEY) $(DEBUG_FLAGS)
	@echo "Build completed: $(OUTPUT_PRG)"

# Build for specific device
.PHONY: build-device
build-device: build

# Build and run in simulator
.PHONY: run
run: build
	@echo "Running $(PROJECT_NAME) in simulator for $(PRODUCT)..."
	$(MONKEYDO) $(OUTPUT_PRG) $(PRODUCT) -a "$(SETTINGS_FILE):GARMIN/Settings/$(PROJECT_NAME)-settings.json"

# Launch simulator only
.PHONY: simulator
simulator:
	@echo "Launching Connect IQ Simulator..."
	$(SIMULATOR)

# Build for simulator (round display)
.PHONY: build-sim
build-sim:
	@$(MAKE) build PRODUCT=round-240x240

# Run simulator build
.PHONY: run-sim
run-sim:
	@$(MAKE) run PRODUCT=round-240x240

# Release build (creates .iq file)
.PHONY: publish
publish: clean $(BIN_DIR)
	@echo "Building release version..."
	$(MONKEYC) -f monkey.jungle -o $(BIN_DIR)/ -p $(APP_NAME).iq -y $(DEVELOPER_KEY) $(RELEASE_FLAGS)
	@echo "Release build completed: $(OUTPUT_IQ)"

# Clean and release
.PHONY: release
release: clean publish

# Test build for multiple common devices
.PHONY: test-devices
test-devices:
	@echo "Testing build for common devices..."
	@$(MAKE) build PRODUCT=instinct3amoled45mm
	@$(MAKE) build PRODUCT=venu2
	@$(MAKE) build PRODUCT=round-240x240
	@$(MAKE) build PRODUCT=fenix7
	@echo "All test builds completed successfully!"

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@if exist "$(BIN_DIR)\*.prg" del /Q "$(BIN_DIR)\*.prg"
	@if exist "$(BIN_DIR)\*.prg.debug.xml" del /Q "$(BIN_DIR)\*.prg.debug.xml"
	@if exist "$(BIN_DIR)\*.iq" del /Q "$(BIN_DIR)\*.iq"
	@echo "Clean completed."

# Clean all generated files
.PHONY: clean-all
clean-all: clean
	@echo "Cleaning all generated files..."
	@if exist "$(BIN_DIR)" rmdir /S /Q "$(BIN_DIR)" 2>nul || echo "bin directory already clean"
	@if exist "$(GEN_DIR)" rmdir /S /Q "$(GEN_DIR)" 2>nul || echo "gen directory already clean"
	@echo "Deep clean completed."

# Build with specific optimization levels
.PHONY: build-debug
build-debug: $(BIN_DIR)
	$(MONKEYC) -d $(PRODUCT) -f monkey.jungle -o $(OUTPUT_PRG) -y $(DEVELOPER_KEY) -w --debug-log-level=5

.PHONY: build-release-test
build-release-test: $(BIN_DIR)
	$(MONKEYC) -d $(PRODUCT) -f monkey.jungle -o $(OUTPUT_PRG) -y $(DEVELOPER_KEY) -r -w -O=2

# Utility targets
.PHONY: info
info:
	@echo "Project: $(PROJECT_NAME)"
	@echo "Target Device: $(PRODUCT)"
	@echo "SDK Path: $(SDK_PATH)"
	@echo "Developer Key: $(DEVELOPER_KEY)"
	@echo "Output PRG: $(OUTPUT_PRG)"
	@echo "Output IQ: $(OUTPUT_IQ)"

# Check if developer key exists
.PHONY: check-key
check-key:
	@if not exist "$(DEVELOPER_KEY)" (echo "Error: Developer key not found: $(DEVELOPER_KEY)" && exit 1)
	@echo "Developer key found: $(DEVELOPER_KEY)"

# Validate project structure
.PHONY: validate
validate: check-key
	@if not exist "monkey.jungle" (echo "Error: monkey.jungle not found" && exit 1)
	@if not exist "manifest.xml" (echo "Error: manifest.xml not found" && exit 1)
	@if not exist "source" (echo "Error: source directory not found" && exit 1)
	@echo "Project structure validated successfully."

# Show available devices from manifest
.PHONY: devices
devices:
	@echo "Available devices from manifest.xml:"
	@findstr /C:"iq:product id" manifest.xml | findstr /V "<!--"
