# PowerShell Build Script for Madre Face Connect IQ Project
param(
    [Parameter(Position=0)]
    [string]$Command = "help",

    [Parameter(Position=1)]
    [string]$Product = "instinct3amoled45mm"
)

# Project configuration
$ProjectName = "madreface"
$AppName = "madre-face"
$DeveloperKey = "developer_key.der"

# Connect IQ SDK path (update this to match your installation)
$SdkPath = "C:\Users\<USER>\AppData\Roaming\Garmin\ConnectIQ\Sdks\connectiq-sdk-win-8.1.1-2025-03-27-66dae750f"
$MonkeyC = "$SdkPath\bin\monkeyc.bat"
$MonkeyDo = "$SdkPath\bin\monkeydo.bat"
$Simulator = "$SdkPath\bin\simulator.exe"

# Build directories
$BinDir = "bin"
$OutputPrg = "$BinDir\$ProjectName.prg"
$OutputIq = "$BinDir\$AppName.iq"
$SettingsFile = "$BinDir\$ProjectName-settings.json"

# Compiler flags
$DebugFlags = @("-w", "--debug-log-level=3")
$ReleaseFlags = @("-e", "-r", "-w", "-O=3z")

# Ensure bin directory exists
if (!(Test-Path $BinDir)) {
    New-Item -ItemType Directory -Path $BinDir | Out-Null
}

function Show-Help {
    Write-Host "Madre Face Connect IQ Build System (PowerShell)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\build.ps1 [command] [device]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Available commands:" -ForegroundColor Green
    Write-Host "  build [device]     - Build for development (default: $Product)"
    Write-Host "  run [device]       - Build and run in simulator"
    Write-Host "  build-sim          - Build for simulator (venu2)"
    Write-Host "  run-sim            - Build and run in simulator"
    Write-Host "  simulator          - Launch Connect IQ simulator"
    Write-Host "  sim-status         - Show simulator status"
    Write-Host "  sim-stop           - Stop simulator"
    Write-Host "  gen-key            - Generate developer key"
    Write-Host "  publish            - Build release version (.iq file)"
    Write-Host "  release            - Clean and build release"
    Write-Host "  clean              - Clean build artifacts"
    Write-Host "  clean-all          - Clean all generated files"
    Write-Host "  test               - Build for common test devices"
    Write-Host "  info               - Show project information"
    Write-Host "  validate           - Validate project structure"
    Write-Host "  help               - Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 build                              # Build for default device"
    Write-Host "  .\build.ps1 build venu2                       # Build for Venu 2"
    Write-Host "  .\build.ps1 run-sim                            # Build and run in simulator"
    Write-Host "  .\build.ps1 simulator                          # Launch simulator"
    Write-Host "  .\build.ps1 sim-status                         # Check simulator status"
    Write-Host "  .\build.ps1 gen-key                            # Generate developer key"
    Write-Host "  .\build.ps1 publish                            # Build release .iq file"
    Write-Host ""
    Write-Host "Common devices:" -ForegroundColor Magenta
    Write-Host "  instinct3amoled45mm, instinct3amoled50mm, venu2, venu3"
    Write-Host "  round-240x240 (simulator), fenix7, fr965, epix2"
}

function Invoke-Build {
    param([string]$TargetProduct = $Product)

    Write-Host "Building $ProjectName for $TargetProduct..." -ForegroundColor Green

    $compilerArgs = @("-d", $TargetProduct, "-f", "monkey.jungle", "-o", $OutputPrg, "-y", $DeveloperKey) + $DebugFlags

    & $MonkeyC @compilerArgs

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed: $OutputPrg" -ForegroundColor Green
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Run {
    param([string]$TargetProduct = $Product)

    Write-Host "Building and running $ProjectName for $TargetProduct..." -ForegroundColor Green

    Invoke-Build -TargetProduct $TargetProduct

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Running in simulator..." -ForegroundColor Cyan
        Write-Host "Command: $MonkeyDo $OutputPrg $TargetProduct" -ForegroundColor Gray
        & $MonkeyDo $OutputPrg $TargetProduct

        if ($LASTEXITCODE -eq 0) {
            Write-Host "App successfully loaded in simulator!" -ForegroundColor Green
        } else {
            Write-Host "Failed to load app in simulator. Make sure simulator is running." -ForegroundColor Red
        }
    }
}

function Start-Simulator {
    param([switch]$FixGraphics)

    Write-Host "Launching Connect IQ Simulator..." -ForegroundColor Cyan

    # Check if simulator is already running
    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if ($simulatorProcess) {
        Write-Host "Simulator is already running (PID: $($simulatorProcess.Id))" -ForegroundColor Yellow
    } else {
        Write-Host "Starting simulator..." -ForegroundColor Green

        # For Linux/WSL users who might encounter graphics issues
        if ($FixGraphics -and $IsLinux) {
            $env:WEBKIT_DISABLE_DMABUF_RENDERER = "1"
            Write-Host "Applied graphics fix for Linux (WEBKIT_DISABLE_DMABUF_RENDERER=1)" -ForegroundColor Yellow
        }

        Start-Process $Simulator
        Start-Sleep -Seconds 3
        Write-Host "Simulator started. You can now run apps with '.\build.ps1 run [device]'" -ForegroundColor Green

        if ($FixGraphics) {
            Write-Host "Note: If you encounter graphics issues, try running with -FixGraphics flag" -ForegroundColor Yellow
        }
    }
}

function Invoke-BuildSim {
    Write-Host "Building for simulator (venu2)..." -ForegroundColor Green
    Invoke-Build -TargetProduct "venu2"
}

function Invoke-RunSim {
    Write-Host "Building and running in simulator..." -ForegroundColor Green

    # Build for simulator
    Invoke-Build -TargetProduct "venu2"

    if ($LASTEXITCODE -eq 0) {
        # Start simulator if not running
        Start-SimulatorQuiet
        Start-Sleep -Seconds 2

        Write-Host "Running app in simulator..." -ForegroundColor Cyan
        Write-Host "Command: $MonkeyDo $OutputPrg venu2" -ForegroundColor Gray
        & $MonkeyDo $OutputPrg "venu2"

        if ($LASTEXITCODE -eq 0) {
            Write-Host "App successfully loaded in simulator!" -ForegroundColor Green
        } else {
            Write-Host "Failed to load app in simulator. Make sure simulator is running." -ForegroundColor Red
        }
    }
}

function Start-SimulatorQuiet {
    # Internal function to start simulator without verbose output
    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if (-not $simulatorProcess) {
        Start-Process $Simulator
        Start-Sleep -Seconds 3
    }
}

function Show-SimulatorStatus {
    Write-Host "Simulator Status:" -ForegroundColor Cyan

    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if ($simulatorProcess) {
        Write-Host "  Status: Running" -ForegroundColor Green
        Write-Host "  PID: $($simulatorProcess.Id)" -ForegroundColor White
        Write-Host "  Memory: $([math]::Round($simulatorProcess.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor White
        Write-Host "  Start Time: $($simulatorProcess.StartTime)" -ForegroundColor White
    } else {
        Write-Host "  Status: Not Running" -ForegroundColor Red
        Write-Host "  Use '.\build.ps1 simulator' to start" -ForegroundColor Yellow
    }
}

function Stop-Simulator {
    Write-Host "Stopping Connect IQ Simulator..." -ForegroundColor Yellow

    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if ($simulatorProcess) {
        $simulatorProcess | Stop-Process -Force
        Write-Host "Simulator stopped." -ForegroundColor Green
    } else {
        Write-Host "Simulator is not running." -ForegroundColor Yellow
    }
}

function New-DeveloperKey {
    Write-Host "Generating developer key..." -ForegroundColor Cyan

    if (Test-Path "generate-key.ps1") {
        Write-Host "Using generate-key.ps1..." -ForegroundColor Green
        & powershell -ExecutionPolicy Bypass -File "generate-key.ps1"
    } else {
        Write-Host "Key generation script not found!" -ForegroundColor Red
        Write-Host "Please ensure generate-key.ps1 exists" -ForegroundColor Yellow
        return $false
    }

    if (Test-Path $DeveloperKey) {
        Write-Host "Developer key generated successfully: $DeveloperKey" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Key generation failed!" -ForegroundColor Red
        return $false
    }
}

function Invoke-Publish {
    Write-Host "Building release version..." -ForegroundColor Green

    Invoke-Clean

    $compilerArgs = @("-f", "monkey.jungle", "-o", "$BinDir\", "-p", "$AppName.iq", "-y", $DeveloperKey) + $ReleaseFlags

    & $MonkeyC @compilerArgs

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Release build completed: $OutputIq" -ForegroundColor Green
    } else {
        Write-Host "Release build failed!" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Release {
    Invoke-Clean
    Invoke-Publish
}

function Invoke-Clean {
    Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow

    if (Test-Path "$BinDir\*.prg") { Remove-Item "$BinDir\*.prg" -Force }
    if (Test-Path "$BinDir\*.prg.debug.xml") { Remove-Item "$BinDir\*.prg.debug.xml" -Force }
    if (Test-Path "$BinDir\*.iq") { Remove-Item "$BinDir\*.iq" -Force }

    Write-Host "Clean completed." -ForegroundColor Green
}

function Invoke-CleanAll {
    Write-Host "Cleaning all generated files..." -ForegroundColor Yellow

    if (Test-Path $BinDir) { Remove-Item $BinDir -Recurse -Force }
    if (Test-Path "gen") { Remove-Item "gen" -Recurse -Force }

    Write-Host "Deep clean completed." -ForegroundColor Green
}

function Test-Devices {
    Write-Host "Testing build for common devices..." -ForegroundColor Cyan

    $devices = @("instinct3amoled45mm", "venu2", "round-240x240", "fenix7")

    foreach ($device in $devices) {
        Write-Host "Building for $device..." -ForegroundColor Yellow
        Invoke-Build -TargetProduct $device
    }

    Write-Host "All test builds completed!" -ForegroundColor Green
}

function Show-Info {
    Write-Host "Project Information:" -ForegroundColor Cyan
    Write-Host "  Project: $ProjectName"
    Write-Host "  Target Device: $Product"
    Write-Host "  SDK Path: $SdkPath"
    Write-Host "  Developer Key: $DeveloperKey"
    Write-Host "  Output PRG: $OutputPrg"
    Write-Host "  Output IQ: $OutputIq"
}

function Test-ProjectStructure {
    $errors = @()

    if (!(Test-Path $DeveloperKey)) {
        $errors += "Developer key not found: $DeveloperKey"
    }

    if (!(Test-Path "monkey.jungle")) {
        $errors += "monkey.jungle not found"
    }

    if (!(Test-Path "manifest.xml")) {
        $errors += "manifest.xml not found"
    }

    if (!(Test-Path "source")) {
        $errors += "source directory not found"
    }

    if ($errors.Count -gt 0) {
        Write-Host "Validation errors:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  - $error" -ForegroundColor Red
        }
        exit 1
    } else {
        Write-Host "Project structure validated successfully." -ForegroundColor Green
    }
}

# Command dispatcher
switch ($Command.ToLower()) {
    "build" { Invoke-Build -TargetProduct $Product }
    "run" { Invoke-Run -TargetProduct $Product }
    "build-sim" { Invoke-BuildSim }
    "run-sim" { Invoke-RunSim }
    "simulator" { Start-Simulator }
    "sim-status" { Show-SimulatorStatus }
    "sim-stop" { Stop-Simulator }
    "gen-key" { New-DeveloperKey }
    "publish" { Invoke-Publish }
    "release" { Invoke-Release }
    "clean" { Invoke-Clean }
    "clean-all" { Invoke-CleanAll }
    "test" { Test-Devices }
    "info" { Show-Info }
    "validate" { Test-ProjectStructure }
    "help" { Show-Help }
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Show-Help
        exit 1
    }
}
