# PowerShell Build Script for Madre Face Connect IQ Project
param(
    [Parameter(Position=0)]
    [string]$Command = "help",

    [Parameter(Position=1)]
    [string]$Product = "instinct3amoled45mm"
)

# Project configuration
$ProjectName = "madreface"
$AppName = "madre-face"
$DeveloperKey = "developer_key.der"

# Connect IQ SDK path (update this to match your installation)
$SdkPath = "C:\Users\<USER>\AppData\Roaming\Garmin\ConnectIQ\Sdks\connectiq-sdk-win-8.1.1-2025-03-27-66dae750f"
$MonkeyC = "$SdkPath\bin\monkeyc.bat"
$MonkeyDo = "$SdkPath\bin\monkeydo.bat"
$Simulator = "$SdkPath\bin\simulator.exe"

# Build directories
$BinDir = "bin"
$OutputPrg = "$BinDir\$ProjectName.prg"
$OutputIq = "$BinDir\$AppName.iq"
$SettingsFile = "$BinDir\$ProjectName-settings.json"

# Compiler flags
$DebugFlags = @("-w", "--debug-log-level=3")
$ReleaseFlags = @("-e", "-r", "-w", "-O=3z")

# Ensure bin directory exists
if (!(Test-Path $BinDir)) {
    New-Item -ItemType Directory -Path $BinDir | Out-Null
}

function Show-Help {
    Write-Host "Madre Face Connect IQ Build System (PowerShell)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\build.ps1 [command] [device]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Available commands:" -ForegroundColor Green
    Write-Host "  build [device]     - Build for development (default: $Product)"
    Write-Host "  run [device]       - Build and run in simulator"
    Write-Host "  build-sim          - Build for simulator (venu2)"
    Write-Host "  run-sim            - Build and run in simulator"
    Write-Host "  simulator          - Launch Connect IQ simulator"
    Write-Host "  sim-status         - Show simulator status"
    Write-Host "  sim-stop           - Stop simulator"
    Write-Host "  debug-help         - Show debug output guide"
    Write-Host "  dev [device]       - Quick development build and run"
    Write-Host "  logs               - Show recent debug logs"
    Write-Host "  gen-key            - Generate developer key"
    Write-Host "  publish            - Build release version (.iq file)"
    Write-Host "  release            - Clean and build release"
    Write-Host "  clean              - Clean build artifacts"
    Write-Host "  clean-all          - Clean all generated files"
    Write-Host "  test               - Build for common test devices"
    Write-Host "  info               - Show project information"
    Write-Host "  validate           - Validate project structure"
    Write-Host "  help               - Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 build                              # Build for default device"
    Write-Host "  .\build.ps1 build venu2                       # Build for Venu 2"
    Write-Host "  .\build.ps1 run-sim                            # Build and run in simulator"
    Write-Host "  .\build.ps1 simulator                          # Launch simulator"
    Write-Host "  .\build.ps1 sim-status                         # Check simulator status"
    Write-Host "  .\build.ps1 debug-help                         # Show debug output guide"
    Write-Host "  .\build.ps1 dev                                # Quick development build (venu2)"
    Write-Host "  .\build.ps1 dev instinct3amoled45mm           # Quick dev build for specific device"
    Write-Host "  .\build.ps1 logs                               # Show recent debug logs"
    Write-Host "  .\build.ps1 gen-key                            # Generate developer key"
    Write-Host "  .\build.ps1 publish                            # Build release .iq file"
    Write-Host ""
    Write-Host "Common devices:" -ForegroundColor Magenta
    Write-Host "  instinct3amoled45mm, instinct3amoled50mm, venu2, venu3"
    Write-Host "  round-240x240 (simulator), fenix7, fr965, epix2"
}

function Invoke-Build {
    param([string]$TargetProduct = $Product)

    Write-Host "Building $ProjectName for $TargetProduct..." -ForegroundColor Green

    $compilerArgs = @("-d", $TargetProduct, "-f", "monkey.jungle", "-o", $OutputPrg, "-y", $DeveloperKey) + $DebugFlags

    & $MonkeyC @compilerArgs

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed: $OutputPrg" -ForegroundColor Green
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Run {
    param([string]$TargetProduct = $Product)

    Write-Host "Building and running $ProjectName for $TargetProduct..." -ForegroundColor Green

    # Check if simulator is running, start if needed
    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if (-not $simulatorProcess) {
        Write-Host "Simulator not running. Starting simulator..." -ForegroundColor Yellow
        Start-SimulatorQuiet
        Start-Sleep -Seconds 5
        Write-Host "Simulator started. Continuing with build..." -ForegroundColor Green
    }

    Invoke-Build -TargetProduct $TargetProduct

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Running in simulator..." -ForegroundColor Cyan
        Write-Host "Command: $MonkeyDo $OutputPrg $TargetProduct" -ForegroundColor Gray

        # Run monkeydo with timeout and correct working directory
        $currentDir = Get-Location
        $job = Start-Job -ScriptBlock {
            param($MonkeyDo, $OutputPrg, $TargetProduct, $WorkingDir)
            Set-Location $WorkingDir
            & $MonkeyDo $OutputPrg $TargetProduct
        } -ArgumentList $MonkeyDo, $OutputPrg, $TargetProduct, $currentDir

        # Wait for job with timeout
        $timeout = 30 # seconds
        $completed = Wait-Job $job -Timeout $timeout

        if ($completed) {
            $null = Receive-Job $job
            Remove-Job $job

            Write-Host "App loading completed!" -ForegroundColor Green
            Write-Host ""
            Write-Host "DEBUG OUTPUT LOCATION:" -ForegroundColor Yellow
            Write-Host "  Sys.println() output will appear in:" -ForegroundColor White
            Write-Host "  1. Connect IQ Simulator Console (View > Output Panel)" -ForegroundColor Cyan
            Write-Host "  2. VS Code Debug Console (if using Connect IQ extension)" -ForegroundColor Cyan
            Write-Host "  3. Simulator's built-in console window" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "To see debug output:" -ForegroundColor Yellow
            Write-Host "  - Open Connect IQ Simulator window" -ForegroundColor White
            Write-Host "  - Look for console/output panel in simulator" -ForegroundColor White
            Write-Host "  - Debug output will NOT appear in this terminal" -ForegroundColor Red
        } else {
            Remove-Job $job -Force
            Write-Host "App loading timed out after $timeout seconds." -ForegroundColor Yellow
            Write-Host "This is normal - the app may still be loading in simulator." -ForegroundColor Yellow
            Write-Host "Check the Connect IQ Simulator window for your app." -ForegroundColor Cyan
        }
    }
}

function Start-Simulator {
    param([switch]$FixGraphics)

    Write-Host "Launching Connect IQ Simulator..." -ForegroundColor Cyan

    # Check if simulator is already running
    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if ($simulatorProcess) {
        Write-Host "Simulator is already running (PID: $($simulatorProcess.Id))" -ForegroundColor Yellow
    } else {
        Write-Host "Starting simulator..." -ForegroundColor Green

        # For Linux/WSL users who might encounter graphics issues
        if ($FixGraphics -and $IsLinux) {
            $env:WEBKIT_DISABLE_DMABUF_RENDERER = "1"
            Write-Host "Applied graphics fix for Linux (WEBKIT_DISABLE_DMABUF_RENDERER=1)" -ForegroundColor Yellow
        }

        Start-Process $Simulator
        Start-Sleep -Seconds 3
        Write-Host "Simulator started. You can now run apps with '.\build.ps1 run [device]'" -ForegroundColor Green

        if ($FixGraphics) {
            Write-Host "Note: If you encounter graphics issues, try running with -FixGraphics flag" -ForegroundColor Yellow
        }
    }
}

function Invoke-BuildSim {
    Write-Host "Building for simulator (venu2)..." -ForegroundColor Green
    Invoke-Build -TargetProduct "venu2"
}

function Invoke-RunSim {
    Write-Host "Building and running in simulator..." -ForegroundColor Green

    # Build for simulator
    Invoke-Build -TargetProduct "venu2"

    if ($LASTEXITCODE -eq 0) {
        # Start simulator if not running
        Start-SimulatorQuiet
        Start-Sleep -Seconds 2

        Write-Host "Running app in simulator..." -ForegroundColor Cyan
        Write-Host "Command: $MonkeyDo $OutputPrg venu2" -ForegroundColor Gray
        & $MonkeyDo $OutputPrg "venu2"

        if ($LASTEXITCODE -eq 0) {
            Write-Host "App successfully loaded in simulator!" -ForegroundColor Green
            Write-Host ""
            Write-Host "DEBUG OUTPUT LOCATION:" -ForegroundColor Yellow
            Write-Host "  Your Sys.println() output will appear in:" -ForegroundColor White
            Write-Host "  1. Connect IQ Simulator Console" -ForegroundColor Cyan
            Write-Host "  2. VS Code Debug Console (if using Connect IQ extension)" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "To see debug output:" -ForegroundColor Yellow
            Write-Host "  - Open Connect IQ Simulator window" -ForegroundColor White
            Write-Host "  - Look for console/output panel" -ForegroundColor White
            Write-Host "  - Debug prints will NOT appear in this PowerShell terminal" -ForegroundColor Red
        } else {
            Write-Host "Failed to load app in simulator. Make sure simulator is running." -ForegroundColor Red
        }
    }
}

function Start-SimulatorQuiet {
    # Internal function to start simulator without verbose output
    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if (-not $simulatorProcess) {
        Start-Process $Simulator
        Start-Sleep -Seconds 3
    }
}

function Show-SimulatorStatus {
    Write-Host "Simulator Status:" -ForegroundColor Cyan

    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if ($simulatorProcess) {
        Write-Host "  Status: Running" -ForegroundColor Green
        Write-Host "  PID: $($simulatorProcess.Id)" -ForegroundColor White
        Write-Host "  Memory: $([math]::Round($simulatorProcess.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor White
        Write-Host "  Start Time: $($simulatorProcess.StartTime)" -ForegroundColor White
    } else {
        Write-Host "  Status: Not Running" -ForegroundColor Red
        Write-Host "  Use '.\build.ps1 simulator' to start" -ForegroundColor Yellow
    }
}

function Stop-Simulator {
    Write-Host "Stopping Connect IQ Simulator..." -ForegroundColor Yellow

    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if ($simulatorProcess) {
        $simulatorProcess | Stop-Process -Force
        Write-Host "Simulator stopped." -ForegroundColor Green
    } else {
        Write-Host "Simulator is not running." -ForegroundColor Yellow
    }
}

function Show-DebugInfo {
    Write-Host ""
    Write-Host "CONNECT IQ DEBUG GUIDE" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Your Sys.println output appears in:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Connect IQ Simulator Console:" -ForegroundColor Green
    Write-Host "   - Open Connect IQ Simulator" -ForegroundColor White
    Write-Host "   - Look for 'Output' or 'Console' panel" -ForegroundColor White
    Write-Host "   - Usually at bottom of simulator window" -ForegroundColor White
    Write-Host ""
    Write-Host "2. VS Code Debug Console:" -ForegroundColor Green
    Write-Host "   - Install Connect IQ extension" -ForegroundColor White
    Write-Host "   - Use F5 to debug instead of build script" -ForegroundColor White
    Write-Host "   - Debug output appears in VS Code Debug Console" -ForegroundColor White
    Write-Host ""
    Write-Host "3. Simulator Log Files:" -ForegroundColor Green
    Write-Host "   - Windows: %APPDATA%\Garmin\ConnectIQ\Logs\" -ForegroundColor White
    Write-Host "   - Look for simulator.log or device-specific logs" -ForegroundColor White
    Write-Host "   - Use: .\build.ps1 logs" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Debug output will NOT appear in:" -ForegroundColor Red
    Write-Host "   - This PowerShell terminal" -ForegroundColor White
    Write-Host "   - Command prompt where you run build scripts" -ForegroundColor White
    Write-Host ""
    Write-Host "Tips:" -ForegroundColor Yellow
    Write-Host "   - Make sure simulator is running before loading app" -ForegroundColor White
    Write-Host "   - Try: .\build.ps1 simulator" -ForegroundColor White
    Write-Host "   - Check status: .\build.ps1 sim-status" -ForegroundColor White
    Write-Host "   - View logs: .\build.ps1 logs" -ForegroundColor White
    Write-Host ""
}

function Show-Logs {
    Write-Host ""
    Write-Host "CONNECT IQ DEBUG LOGS" -ForegroundColor Cyan
    Write-Host "=====================" -ForegroundColor Cyan
    Write-Host ""

    # Try multiple possible log locations
    $possibleLogDirs = @(
        "$env:APPDATA\Garmin\ConnectIQ\Logs",
        "$env:LOCALAPPDATA\Garmin\ConnectIQ\Logs",
        "$env:USERPROFILE\.Garmin\ConnectIQ\Logs",
        "$env:TEMP\ConnectIQ\Logs"
    )

    $logDir = $null
    foreach ($dir in $possibleLogDirs) {
        if (Test-Path $dir) {
            $logDir = $dir
            break
        }
    }

    if ($logDir) {
        Write-Host "Log Directory: $logDir" -ForegroundColor Green
        Write-Host ""

        # Show available log files
        $logFiles = Get-ChildItem $logDir -Filter "*.log" | Sort-Object LastWriteTime -Descending

        if ($logFiles.Count -gt 0) {
            Write-Host "Available log files:" -ForegroundColor Yellow
            foreach ($file in $logFiles) {
                $size = [math]::Round($file.Length / 1KB, 2)
                Write-Host "  $($file.Name) - $size KB - $($file.LastWriteTime)" -ForegroundColor White
            }
            Write-Host ""

            # Show recent entries from simulator.log
            $simLog = Join-Path $logDir "simulator.log"
            if (Test-Path $simLog) {
                Write-Host "Recent entries from simulator.log:" -ForegroundColor Yellow
                Write-Host "-----------------------------------" -ForegroundColor Gray
                try {
                    $recentLines = Get-Content $simLog -Tail 20 -ErrorAction SilentlyContinue
                    if ($recentLines) {
                        $recentLines | ForEach-Object { Write-Host $_ -ForegroundColor White }
                    } else {
                        Write-Host "No recent entries found." -ForegroundColor Gray
                    }
                } catch {
                    Write-Host "Could not read simulator.log: $($_.Exception.Message)" -ForegroundColor Red
                }
                Write-Host "-----------------------------------" -ForegroundColor Gray
            }

            Write-Host ""
            Write-Host "Commands:" -ForegroundColor Cyan
            Write-Host "  explorer `"$logDir`"                    # Open log directory" -ForegroundColor White
            Write-Host "  Get-Content `"$simLog`" -Tail 50        # Show last 50 lines" -ForegroundColor White
            Write-Host "  Get-Content `"$simLog`" -Wait           # Follow log in real-time" -ForegroundColor White
        } else {
            Write-Host "No log files found in $logDir" -ForegroundColor Yellow
            Write-Host "Make sure you've run the simulator at least once." -ForegroundColor Gray
        }
    } else {
        Write-Host "Log directories not found in any of these locations:" -ForegroundColor Red
        foreach ($dir in $possibleLogDirs) {
            Write-Host "  $dir" -ForegroundColor Gray
        }
        Write-Host ""
        Write-Host "Debug output locations:" -ForegroundColor Yellow
        Write-Host "1. Connect IQ Simulator Console (most reliable)" -ForegroundColor Green
        Write-Host "   - Open Connect IQ Simulator window" -ForegroundColor White
        Write-Host "   - Look for 'Output' or 'Console' panel" -ForegroundColor White
        Write-Host ""
        Write-Host "2. VS Code Debug Console" -ForegroundColor Green
        Write-Host "   - Install Connect IQ extension" -ForegroundColor White
        Write-Host "   - Use F5 to debug" -ForegroundColor White
        Write-Host ""
        Write-Host "Note: Log files are created when simulator runs." -ForegroundColor Gray
        Write-Host "Try running: .\build.ps1 simulator" -ForegroundColor Cyan
    }
    Write-Host ""
}

function Invoke-DevBuild {
    param([string]$TargetProduct = "venu2")

    Write-Host "QUICK DEVELOPMENT BUILD" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    Write-Host ""

    # Check simulator status
    $simulatorProcess = Get-Process -Name "simulator" -ErrorAction SilentlyContinue
    if (-not $simulatorProcess) {
        Write-Host "Starting simulator..." -ForegroundColor Yellow
        Start-SimulatorQuiet
        Start-Sleep -Seconds 3
    } else {
        Write-Host "Simulator already running (PID: $($simulatorProcess.Id))" -ForegroundColor Green
    }

    # Quick build for specified device
    Write-Host "Building for $TargetProduct..." -ForegroundColor Green
    Invoke-Build -TargetProduct $TargetProduct

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "BUILD SUCCESSFUL!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Check Connect IQ Simulator window" -ForegroundColor White
        Write-Host "2. Your app should auto-load or manually load the .prg file" -ForegroundColor White
        Write-Host "3. Debug output appears in simulator console" -ForegroundColor White
        Write-Host ""
        Write-Host "DEBUG OUTPUT LOCATIONS:" -ForegroundColor Cyan
        Write-Host "1. Simulator Console (most common):" -ForegroundColor Green
        Write-Host "   - Open Connect IQ Simulator window" -ForegroundColor White
        Write-Host "   - Look for 'Output' or 'Console' panel" -ForegroundColor White
        Write-Host ""
        Write-Host "2. Log Files:" -ForegroundColor Green
        Write-Host "   - Location: %APPDATA%\Garmin\ConnectIQ\Logs\" -ForegroundColor White
        Write-Host "   - Files: simulator.log, $TargetProduct.log" -ForegroundColor White
        Write-Host "   - Command: .\build.ps1 logs" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Development Tips:" -ForegroundColor Cyan
        Write-Host "- Use .\build.ps1 dev $TargetProduct for quick rebuilds" -ForegroundColor White
        Write-Host "- Keep simulator running between builds" -ForegroundColor White
        Write-Host "- Use VS Code + F5 for even faster development" -ForegroundColor White
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
    }
}

function New-DeveloperKey {
    Write-Host "Generating developer key..." -ForegroundColor Cyan

    if (Test-Path "generate-key.ps1") {
        Write-Host "Using generate-key.ps1..." -ForegroundColor Green
        & powershell -ExecutionPolicy Bypass -File "generate-key.ps1"
    } else {
        Write-Host "Key generation script not found!" -ForegroundColor Red
        Write-Host "Please ensure generate-key.ps1 exists" -ForegroundColor Yellow
        return $false
    }

    if (Test-Path $DeveloperKey) {
        Write-Host "Developer key generated successfully: $DeveloperKey" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Key generation failed!" -ForegroundColor Red
        return $false
    }
}

function Invoke-Publish {
    Write-Host "Building release version..." -ForegroundColor Green

    Invoke-Clean

    $compilerArgs = @("-f", "monkey.jungle", "-o", "$BinDir\", "-p", "$AppName.iq", "-y", $DeveloperKey) + $ReleaseFlags

    & $MonkeyC @compilerArgs

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Release build completed: $OutputIq" -ForegroundColor Green
    } else {
        Write-Host "Release build failed!" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Release {
    Invoke-Clean
    Invoke-Publish
}

function Invoke-Clean {
    Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow

    if (Test-Path "$BinDir\*.prg") { Remove-Item "$BinDir\*.prg" -Force }
    if (Test-Path "$BinDir\*.prg.debug.xml") { Remove-Item "$BinDir\*.prg.debug.xml" -Force }
    if (Test-Path "$BinDir\*.iq") { Remove-Item "$BinDir\*.iq" -Force }

    Write-Host "Clean completed." -ForegroundColor Green
}

function Invoke-CleanAll {
    Write-Host "Cleaning all generated files..." -ForegroundColor Yellow

    if (Test-Path $BinDir) { Remove-Item $BinDir -Recurse -Force }
    if (Test-Path "gen") { Remove-Item "gen" -Recurse -Force }

    Write-Host "Deep clean completed." -ForegroundColor Green
}

function Test-Devices {
    Write-Host "Testing build for common devices..." -ForegroundColor Cyan

    $devices = @("instinct3amoled45mm", "venu2", "round-240x240", "fenix7")

    foreach ($device in $devices) {
        Write-Host "Building for $device..." -ForegroundColor Yellow
        Invoke-Build -TargetProduct $device
    }

    Write-Host "All test builds completed!" -ForegroundColor Green
}

function Show-Info {
    Write-Host "Project Information:" -ForegroundColor Cyan
    Write-Host "  Project: $ProjectName"
    Write-Host "  Target Device: $Product"
    Write-Host "  SDK Path: $SdkPath"
    Write-Host "  Developer Key: $DeveloperKey"
    Write-Host "  Output PRG: $OutputPrg"
    Write-Host "  Output IQ: $OutputIq"
}

function Test-ProjectStructure {
    $errors = @()

    if (!(Test-Path $DeveloperKey)) {
        $errors += "Developer key not found: $DeveloperKey"
    }

    if (!(Test-Path "monkey.jungle")) {
        $errors += "monkey.jungle not found"
    }

    if (!(Test-Path "manifest.xml")) {
        $errors += "manifest.xml not found"
    }

    if (!(Test-Path "source")) {
        $errors += "source directory not found"
    }

    if ($errors.Count -gt 0) {
        Write-Host "Validation errors:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  - $error" -ForegroundColor Red
        }
        exit 1
    } else {
        Write-Host "Project structure validated successfully." -ForegroundColor Green
    }
}

# Command dispatcher
switch ($Command.ToLower()) {
    "build" { Invoke-Build -TargetProduct $Product }
    "run" { Invoke-Run -TargetProduct $Product }
    "build-sim" { Invoke-BuildSim }
    "run-sim" { Invoke-RunSim }
    "simulator" { Start-Simulator }
    "sim-status" { Show-SimulatorStatus }
    "sim-stop" { Stop-Simulator }
    "debug-help" { Show-DebugInfo }
    "dev" { Invoke-DevBuild -TargetProduct $Product }
    "logs" { Show-Logs }
    "gen-key" { New-DeveloperKey }
    "publish" { Invoke-Publish }
    "release" { Invoke-Release }
    "clean" { Invoke-Clean }
    "clean-all" { Invoke-CleanAll }
    "test" { Test-Devices }
    "info" { Show-Info }
    "validate" { Test-ProjectStructure }
    "help" { Show-Help }
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Show-Help
        exit 1
    }
}
