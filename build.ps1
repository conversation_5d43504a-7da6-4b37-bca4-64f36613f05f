# PowerShell Build Script for Madre Face Connect IQ Project
param(
    [Parameter(Position=0)]
    [string]$Command = "help",
    
    [Parameter(Position=1)]
    [string]$Product = "instinct3amoled45mm"
)

# Project configuration
$ProjectName = "madreface"
$AppName = "madre-face"
$DeveloperKey = "developer_key.der"

# Connect IQ SDK path (update this to match your installation)
$SdkPath = "C:\Users\<USER>\AppData\Roaming\Garmin\ConnectIQ\Sdks\connectiq-sdk-win-8.1.1-2025-03-27-66dae750f"
$MonkeyC = "$SdkPath\bin\monkeyc.bat"
$MonkeyDo = "$SdkPath\bin\monkeydo.bat"
$Simulator = "$SdkPath\bin\simulator.exe"

# Build directories
$BinDir = "bin"
$OutputPrg = "$BinDir\$ProjectName.prg"
$OutputIq = "$BinDir\$AppName.iq"
$SettingsFile = "$BinDir\$ProjectName-settings.json"

# Compiler flags
$DebugFlags = @("-w", "--debug-log-level=3")
$ReleaseFlags = @("-e", "-r", "-w", "-O=3z")

# Ensure bin directory exists
if (!(Test-Path $BinDir)) {
    New-Item -ItemType Directory -Path $BinDir | Out-Null
}

function Show-Help {
    Write-Host "Madre Face Connect IQ Build System (PowerShell)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\build.ps1 [command] [device]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Available commands:" -ForegroundColor Green
    Write-Host "  build [device]     - Build for development (default: $Product)"
    Write-Host "  run [device]       - Build and run in simulator"
    Write-Host "  simulator          - Launch Connect IQ simulator"
    Write-Host "  publish            - Build release version (.iq file)"
    Write-Host "  release            - Clean and build release"
    Write-Host "  clean              - Clean build artifacts"
    Write-Host "  clean-all          - Clean all generated files"
    Write-Host "  test               - Build for common test devices"
    Write-Host "  info               - Show project information"
    Write-Host "  validate           - Validate project structure"
    Write-Host "  help               - Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 build                              # Build for default device"
    Write-Host "  .\build.ps1 build venu2                       # Build for Venu 2"
    Write-Host "  .\build.ps1 run round-240x240                  # Build and run simulator"
    Write-Host "  .\build.ps1 publish                            # Build release .iq file"
    Write-Host ""
    Write-Host "Common devices:" -ForegroundColor Magenta
    Write-Host "  instinct3amoled45mm, instinct3amoled50mm, venu2, venu3"
    Write-Host "  round-240x240 (simulator), fenix7, fr965, epix2"
}

function Invoke-Build {
    param([string]$TargetProduct = $Product)
    
    Write-Host "Building $ProjectName for $TargetProduct..." -ForegroundColor Green
    
    $args = @("-d", $TargetProduct, "-f", "monkey.jungle", "-o", $OutputPrg, "-y", $DeveloperKey) + $DebugFlags
    
    & $MonkeyC @args
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed: $OutputPrg" -ForegroundColor Green
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Run {
    param([string]$TargetProduct = $Product)
    
    Write-Host "Building and running $ProjectName for $TargetProduct..." -ForegroundColor Green
    
    Invoke-Build -TargetProduct $TargetProduct
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Running in simulator..." -ForegroundColor Cyan
        & $MonkeyDo $OutputPrg $TargetProduct -a "$SettingsFile`:GARMIN/Settings/$ProjectName-settings.json"
    }
}

function Start-Simulator {
    Write-Host "Launching Connect IQ Simulator..." -ForegroundColor Cyan
    Start-Process $Simulator
}

function Invoke-Publish {
    Write-Host "Building release version..." -ForegroundColor Green
    
    Invoke-Clean
    
    $args = @("-f", "monkey.jungle", "-o", "$BinDir\", "-p", "$AppName.iq", "-y", $DeveloperKey) + $ReleaseFlags
    
    & $MonkeyC @args
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Release build completed: $OutputIq" -ForegroundColor Green
    } else {
        Write-Host "Release build failed!" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Release {
    Invoke-Clean
    Invoke-Publish
}

function Invoke-Clean {
    Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow
    
    if (Test-Path "$BinDir\*.prg") { Remove-Item "$BinDir\*.prg" -Force }
    if (Test-Path "$BinDir\*.prg.debug.xml") { Remove-Item "$BinDir\*.prg.debug.xml" -Force }
    if (Test-Path "$BinDir\*.iq") { Remove-Item "$BinDir\*.iq" -Force }
    
    Write-Host "Clean completed." -ForegroundColor Green
}

function Invoke-CleanAll {
    Write-Host "Cleaning all generated files..." -ForegroundColor Yellow
    
    if (Test-Path $BinDir) { Remove-Item $BinDir -Recurse -Force }
    if (Test-Path "gen") { Remove-Item "gen" -Recurse -Force }
    
    Write-Host "Deep clean completed." -ForegroundColor Green
}

function Test-Devices {
    Write-Host "Testing build for common devices..." -ForegroundColor Cyan
    
    $devices = @("instinct3amoled45mm", "venu2", "round-240x240", "fenix7")
    
    foreach ($device in $devices) {
        Write-Host "Building for $device..." -ForegroundColor Yellow
        Invoke-Build -TargetProduct $device
    }
    
    Write-Host "All test builds completed!" -ForegroundColor Green
}

function Show-Info {
    Write-Host "Project Information:" -ForegroundColor Cyan
    Write-Host "  Project: $ProjectName"
    Write-Host "  Target Device: $Product"
    Write-Host "  SDK Path: $SdkPath"
    Write-Host "  Developer Key: $DeveloperKey"
    Write-Host "  Output PRG: $OutputPrg"
    Write-Host "  Output IQ: $OutputIq"
}

function Test-ProjectStructure {
    $errors = @()
    
    if (!(Test-Path $DeveloperKey)) {
        $errors += "Developer key not found: $DeveloperKey"
    }
    
    if (!(Test-Path "monkey.jungle")) {
        $errors += "monkey.jungle not found"
    }
    
    if (!(Test-Path "manifest.xml")) {
        $errors += "manifest.xml not found"
    }
    
    if (!(Test-Path "source")) {
        $errors += "source directory not found"
    }
    
    if ($errors.Count -gt 0) {
        Write-Host "Validation errors:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  - $error" -ForegroundColor Red
        }
        exit 1
    } else {
        Write-Host "Project structure validated successfully." -ForegroundColor Green
    }
}

# Command dispatcher
switch ($Command.ToLower()) {
    "build" { Invoke-Build -TargetProduct $Product }
    "run" { Invoke-Run -TargetProduct $Product }
    "simulator" { Start-Simulator }
    "publish" { Invoke-Publish }
    "release" { Invoke-Release }
    "clean" { Invoke-Clean }
    "clean-all" { Invoke-CleanAll }
    "test" { Test-Devices }
    "info" { Show-Info }
    "validate" { Test-ProjectStructure }
    "help" { Show-Help }
    default { 
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Show-Help
        exit 1
    }
}
