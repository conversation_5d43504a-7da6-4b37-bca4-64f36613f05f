@echo off
echo ========================================
echo MonkeyC Build and Test - Madre Face
echo ========================================
echo.

echo IMPORTANT: Make sure you have:
echo 1. Connect IQ SDK installed
echo 2. Developer key from Garmin (optional for simulator)
echo 3. Connect IQ Simulator installed
echo.

:menu
echo Choose build option:
echo 1. Build for Simulator (No key needed)
echo 2. Build for Device (Requires developer key)
echo 3. Clean + Build for Simulator
echo 4. Test in Simulator
echo 5. Check if drawable changes applied
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto build_sim
if "%choice%"=="2" goto build_device
if "%choice%"=="3" goto clean_build_sim
if "%choice%"=="4" goto test_sim
if "%choice%"=="5" goto check_changes
if "%choice%"=="6" goto exit
goto menu

:build_sim
echo Building for simulator (fenix7x)...
monkeyc -d fenix7x -f monkey.jungle -o bin/madreface.prg
if %errorlevel% equ 0 (
    echo ✓ Build successful!
    echo File: bin/madreface.prg created
) else (
    echo ✗ Build failed!
    echo Check the error messages above
)
echo.
pause
goto menu

:build_device
echo Building for device (requires developer key)...
if "%GARMIN_DEVELOPER_KEY%"=="" (
    echo ERROR: GARMIN_DEVELOPER_KEY environment variable not set
    echo Please set it to your developer key path
    echo Example: set GARMIN_DEVELOPER_KEY=C:\path\to\your\developer_key.der
    echo.
    pause
    goto menu
)
monkeyc -d instinct3amoled45mm -f monkey.jungle -o bin/madreface.prg -y "%GARMIN_DEVELOPER_KEY%"
if %errorlevel% equ 0 (
    echo ✓ Build successful!
    echo File: bin/madreface.prg created
) else (
    echo ✗ Build failed!
    echo Check the error messages above
)
echo.
pause
goto menu

:clean_build_sim
echo Cleaning and building for simulator...
if exist bin rmdir /s /q bin
mkdir bin
echo Building...
monkeyc -d fenix7x -f monkey.jungle -o bin/madreface.prg
if %errorlevel% equ 0 (
    echo ✓ Clean build successful!
    echo Your drawable changes should now be applied
) else (
    echo ✗ Build failed!
)
echo.
pause
goto menu

:test_sim
echo Testing in Connect IQ Simulator...
if not exist bin\madreface.prg (
    echo ERROR: bin\madreface.prg not found
    echo Please build first (option 1 or 3)
    echo.
    pause
    goto menu
)
echo Starting simulator...
connectiq bin\madreface.prg
echo.
pause
goto menu

:check_changes
echo Checking if your drawable changes are applied...
echo.
echo 1. Check layout.xml files:
if exist resources\layouts\layout.xml (
    echo ✓ Main layout.xml exists
    findstr /i "drawable" resources\layouts\layout.xml | find /c "drawable" > nul
    if %errorlevel% equ 0 (
        echo   - Contains drawable elements
    )
) else (
    echo ✗ Main layout.xml not found
)

echo.
echo 2. Check build artifacts:
if exist bin\madreface.prg (
    echo ✓ madreface.prg exists
    dir bin\madreface.prg | findstr madreface.prg
) else (
    echo ✗ madreface.prg not found - need to build first
)

echo.
echo 3. To verify drawable changes:
echo    - Build with option 3 (Clean + Build)
echo    - Test with option 4 (Test in Simulator)
echo    - Check if removed drawables are gone
echo.
pause
goto menu

:exit
echo.
echo Quick Reference:
echo - For drawable changes: Always use option 3 (Clean + Build)
echo - For testing: Use option 4 (Test in Simulator)
echo - For device deployment: Use option 2 (requires developer key)
echo.
echo Goodbye!
exit
