<layout id="WatchFace">
	<drawable class="Background" />

	<drawable id="LeftGoalMeter" class="GoalMeter">
		<param name="side">:left</param>
		<param name="stroke">6</param>
		<param name="height">148</param>
		<param name="separator">2</param>
	</drawable>

	<drawable id="RightGoalMeter" class="GoalMeter">
		<param name="side">:right</param>
		<param name="stroke">6</param>
		<param name="height">148</param>
		<param name="separator">2</param>
	</drawable>

	<drawable id="DataArea" class="DataArea">
		<param name="locX">42</param>
		<param name="width">121</param>
		<param name="row1Y">122</param>
		<param name="row2Y">138</param>
		<param name="goalIconY">120</param>
		<param name="goalIconLeftX">14</param>
		<param name="goalIconRightX">191</param>
	</drawable>

	<drawable id="DataFields" class="DataFields">
		<param name="left">92</param>
		<param name="right">174</param>
		<param name="top">14</param>
		<param name="bottom">33</param>
		<param name="batteryWidth">24</param>
	</drawable>

	<drawable id="Date" class="DateLine">
		<param name="x">15</param>		
		<param name="y">14</param>
		<param name="yLine2">33</param>
	</drawable>

	<drawable id="Indicators" class="Indicators">
		<param name="locX">23</param>
		<param name="locY">80</param>
		<param name="spacingY">26</param>
		<param name="batteryWidth">20</param>
	</drawable>

	<drawable id="Time" class="ThickThinTime">
		<param name="secondsX">137</param>
		<param name="secondsY">85</param>

		<!-- Partial updates not supported -->	
		<!-- param name="secondsClipY">124</param-->
		<!-- param name="secondsClipWidth">22</param-->
		<!-- param name="secondsClipHeight">15</param-->

		<!-- Move time up slightly to centre vertically within available space -->
		<param name="adjustY">-5</param>

		<!-- Ample horizontal space, so increase AM/PM offset -->
		<param name="amPmOffset">4</param>
	</drawable>

	<drawable id="MoveBar" class="MoveBar">
		<param name="x">45</param>
		<param name="y">102</param>
		<param name="width">87</param>
		<param name="height">7</param>
		<param name="separator">3</param>
	</drawable>
	
</layout>