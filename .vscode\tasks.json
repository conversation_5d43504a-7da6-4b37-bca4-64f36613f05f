{"version": "2.0.0", "tasks": [{"label": "buildForDevice", "type": "shell", "command": "monkeyc", "args": ["-d", "instinct3amoled45mm", "-f", "monkey.jungle", "-o", "bin/madreface.prg", "-y", "${workspaceFolder}/developer_key.der"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "buildForDeviceWithKey", "type": "shell", "command": "monkeyc", "args": ["-d", "instinct3amoled45mm", "-f", "monkey.jungle", "-o", "bin/madreface.prg", "-y", "${workspaceFolder}/developer_key.der"], "options": {"env": {"GARMIN_DEVELOPER_KEY": "${workspaceFolder}/developer_key.der"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "buildForSimulator", "type": "shell", "command": "monkeyc", "args": ["-d", "instinct3amoled45mm", "-f", "monkey.jungle", "-o", "bin/madreface.prg", "-y", "${workspaceFolder}/developer_key.der"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "clean", "type": "shell", "command": "rmdir", "args": ["/s", "/q", "bin"], "windows": {"command": "rmdir", "args": ["/s", "/q", "bin"]}, "linux": {"command": "rm", "args": ["-rf", "bin"]}, "osx": {"command": "rm", "args": ["-rf", "bin"]}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}