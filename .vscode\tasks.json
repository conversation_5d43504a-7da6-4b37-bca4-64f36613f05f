{"version": "2.0.0", "tasks": [{"label": "Clean Bin Directory", "type": "shell", "command": "powershell", "args": ["-Command", "Write-Host 'Cleaning bin contents...'; if (Test-Path 'bin') { try { Get-ChildItem -Path 'bin' -Recurse -Force | Where-Object { ($_.Extension -eq '.prg' -or $_.Extension -eq '.xml' -or $_.Name -like '*.debug.xml') -or $_.PSIsContainer } | Remove-Item -Force -Recurse -ErrorAction Stop; Write-Host 'Bin contents cleaned successfully' -ForegroundColor Green } catch { Write-Host 'Error cleaning bin contents:' $_.Exception.Message -ForegroundColor Red } } else { Write-Host 'Bin directory does not exist' -ForegroundColor Yellow }; Start-Sleep -Milliseconds 200"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "problemMatcher": []}, {"label": "Clean Cache Only", "type": "shell", "command": "powershell", "args": ["-Command", "& { Write-Host 'Cleaning cache directories...'; if (Test-Path 'bin\\gen') { Remove-Item 'bin\\gen' -Recurse -Force -ErrorAction SilentlyContinue; Write-Host 'Removed gen' }; if (Test-Path 'bin\\mir') { Remove-Item 'bin\\mir' -Recurse -Force -ErrorAction SilentlyContinue; Write-Host 'Removed mir' }; if (Test-Path 'bin\\internal-mir') { Remove-Item 'bin\\internal-mir' -Recurse -Force -ErrorAction SilentlyContinue; Write-Host 'Removed internal-mir' }; if (Test-Path 'bin\\optimized') { Remove-Item 'bin\\optimized' -Recurse -Force -ErrorAction SilentlyContinue; Write-Host 'Removed optimized' }; Write-Host 'Cache cleaning completed' -ForegroundColor Green }"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Build Monkey C (Clean)", "type": "shell", "command": "monkeyc", "args": ["-d", "instinct3amoled45mm", "-f", "monkey.jungle", "-o", "bin/adam.prg", "-y", "${workspaceFolder}/developer_key.der", "-w", "--debug-log-level=3"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "monkeyc", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(WARNING|ERROR):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}, "dependsOn": "Clean Cache Only"}, {"label": "buildForDevice", "type": "shell", "command": "monkeyc", "args": ["-d", "instinct3amoled45mm", "-f", "monkey.jungle", "-o", "bin/adam.prg", "-y", "${workspaceFolder}/developer_key.der", "-w", "--debug-log-level=3"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "monkeyc", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(WARNING|ERROR):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "clean", "type": "shell", "command": "powershell", "args": ["-Command", "if (Test-Path 'bin') { Remove-Item -Path 'bin' -Recurse -Force; Write-Host 'Cleaned bin directory' } else { Write-Host 'Bin directory does not exist' }"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}