# GoalMeter Class

A vertical progress meter component for Garmin Connect IQ watch faces that displays fitness and system metrics as segmented progress bars.

## Overview

The `GoalMeter` class renders vertical progress meters on the left and right sides of watch faces, providing users with instant visual feedback on their daily fitness goals and system status.

## Features

- ✅ **Multiple Goal Types**: Support for steps, floors climbed, active minutes, calories, and battery level
- ✅ **Adaptive Design**: Automatically adjusts to rectangular and circular screen shapes
- ✅ **Flexible Rendering**: Two rendering modes for different memory constraints
- ✅ **Visual Customization**: Multiple display styles with configurable separators
- ✅ **Performance Optimized**: Efficient buffered rendering with memory management
- ✅ **Responsive Layout**: Dynamic segment sizing based on goal values

## Supported Goal Types

### System Goals (Garmin Built-in)
| Type | Description | Value |
|------|-------------|-------|
| `GOAL_TYPE_STEPS` | Daily step count | 0 |
| `GOAL_TYPE_FLOORS_CLIMBED` | Floors climbed goal | 1 |
| `GOAL_TYPE_ACTIVE_MINUTES` | Active minutes goal | 2 |

### Custom Goals
| Type | Description | Value |
|------|-------------|-------|
| `GOAL_TYPE_BATTERY` | Battery level percentage | -1 |
| `GOAL_TYPE_CALORIES` | Calories burned today | -2 |
| `GOAL_TYPE_OFF` | Meter disabled/hidden | -3 |

## Visual Styles

The goal meter supports 5 different visual styles:

| Style ID | Name | Description |
|----------|------|-------------|
| 0 | `ALL_SEGMENTS` | Show all segments (filled + empty) with separators |
| 1 | `ALL_SEGMENTS_MERGED` | Show all segments without separators |
| 2 | `HIDDEN` | Hide goal meter completely |
| 3 | `FILLED_SEGMENTS` | Show only filled segments with separators |
| 4 | `FILLED_SEGMENTS_MERGED` | Show only filled segments without separators |

## Rendering Modes

### Buffered Mode (:buffered)
- **Pros**: High performance, smooth rendering, maximum 2 draws per frame
- **Cons**: Higher memory usage (4 buffers total per watchface)
- **Best for**: Devices with sufficient memory

### Unbuffered Mode (:unbuffered)
- **Pros**: Low memory footprint, no buffer allocation
- **Cons**: More expensive per frame, requires additional layout elements
- **Best for**: Memory-constrained devices

## Usage

### Constructor

```javascript
var goalMeter = new GoalMeter({
    :side => :left,        // :left or :right
    :stroke => 12,         // Width in pixels
    :height => 180,        // Height in pixels
    :separator => 2        // Separator width in pixels
});
```

### Parameters

#### GoalMeterParams
| Parameter | Type | Description |
|-----------|------|-------------|
| `:side` | Symbol | Screen side (`:left` or `:right`) |
| `:stroke` | Number | Width of meter bar in pixels |
| `:height` | Number | Total height of meter area in pixels |
| `:separator` | Number | Width of separator bars between segments |

### Methods

#### `initialize(params)`
Initializes the goal meter with layout parameters.

#### `setValues(current, max, isOff)`
Updates the meter with new progress values.
- `current`: Current progress value
- `max`: Maximum/target value
- `isOff`: Boolean to hide meter

#### `draw(dc)`
Renders the goal meter to the drawing context.

#### `onSettingsChanged()`
Handles style setting changes and buffer management.

## Implementation Details

### Segment Calculation
- Segments are dynamically sized based on goal values
- Uses intelligent scaling (1, 10, 100, 1000, 10000) for optimal granularity
- Minimum segment height: 5 pixels for visibility
- Supports partial segments for precise progress indication

### Screen Shape Adaptation
- **Rectangular screens**: Uses stroke width directly
- **Circular screens**: Calculates arc segment width using geometric formulas
- Applies circular masks for proper arc appearance on round displays

### Memory Optimization
- Buffered mode uses 2-color palettes to minimize memory usage
- Efficient buffer recreation only when necessary
- Automatic buffer cleanup (planned feature)

## Color Integration

The goal meter integrates with watch face themes using global color variables:
- `gThemeColour`: Color for filled segments
- `gMeterBackgroundColour`: Color for empty segments
- `gBackgroundColour`: Background color for masking

## Performance Considerations

### Buffered Mode Performance
1. **Initialization**: Calculate dimensions, create buffers
2. **Value Changes**: Recalculate segments/fill height as needed
3. **Drawing**: Maximum 2 bitmap draws per frame
4. **Settings Changes**: Recreate buffers only when necessary

### Memory Usage
- Each goal meter uses 2 bitmap buffers
- Buffer size: `width × height × 2 colors`
- Total per watchface: 4 buffers (left + right meters)

## Dependencies

- Garmin Connect IQ SDK
- Toybox.WatchUi
- Toybox.System
- Toybox.Application
- Toybox.Graphics
- Toybox.Lang

## Compatibility

- Supports both rectangular and circular Garmin devices
- Compatible with Connect IQ applications
- Requires appropriate memory allocation for buffered mode

## Configuration

Goal meter behavior is controlled through watch face properties:
- `GoalMeterStyle`: Controls visual appearance (0-4)
- Theme colors: Integrated with global color scheme

## Troubleshooting

### Common Issues

1. **Segments too small**: Increase meter height or adjust goal values
2. **Memory errors in buffered mode**: Switch to unbuffered mode
3. **Circular screen clipping**: Verify geometric calculations
4. **Performance issues**: Check buffer recreation frequency

### Debug Features

Uncomment debug output in `drawSegments()` and other methods for detailed logging:
```javascript
// Sys.println("segment: " + segmentStart + "-->" + segmentEnd);
```

## Future Enhancements

- [ ] Automatic buffer cleanup when meter is disabled
- [ ] Additional goal types (heart rate zones, stress, etc.)
- [ ] Horizontal meter orientation support
- [ ] Animation support for value changes
- [ ] Custom color per goal type

## License

This component is part of a Garmin Connect IQ watch face application. Please refer to the main project license for usage terms.

---

*For more information about Garmin Connect IQ development, visit the [Connect IQ Developer Portal](https://developer.garmin.com/connect-iq/).*