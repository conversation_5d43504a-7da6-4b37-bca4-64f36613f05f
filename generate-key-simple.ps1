# Simple PowerShell Script to Generate Garmin Connect IQ Developer Key
# Based on: https://medium.com/@bgallois/garmin-app-development-without-the-visual-studio-code-85628e4b6ba1

param(
    [string]$KeyName = "developer_key",
    [switch]$Force,
    [switch]$Help
)

# Configuration
$PemFile = "$KeyName.pem"
$DerFile = "$KeyName.der"
$KeySize = 4096

function Show-Help {
    Write-Host "Garmin Connect IQ Developer Key Generator" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\generate-key-simple.ps1 [key_name] [-Force] [-Help]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Green
    Write-Host "  key_name       - Base name for key files (default: developer_key)"
    Write-Host "  -Force         - Overwrite existing key files"
    Write-Host "  -Help          - Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\generate-key-simple.ps1                    # Generate developer_key.pem and developer_key.der"
    Write-Host "  .\generate-key-simple.ps1 my_key             # Generate my_key.pem and my_key.der"
    Write-Host "  .\generate-key-simple.ps1 -Force             # Overwrite existing keys"
    Write-Host ""
    Write-Host "Requirements:" -ForegroundColor Magenta
    Write-Host "  - OpenSSL must be installed and available in PATH"
    Write-Host "  - Or use Git Bash (includes OpenSSL)"
    Write-Host "  - Or use WSL (Windows Subsystem for Linux)"
}

function Test-OpenSSL {
    try {
        $null = & openssl version 2>$null
        return $true
    } catch {
        return $false
    }
}

function Find-OpenSSL {
    Write-Host "Checking for OpenSSL..." -ForegroundColor Yellow
    
    # Check if openssl is in PATH
    if (Test-OpenSSL) {
        Write-Host "Found OpenSSL in PATH" -ForegroundColor Green
        return "openssl"
    }
    
    # Check for Git Bash OpenSSL
    $gitPaths = @(
        "C:\Program Files\Git\bin\openssl.exe",
        "C:\Program Files (x86)\Git\bin\openssl.exe"
    )
    
    foreach ($path in $gitPaths) {
        if (Test-Path $path) {
            Write-Host "Found OpenSSL in Git: $path" -ForegroundColor Green
            return $path
        }
    }
    
    Write-Host "OpenSSL not found!" -ForegroundColor Red
    Write-Host "Please install OpenSSL or Git for Windows" -ForegroundColor Yellow
    return $null
}

function New-DeveloperKey {
    param([string]$OpenSSLPath)
    
    Write-Host "Generating Garmin Connect IQ Developer Key..." -ForegroundColor Cyan
    Write-Host "Key size: $KeySize bits" -ForegroundColor White
    Write-Host "Output files: $PemFile, $DerFile" -ForegroundColor White
    Write-Host ""
    
    # Check if files exist
    if ((Test-Path $PemFile) -or (Test-Path $DerFile)) {
        if (-not $Force) {
            Write-Host "Key files already exist!" -ForegroundColor Yellow
            Write-Host "Use -Force to overwrite" -ForegroundColor Red
            return $false
        } else {
            Write-Host "Overwriting existing files..." -ForegroundColor Yellow
        }
    }
    
    try {
        # Step 1: Generate PEM key
        Write-Host "Step 1: Generating RSA private key..." -ForegroundColor Green
        $step1Args = @(
            "genpkey", "-algorithm", "RSA", "-out", $PemFile, 
            "-outform", "PEM", "-pkeyopt", "rsa_keygen_bits:$KeySize"
        )
        
        & $OpenSSLPath @step1Args
        if ($LASTEXITCODE -ne 0) { throw "Failed to generate PEM key" }
        if (-not (Test-Path $PemFile)) { throw "PEM file not created" }
        
        Write-Host "PEM key generated: $PemFile" -ForegroundColor Green
        
        # Step 2: Convert to DER
        Write-Host "Step 2: Converting to DER format..." -ForegroundColor Green
        $step2Args = @(
            "pkcs8", "-topk8", "-inform", "PEM", "-outform", "DER",
            "-in", $PemFile, "-out", $DerFile, "-nocrypt"
        )
        
        & $OpenSSLPath @step2Args
        if ($LASTEXITCODE -ne 0) { throw "Failed to convert to DER" }
        if (-not (Test-Path $DerFile)) { throw "DER file not created" }
        
        Write-Host "DER key generated: $DerFile" -ForegroundColor Green
        Write-Host ""
        
        # Show results
        $pemInfo = Get-Item $PemFile
        $derInfo = Get-Item $DerFile
        
        Write-Host "Key Generation Successful!" -ForegroundColor Green
        Write-Host "Files created:" -ForegroundColor Cyan
        Write-Host "  $PemFile - $($pemInfo.Length) bytes (PEM format)" -ForegroundColor White
        Write-Host "  $DerFile - $($derInfo.Length) bytes (DER format - for Garmin SDK)" -ForegroundColor White
        Write-Host ""
        Write-Host "Usage in build scripts:" -ForegroundColor Cyan
        Write-Host "  monkeyc -d device -f monkey.jungle -o app.prg -y $DerFile"
        
        return $true
        
    } catch {
        Write-Host "Error: $_" -ForegroundColor Red
        
        # Cleanup on failure
        if (Test-Path $PemFile) { Remove-Item $PemFile -Force }
        if (Test-Path $DerFile) { Remove-Item $DerFile -Force }
        
        return $false
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "Garmin Connect IQ Developer Key Generator" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Find OpenSSL
$openSSLPath = Find-OpenSSL
if (-not $openSSLPath) {
    exit 1
}

# Generate key
$success = New-DeveloperKey -OpenSSLPath $openSSLPath

if ($success) {
    Write-Host "Key generation completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "Key generation failed!" -ForegroundColor Red
    exit 1
}
