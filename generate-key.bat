@echo off
REM Batch Script to Generate Garmin Connect IQ Developer Key
REM Based on: https://medium.com/@bgallois/garmin-app-development-without-the-visual-studio-code-85628e4b6ba1
setlocal enabledelayedexpansion

REM Configuration
set KEY_NAME=%1
if "%KEY_NAME%"=="" set KEY_NAME=developer_key
set FORCE=%2

set PEM_FILE=%KEY_NAME%.pem
set DER_FILE=%KEY_NAME%.der
set KEY_SIZE=4096

REM Check for help
if "%1"=="help" goto :help
if "%1"=="-h" goto :help
if "%1"=="--help" goto :help

echo Garmin Connect IQ Developer Key Generator
echo =========================================
echo.

REM Find OpenSSL
call :find_openssl
if "%OPENSSL_PATH%"=="" (
    echo Error: OpenSSL not found!
    echo.
    echo Please install one of the following:
    echo   1. OpenSSL for Windows: https://slproweb.com/products/Win32OpenSSL.html
    echo   2. Git for Windows ^(includes OpenSSL^): https://git-scm.com/download/win
    echo   3. Use Chocolatey: choco install openssl
    echo   4. Use WSL: wsl openssl
    exit /b 1
)

echo Using OpenSSL: %OPENSSL_PATH%
echo.

REM Check if files exist
if exist "%PEM_FILE%" (
    if not "%FORCE%"=="--force" (
        echo Key files already exist!
        echo   %PEM_FILE%: EXISTS
        if exist "%DER_FILE%" echo   %DER_FILE%: EXISTS
        echo.
        echo Use --force as second parameter to overwrite
        echo Example: %0 %KEY_NAME% --force
        exit /b 1
    ) else (
        echo Overwriting existing key files...
    )
)

REM Generate key
call :generate_key
if %ERRORLEVEL% equ 0 (
    echo.
    echo Key generation completed successfully!
    call :show_usage
) else (
    echo.
    echo Key generation failed!
    exit /b 1
)

goto :end

:find_openssl
echo Checking for OpenSSL...

REM Check if openssl is in PATH
openssl version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    set OPENSSL_PATH=openssl
    echo Found OpenSSL in PATH
    goto :eof
)

REM Check Git installation paths
set GIT_PATHS[0]="C:\Program Files\Git\bin\openssl.exe"
set GIT_PATHS[1]="C:\Program Files (x86)\Git\bin\openssl.exe"
set GIT_PATHS[2]="%ProgramFiles%\Git\bin\openssl.exe"
set GIT_PATHS[3]="%ProgramFiles(x86)%\Git\bin\openssl.exe"

for /L %%i in (0,1,3) do (
    if exist !GIT_PATHS[%%i]! (
        set OPENSSL_PATH=!GIT_PATHS[%%i]!
        echo Found OpenSSL in Git installation: !GIT_PATHS[%%i]!
        goto :eof
    )
)

REM Check for WSL
wsl --list --quiet >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo WSL detected. You can use: wsl openssl
    echo Run this script in WSL for better compatibility
)

set OPENSSL_PATH=
goto :eof

:generate_key
echo Generating Garmin Connect IQ Developer Key...
echo Key size: %KEY_SIZE% bits
echo Output files: %PEM_FILE%, %DER_FILE%
echo.

REM Step 1: Generate private key in PEM format
echo Step 1: Generating RSA private key ^(%KEY_SIZE% bits^)...
echo Command: %OPENSSL_PATH% genpkey -algorithm RSA -out %PEM_FILE% -outform PEM -pkeyopt rsa_keygen_bits:%KEY_SIZE%

%OPENSSL_PATH% genpkey -algorithm RSA -out %PEM_FILE% -outform PEM -pkeyopt rsa_keygen_bits:%KEY_SIZE%
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to generate PEM key
    goto :cleanup_and_exit
)

if not exist "%PEM_FILE%" (
    echo Error: PEM file was not created
    goto :cleanup_and_exit
)

echo Success: PEM key generated: %PEM_FILE%

REM Step 2: Convert to DER format
echo Step 2: Converting to DER format...
echo Command: %OPENSSL_PATH% pkcs8 -topk8 -inform PEM -outform DER -in %PEM_FILE% -out %DER_FILE% -nocrypt

%OPENSSL_PATH% pkcs8 -topk8 -inform PEM -outform DER -in %PEM_FILE% -out %DER_FILE% -nocrypt
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to convert to DER format
    goto :cleanup_and_exit
)

if not exist "%DER_FILE%" (
    echo Error: DER file was not created
    goto :cleanup_and_exit
)

echo Success: DER key generated: %DER_FILE%

REM Show file information
echo.
echo Key Generation Successful!
echo Files created:
for %%F in ("%PEM_FILE%") do echo   %PEM_FILE% - %%~zF bytes ^(PEM format - keep secure^)
for %%F in ("%DER_FILE%") do echo   %DER_FILE% - %%~zF bytes ^(DER format - for Garmin SDK^)

goto :eof

:cleanup_and_exit
if exist "%PEM_FILE%" (
    del "%PEM_FILE%"
    echo Cleaned up: %PEM_FILE%
)
if exist "%DER_FILE%" (
    del "%DER_FILE%"
    echo Cleaned up: %DER_FILE%
)
exit /b 1

:show_usage
echo.
echo Next steps:
echo   1. Keep both files secure and backed up
echo   2. Use %DER_FILE% with MonkeyC compiler ^(-y parameter^)
echo   3. Never share your private keys publicly
echo.
echo Usage in build scripts:
echo   monkeyc -d device -f monkey.jungle -o app.prg -y %DER_FILE%
echo.
echo Integration with build scripts:
echo   .\build.bat build instinct3amoled45mm
echo   .\build.ps1 build venu2
goto :eof

:help
echo Garmin Connect IQ Developer Key Generator
echo.
echo Usage: %0 [key_name] [--force]
echo.
echo Parameters:
echo   key_name       - Base name for key files ^(default: developer_key^)
echo   --force        - Overwrite existing key files
echo.
echo Examples:
echo   %0                           # Generate developer_key.pem and developer_key.der
echo   %0 my_key                    # Generate my_key.pem and my_key.der
echo   %0 developer_key --force     # Overwrite existing keys
echo.
echo Requirements:
echo   - OpenSSL must be installed and available
echo   - Or use Git Bash ^(includes OpenSSL^)
echo   - Or use WSL ^(Windows Subsystem for Linux^)
echo.
echo Output files:
echo   ^<key_name^>.pem    - Private key in PEM format ^(intermediate^)
echo   ^<key_name^>.der    - Private key in DER format ^(for Garmin SDK^)
goto :end

:end
endlocal
