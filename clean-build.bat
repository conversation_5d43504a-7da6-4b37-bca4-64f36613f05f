@echo off
echo Cleaning MonkeyC build artifacts...

REM Remove all build artifacts
if exist bin (
    echo Removing bin directory...
    rmdir /s /q bin
)

REM Recreate bin directory
mkdir bin

echo Clean completed. Now rebuild your project in VSCode.
echo.
echo Steps to debug properly:
echo 1. Press Ctrl+Shift+P in VSCode
echo 2. Type "Monkey C: Clean"
echo 3. Then "Monkey C: Build for Device"
echo 4. Finally start debugging with F5
pause
