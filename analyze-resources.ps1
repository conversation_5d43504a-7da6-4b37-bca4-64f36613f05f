# Analyze which resource directories are used for specific devices
param(
    [string]$Device = "",
    [switch]$ListAll,
    [switch]$Help
)

function Show-Help {
    Write-Host ""
    Write-Host "CONNECT IQ RESOURCE ANALYZER" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\analyze-resources.ps1 [device_name]  # Check resources for specific device"
    Write-Host "  .\analyze-resources.ps1 -ListAll       # List all resource directories"
    Write-Host "  .\analyze-resources.ps1 -Help          # Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Green
    Write-Host "  .\analyze-resources.ps1 instinct3amoled45mm"
    Write-Host "  .\analyze-resources.ps1 instinct3amoled50mm"
    Write-Host "  .\analyze-resources.ps1 venu2"
    Write-Host "  .\analyze-resources.ps1 -ListAll"
    Write-Host ""
}

function Get-DeviceInfo {
    param([string]$DeviceName)

    # Device information database (based on Garmin Connect IQ documentation)
    $deviceInfo = @{
        # Instinct 3 AMOLED Series
        "instinct3amoled45mm" = @{ ScreenSize = "round-416x416"; Resolution = "416x416"; Shape = "round" }
        "instinct3amoled50mm" = @{ ScreenSize = "round-454x454"; Resolution = "454x454"; Shape = "round" }

        # Venu Series
        "venu" = @{ ScreenSize = "round-390x390"; Resolution = "390x390"; Shape = "round" }
        "venu2" = @{ ScreenSize = "round-416x416"; Resolution = "416x416"; Shape = "round" }
        "venu2s" = @{ ScreenSize = "round-360x360"; Resolution = "360x360"; Shape = "round" }
        "venu3" = @{ ScreenSize = "round-454x454"; Resolution = "454x454"; Shape = "round" }
        "venu3s" = @{ ScreenSize = "round-390x390"; Resolution = "390x390"; Shape = "round" }

        # Fenix Series
        "fenix7" = @{ ScreenSize = "round-260x260"; Resolution = "260x260"; Shape = "round" }
        "fenix7s" = @{ ScreenSize = "round-240x240"; Resolution = "240x240"; Shape = "round" }
        "fenix7x" = @{ ScreenSize = "round-280x280"; Resolution = "280x280"; Shape = "round" }
        "fenix843mm" = @{ ScreenSize = "round-454x454"; Resolution = "454x454"; Shape = "round" }
        "fenix847mm" = @{ ScreenSize = "round-454x454"; Resolution = "454x454"; Shape = "round" }

        # Forerunner Series
        "fr245" = @{ ScreenSize = "round-240x240"; Resolution = "240x240"; Shape = "round" }
        "fr245m" = @{ ScreenSize = "round-240x240"; Resolution = "240x240"; Shape = "round" }
        "fr265" = @{ ScreenSize = "round-416x416"; Resolution = "416x416"; Shape = "round" }
        "fr965" = @{ ScreenSize = "round-454x454"; Resolution = "454x454"; Shape = "round" }

        # Vivoactive Series
        "vivoactive4" = @{ ScreenSize = "round-260x260"; Resolution = "260x260"; Shape = "round" }
        "vivoactive4s" = @{ ScreenSize = "round-218x218"; Resolution = "218x218"; Shape = "round" }
        "vivoactive5" = @{ ScreenSize = "round-390x390"; Resolution = "390x390"; Shape = "round" }

        # Rectangle devices
        "approach_s7_42mm" = @{ ScreenSize = "rectangle-280x280"; Resolution = "280x280"; Shape = "rectangle" }
        "approach_s7_47mm" = @{ ScreenSize = "rectangle-320x360"; Resolution = "320x360"; Shape = "rectangle" }
    }

    if ($deviceInfo.ContainsKey($DeviceName)) {
        return $deviceInfo[$DeviceName]
    } else {
        # Try to guess based on name patterns
        if ($DeviceName -match "rectangle|approach") {
            return @{ ScreenSize = "rectangle"; Resolution = "unknown"; Shape = "rectangle" }
        } else {
            return @{ ScreenSize = "round"; Resolution = "unknown"; Shape = "round" }
        }
    }
}

function Get-ResourceDirectories {
    $resourceDirs = @()

    if (Test-Path ".") {
        $dirs = Get-ChildItem -Directory | Where-Object { $_.Name -match "^resources" }
        foreach ($dir in $dirs) {
            $resourceDirs += $dir.Name
        }
    }

    return $resourceDirs | Sort-Object
}

function Get-ResourcePath {
    param([string]$DeviceName)

    if (-not (Test-Path "monkey.jungle")) {
        Write-Host "Error: monkey.jungle file not found!" -ForegroundColor Red
        return
    }

    $deviceInfo = Get-DeviceInfo -DeviceName $DeviceName
    $resourceDirs = Get-ResourceDirectories

    Write-Host ""
    Write-Host "RESOURCE ANALYSIS FOR: $DeviceName" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    Write-Host ""

    Write-Host "Device Information:" -ForegroundColor Yellow
    Write-Host "  Screen Size: $($deviceInfo.ScreenSize)" -ForegroundColor Green
    Write-Host "  Resolution: $($deviceInfo.Resolution)" -ForegroundColor Green
    Write-Host "  Shape: $($deviceInfo.Shape)" -ForegroundColor Green
    Write-Host ""

    Write-Host "Available Resource Directories:" -ForegroundColor Yellow
    foreach ($dir in $resourceDirs) {
        if ($dir -eq "resources") {
            Write-Host "  $dir (base)" -ForegroundColor Green
        } else {
            Write-Host "  $dir" -ForegroundColor White
        }
    }
    Write-Host ""

    # Determine which resource directory would be used
    $matchingDirs = @()

    # Check for exact screen size match
    $exactMatch = $resourceDirs | Where-Object { $_ -match $deviceInfo.ScreenSize }
    if ($exactMatch) {
        $matchingDirs += $exactMatch
    }

    # Check for shape-based match
    $shapeMatch = $resourceDirs | Where-Object { $_ -match $deviceInfo.Shape }
    if ($shapeMatch) {
        $matchingDirs += $shapeMatch
    }

    # Always include base resources
    if ($resourceDirs -contains "resources") {
        $matchingDirs += "resources"
    }

    # Remove duplicates and sort by priority
    $matchingDirs = $matchingDirs | Select-Object -Unique

    Write-Host "Resource Resolution Priority:" -ForegroundColor Yellow
    if ($matchingDirs.Count -gt 0) {
        for ($i = 0; $i -lt $matchingDirs.Count; $i++) {
            $priority = $i + 1
            $dir = $matchingDirs[$i]
            if ($dir -eq "resources") {
                Write-Host "  $priority. $dir (base - always included)" -ForegroundColor Green
            } else {
                Write-Host "  $priority. $dir" -ForegroundColor Cyan
            }
        }
    } else {
        Write-Host "  No matching resource directories found" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "Primary Resource Directory:" -ForegroundColor Yellow
    if ($matchingDirs.Count -gt 0) {
        $primaryDir = $matchingDirs[0]
        Write-Host "  $primaryDir" -ForegroundColor Green

        if (Test-Path $primaryDir) {
            Write-Host ""
            Write-Host "Contents of ${primaryDir}:" -ForegroundColor Yellow
            $contents = Get-ChildItem $primaryDir -Directory -ErrorAction SilentlyContinue
            if ($contents) {
                foreach ($item in $contents) {
                    Write-Host "  📁 $($item.Name)" -ForegroundColor White
                }
            } else {
                Write-Host "  (empty or no subdirectories)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "  resources (fallback)" -ForegroundColor Yellow
    }
    Write-Host ""
}

function List-AllResources {
    Write-Host ""
    Write-Host "ALL RESOURCE DIRECTORIES" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    Write-Host ""

    $resourceDirs = Get-ResourceDirectories

    if ($resourceDirs.Count -eq 0) {
        Write-Host "No resource directories found." -ForegroundColor Yellow
    } else {
        Write-Host "Found $($resourceDirs.Count) resource directories:" -ForegroundColor Green
        Write-Host ""

        foreach ($dir in $resourceDirs) {
            if ($dir -eq "resources") {
                Write-Host "📁 $dir (base)" -ForegroundColor Green
            } elseif ($dir -match "round") {
                Write-Host "📁 $dir (round screen)" -ForegroundColor Cyan
            } elseif ($dir -match "rectangle") {
                Write-Host "📁 $dir (rectangle screen)" -ForegroundColor Magenta
            } elseif ($dir -match "semiround") {
                Write-Host "📁 $dir (semi-round screen)" -ForegroundColor Yellow
            } else {
                Write-Host "📁 $dir" -ForegroundColor White
            }

            # Show contents
            if (Test-Path $dir) {
                $contents = Get-ChildItem $dir -Directory -ErrorAction SilentlyContinue
                if ($contents) {
                    foreach ($item in $contents) {
                        Write-Host "   └── $($item.Name)" -ForegroundColor Gray
                    }
                }
            }
            Write-Host ""
        }
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit
}

if ($ListAll) {
    List-AllResources
    exit
}

if ($Device) {
    Get-ResourcePath -DeviceName $Device
} else {
    Show-Help
}
