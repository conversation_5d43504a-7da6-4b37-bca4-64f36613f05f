@echo off
echo Cleaning bin cache files only (SAFE MODE)...

REM Force kill any processes that might be using files in bin directory
taskkill /f /im "connectiq.exe" 2>nul
taskkill /f /im "simulator.exe" 2>nul

REM Wait a moment for processes to close
timeout /t 1 /nobreak >nul

REM Clean only cache directories, keep main bin structure
if exist "bin" (
    echo Cleaning cache directories...
    if exist "bin\gen" rmdir /s /q "bin\gen"
    if exist "bin\internal-mir" rmdir /s /q "bin\internal-mir"
    if exist "bin\mir" rmdir /s /q "bin\mir"
    if exist "bin\optimized" rmdir /s /q "bin\optimized"

    REM Remove .prg and .debug.xml files but keep directory structure
    if exist "bin\*.prg" del /q "bin\*.prg"
    if exist "bin\*.debug.xml" del /q "bin\*.debug.xml"

    echo Cache files cleaned successfully!
    echo Bin directory structure preserved.
) else (
    echo Bin directory does not exist - creating it...
    mkdir "bin"
    echo Bin directory created.
)

echo.
echo SAFE CLEAN COMPLETED
echo - Cache directories removed
echo - Main bin structure preserved
echo - Ready for fresh build
echo.
pause
