@echo off
echo ========================================
echo MonkeyC Debug Helper for Madre Face
echo ========================================
echo.

:menu
echo Choose an option:
echo 1. Clean Build (Remove all cache)
echo 2. Build for Device
echo 3. Build for Simulator
echo 4. Full Clean + Rebuild
echo 5. Check Build Status
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto clean
if "%choice%"=="2" goto build_device
if "%choice%"=="3" goto build_simulator
if "%choice%"=="4" goto full_rebuild
if "%choice%"=="5" goto check_status
if "%choice%"=="6" goto exit
goto menu

:clean
echo Cleaning build artifacts...
if exist bin rmdir /s /q bin
mkdir bin
echo Clean completed!
echo.
pause
goto menu

:build_device
echo Building for device...
monkeyc -d instinct3amoled45mm -f monkey.jungle -o bin/madreface.prg
echo Build completed!
echo.
pause
goto menu

:build_simulator
echo Building for simulator...
monkeyc -d simulator -f monkey.jungle -o bin/madreface.prg
echo Build completed!
echo.
pause
goto menu

:full_rebuild
echo Performing full clean rebuild...
if exist bin rmdir /s /q bin
mkdir bin
echo Building for device...
monkeyc -d instinct3amoled45mm -f monkey.jungle -o bin/madreface.prg
echo Full rebuild completed!
echo.
pause
goto menu

:check_status
echo Checking build status...
echo.
if exist bin\madreface.prg (
    echo ✓ madreface.prg exists
    dir bin\madreface.prg
) else (
    echo ✗ madreface.prg not found
)
echo.
if exist bin\madreface.prg.debug.xml (
    echo ✓ Debug symbols exist
) else (
    echo ✗ Debug symbols not found
)
echo.
pause
goto menu

:exit
echo Goodbye!
exit
