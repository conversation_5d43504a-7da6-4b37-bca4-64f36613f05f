# MadreView Documentation

## Overview

The `MadreView` class serves as the main watch face view controller, extending `WatchFace` and managing all visual aspects of the Madre watch face. It coordinates rendering, power management, theme handling, and component integration.

## Purpose and Architecture

### View Controller Pattern
MadreView implements the view controller pattern with:
- **Centralized Rendering**: Single point of control for all visual elements
- **Component Coordination**: Manages interactions between drawable components
- **State Management**: Handles power modes, themes, and display states
- **Performance Optimization**: Efficient rendering and resource management

### Key Responsibilities
1. **Watch Face Rendering**: Main drawing coordination and layout management
2. **Power Management**: Sleep mode, burn-in protection, and per-second updates
3. **Theme Management**: Color schemes and visual styling
4. **Component Integration**: Coordination between time, data fields, and goal meters
5. **Settings Propagation**: Distributing configuration changes to components
6. **Performance Optimization**: Drawable caching and efficient updates

## Technical Implementation

### Class Hierarchy
```
Ui.WatchFace
    └── MadreView
```

### Power Management

#### Sleep Mode Detection
- **mIsSleeping**: Tracks current sleep state
- **Adaptive Rendering**: Reduces complexity in low-power mode
- **Component Notification**: Propagates sleep state to child components

#### Burn-in Protection
- **mIsBurnInProtection**: AMOLED display protection state
- **mBurnInProtectionChangedSinceLastDraw**: Change detection for efficient updates
- **Visual Adaptations**: Reduces static elements during burn-in protection

#### Per-Second Updates
- **Device Capability Detection**: `PER_SECOND_UPDATES_SUPPORTED` constant
- **Conditional Updates**: Only on supported devices (not all SDK 2.3.0 devices)
- **Performance Optimization**: Minimal updates for seconds display

### Component Management

#### Drawable Caching
Performance optimization through cached component references:

```monkey-c
private var mDrawables as Dictionary<Symbol, Drawable> = {};
```

**Benefits:**
- Eliminates expensive `findDrawableById()` calls during updates
- Saves ~5ms per update cycle for frequently accessed components
- Reduces CPU usage during per-second updates

**Optimization Strategy:**
- Direct references for most frequently used components (Time, DataFields)
- Dictionary lookup for less frequently accessed components
- Cached during `onLayout()` for entire application lifecycle

### Theme Management

#### Color System
Global color variables managed by the view:

```monkey-c
var gThemeColour;           // Primary theme color
var gMonoLightColour;       // Light monochrome color
var gMonoDarkColour;        // Dark monochrome color
var gBackgroundColour;      // Background color
var gMeterBackgroundColour; // Unfilled meter color
var gHoursColour;           // Hour digits color
var gMinutesColour;         // Minute digits color
```

#### Theme Selection
15 available themes with different color schemes:
- **Dark Themes**: Blue, Pink, Green, Red, etc. with dark backgrounds
- **Light Themes**: Blue, Green, Red with light backgrounds
- **Monochrome Themes**: Light and dark monochrome options
- **Specialty Themes**: Cornflower blue, lemon cream, dayglo orange, etc.

#### Color Overrides
Users can override hours/minutes colors independently:
- **FROM_THEME (-1)**: Use theme color (default)
- **MONO_HIGHLIGHT (-2)**: Use monochrome highlight color
- **MONO (-3)**: Use monochrome color

### Font Management

#### Memory Optimization Strategy
Font selection based on feature usage:

```monkey-c
function updateNormalFont() {
    var city = getPropertyValue("LocalTimeInCity");
    gNormalFont = Ui.loadResource(((city != null) && (city.length() > 0)) ?
        Rez.Fonts.NormalFontCities : Rez.Fonts.NormalFont);
}
```

**Benefits:**
- **NormalFontCities**: ~15-20KB (international character support)
- **NormalFont**: ~8-12KB (basic character set)
- **Memory Savings**: ~7-8KB when city feature not used

### Battery Meter Implementation

#### Visual Design
The `drawBatteryMeter()` function creates a detailed battery indicator:
- **Rounded Rectangle Body**: Stroke outline with rounded corners
- **Terminal Head**: Small rectangle on the right side
- **Proportional Fill**: Based on current battery level
- **Color Coding**: Red (≤10%), Yellow (≤20%), Theme color (>20%)

#### Technical Details
- **Screen Adaptation**: Uses `SCREEN_MULTIPLIER` for different screen sizes
- **Pixel Alignment**: Special handling for screens ≥360px wide
- **Consistency**: Matches system battery calculation using `Math.floor()`

## Goal Meter Integration

### Goal Value Processing
The view processes goal data and passes it to components:

```monkey-c
typedef GoalValues as {
    :current as Number,     // Current progress
    :max as Number,         // Target value
    :isValid as Boolean     // Data validity
};
```

### Goal Types Support
- **GOAL_TYPE_STEPS**: Daily step count
- **GOAL_TYPE_FLOORS_CLIMBED**: Floors climbed
- **GOAL_TYPE_ACTIVE_MINUTES**: Active minutes
- **GOAL_TYPE_BATTERY**: Battery level (custom)
- **GOAL_TYPE_CALORIES**: Calories burned (custom)
- **GOAL_TYPE_OFF**: Disabled goal meter

### Data Flow
```
Activity Monitor → Goal Value Calculation → Goal Meter Updates → Visual Rendering
```

## Settings Management

### Settings Change Handling
```monkey-c
function onSettingsChanged() {
    mSettingsChangedSinceLastDraw = true;  // Deferred processing flag
    updateNormalFont();                    // Font optimization
    updateThemeColours();                  // Theme application
    updateHoursMinutesColours();          // Color overrides
    checkPendingWebRequests();            // Background data needs
}
```

### Deferred Processing
Settings changes are processed during the next `onUpdate()` cycle because:
- Watch may be in 1Hz (low power) mode
- Immediate full screen updates not possible on real hardware
- `Ui.requestUpdate()` doesn't work reliably in 1Hz mode

## Performance Optimizations

### Rendering Efficiency
- **Drawable Caching**: Eliminates expensive component lookups
- **State Tracking**: Only updates when necessary
- **Partial Updates**: Per-second updates for supported devices
- **Memory Management**: Font optimization based on feature usage

### Update Strategies
- **Full Updates**: Complete redraw when settings change or wake from sleep
- **Partial Updates**: Seconds display only on capable devices
- **Conditional Rendering**: Skip unnecessary operations based on state

### Resource Management
- **Font Loading**: Dynamic loading based on feature requirements
- **Color Calculation**: Cached theme colors to avoid repeated calculations
- **Component References**: Cached to avoid string-based lookups

## Integration Points

### Application Integration
- **Settings Propagation**: Receives and processes settings from MandreApp
- **Background Data**: Coordinates with background service results
- **Component Management**: Manages all drawable components

### Component Integration
- **DataFields**: Manages data field display and updates
- **GoalMeter**: Coordinates goal meter values and rendering
- **DataArea**: Controls central information display
- **Time Display**: Manages time formatting and display

## Best Practices

### Performance
✅ **Recommended:**
- Cache drawable references during initialization
- Use deferred settings processing for low-power compatibility
- Implement partial updates for per-second displays
- Optimize font loading based on feature usage

### Theme Management
✅ **Recommended:**
- Update all theme colors together in single operation
- Provide user color override options
- Test themes on both light and dark backgrounds
- Ensure sufficient contrast for readability

### Power Management
✅ **Recommended:**
- Detect and adapt to sleep mode changes
- Implement burn-in protection for AMOLED displays
- Use per-second updates only when supported
- Minimize rendering complexity in low-power modes

## Troubleshooting

### Common Issues

#### Settings Not Applying Immediately
**Symptoms:** Settings changes don't appear until next update
**Cause:** Normal behavior due to low-power mode limitations
**Solution:** Settings are applied on next natural update cycle

#### Performance Issues
**Symptoms:** Slow rendering or high battery usage
**Causes:** Inefficient drawable lookups or excessive updates
**Solutions:** Verify drawable caching and update frequency

#### Theme Colors Not Updating
**Symptoms:** Colors don't change when theme is modified
**Causes:** Theme color update not triggered properly
**Solutions:** Check `updateThemeColours()` call in settings change handler

#### Font Loading Issues
**Symptoms:** Text not displaying or memory errors
**Causes:** Font optimization not working correctly
**Solutions:** Verify `updateNormalFont()` logic and resource availability

### Debugging Strategies
1. **Rendering Flow**: Trace update cycles and component rendering
2. **Performance**: Monitor drawable cache usage and update frequency
3. **Theme System**: Verify color calculations and propagation
4. **Power Management**: Check sleep mode detection and adaptation
