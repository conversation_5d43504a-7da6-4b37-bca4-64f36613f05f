
# ============================================================================
# Garmin Connect IQ Project .gitignore
# ============================================================================

# ============================================================================
# SECURITY CRITICAL: Developer Keys (NEVER COMMIT THESE!)
# ============================================================================
# Private keys for signing Connect IQ applications
developer_key.der
developer_key.pem
developer_key
*.der
*.pem
*_key.*
*key.*
test_key.*
my_key.*
backup_key.*
*_backup.*

# ============================================================================
# Connect IQ Build Artifacts
# ============================================================================
# Build directories
build/
bin/
gen/

# Compiled Connect IQ files
*.prg
*.prg.debug.xml
*.iq
*.debug.xml
*.symbols.xml

# Connect IQ build logs and intermediate files
log.zip
*.mir
external-mir/
internal-mir/
mir/
optimized/

# ============================================================================
# IDE and Development Tools
# ============================================================================
# VS Code
.vscode/settings.json
.vscode/launch.json.bak

# Other IDEs
.idea/
*.sublime-workspace
*.swp
*.swo
*~

# ============================================================================
# Operating System Files
# ============================================================================
# macOS
.DS_Store
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
.directory
.Trash-*

# ============================================================================
# Temporary and Log Files
# ============================================================================
*.tmp
*.temp
*.log
*.bak
*.orig

# ============================================================================
# Configuration and Environment
# ============================================================================
# Environment variables and secrets
.env
.env.local
config.json
settings.json
secrets.json

# ============================================================================
# Development Dependencies (if any)
# ============================================================================
# Node.js
node_modules/
npm-debug.log*

# Python
__pycache__/
*.pyc
venv/
.venv/

# ============================================================================
# Test and Debug Files
# ============================================================================
test_*
temp_*
debug_*
coverage/
