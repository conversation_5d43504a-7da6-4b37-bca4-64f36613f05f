@echo off
echo Restoring bin directory structure...

REM Create bin directory if it doesn't exist
if not exist "bin" (
    echo Creating bin directory...
    mkdir "bin"
)

REM Create subdirectories that are typically needed
if not exist "bin\gen" mkdir "bin\gen"
if not exist "bin\internal-mir" mkdir "bin\internal-mir"
if not exist "bin\mir" mkdir "bin\mir"

echo Bin directory structure restored!
echo.
echo Now try building your project again:
echo 1. Press F5 to debug
echo 2. Or use Ctrl+Shift+P and run "Tasks: Run Task" -> "buildForDevice"
echo.
pause
