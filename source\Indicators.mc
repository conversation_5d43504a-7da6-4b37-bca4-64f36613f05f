// Import required Garmin Connect IQ SDK modules for indicators functionality
using Toybox.WatchUi as Ui;        // User interface and drawable components
using Toybox.Graphics as Gfx;      // Graphics drawing and color management
using Toybox.System as Sys;        // System services and device settings
using Toybox.Application as App;   // Application context and property access

import Toybox.Lang;

/**
 * Indicators class manages and renders status indicators on the watch face.
 *
 * This class provides a flexible system for displaying various device status indicators
 * such as Bluetooth connectivity, notifications, alarms, and battery level. It supports
 * both horizontal and vertical layouts with configurable spacing and positioning.
 *
 * Key Features:
 * - Configurable indicator types (Bluetooth, alarms, notifications, battery)
 * - Flexible layout system (1-3 indicators with automatic spacing)
 * - Smart indicator logic (e.g., notifications take priority over Bluetooth when both available)
 * - Visual state indication (active/inactive states with different colors)
 * - Battery meter integration with custom sizing
 * - Conditional compilation for horizontal vs vertical layouts
 *
 * Layout Support:
 * - Horizontal layout: Indicators arranged left-to-right with X-axis spacing
 * - Vertical layout: Indicators arranged top-to-bottom with Y-axis spacing
 * - Automatic centering and spacing calculation based on indicator count
 *
 * Indicator Types:
 * - Bluetooth: Shows phone connection status
 * - Alarms: Shows if any alarms are set
 * - Notifications: Shows if unread notifications exist
 * - Bluetooth/Notifications: Smart indicator that prioritizes notifications over Bluetooth
 * - Battery: Shows battery level as a graphical meter
 */
class Indicators extends Ui.Drawable {

	// Layout and positioning properties
	private var mSpacing;           // Distance between indicators (X or Y axis depending on layout)
	private var mBatteryWidth;      // Width of battery meter indicator

	// Indicator type configuration (set from user preferences)
	private var mIndicator1Type;    // Type of first indicator (0-4)
	private var mIndicator2Type;    // Type of second indicator (0-4)
	private var mIndicator3Type;    // Type of third indicator (0-4)

	/**
	 * Indicator types enumeration (commented out but values used directly for performance)
	 * These correspond to user-selectable indicator types in watch face settings:
	 *
	 * INDICATOR_TYPE_BLUETOOTH = 0                 // Bluetooth connection status
	 * INDICATOR_TYPE_ALARMS = 1                    // Active alarms indicator
	 * INDICATOR_TYPE_NOTIFICATIONS = 2             // Unread notifications indicator
	 * INDICATOR_TYPE_BLUETOOTH_OR_NOTIFICATIONS = 3 // Smart indicator (notifications priority)
	 * INDICATOR_TYPE_BATTERY = 4                   // Battery level meter
	 */
	// private enum /* INDICATOR_TYPES */ {
	// 	INDICATOR_TYPE_BLUETOOTH,                    // 0: Phone connection status
	// 	INDICATOR_TYPE_ALARMS,                       // 1: Active alarms indicator
	// 	INDICATOR_TYPE_NOTIFICATIONS,                // 2: Unread notifications count
	// 	INDICATOR_TYPE_BLUETOOTH_OR_NOTIFICATIONS,   // 3: Smart indicator with priority logic
	// 	INDICATOR_TYPE_BATTERY                       // 4: Battery level graphical meter
	// }

	/**
	 * Type definition for Indicators constructor parameters.
	 * Defines the layout properties needed to position and space the indicators.
	 */
	typedef IndicatorsParams as {
		:locX as Number,        // X coordinate of the center indicator position
		:locY as Number,        // Y coordinate of the center indicator position
		:spacingX as Number,    // Horizontal spacing between indicators (for horizontal layout)
		:spacingY as Number,    // Vertical spacing between indicators (for vertical layout)
		:batteryWidth as Number // Width of battery meter indicator in pixels
	};

	/**
	 * Constructor for Indicators drawable component.
	 * Initializes the indicators with specified layout parameters and loads current settings.
	 *
	 * The constructor automatically determines whether to use horizontal or vertical spacing
	 * based on which parameter is provided, supporting both layout orientations.
	 *
	 * @param params IndicatorsParams containing layout configuration and battery meter size
	 */
	function initialize(params as IndicatorsParams) {
		Drawable.initialize(params);

		// Determine spacing based on layout orientation
		// Horizontal layout uses spacingX, vertical layout uses spacingY
		if (params[:spacingX] != null) {
			mSpacing = params[:spacingX];    // Horizontal layout spacing
		} else {
			mSpacing = params[:spacingY];    // Vertical layout spacing
		}

		// Store battery meter dimensions for battery indicator type
		mBatteryWidth = params[:batteryWidth];

		// Load current indicator type settings from user preferences
		onSettingsChanged();
	}

	/**
	 * Updates cached indicator type settings when watch face settings change.
	 * This method is called when the user modifies indicator settings and ensures
	 * the indicators reflect the current user preferences.
	 *
	 * Indicator Types:
	 * - 0: Bluetooth connection status
	 * - 1: Active alarms indicator
	 * - 2: Unread notifications indicator
	 * - 3: Smart Bluetooth/Notifications indicator
	 * - 4: Battery level meter
	 */
	function onSettingsChanged() {
		mIndicator1Type = getPropertyValue("Indicator1Type");
		mIndicator2Type = getPropertyValue("Indicator2Type");
		mIndicator3Type = getPropertyValue("Indicator3Type");
	}

	/**
	 * Main draw method for rendering all configured indicators.
	 *
	 * This method determines how many indicators to display based on user settings
	 * and delegates to the appropriate layout-specific drawing method. The number
	 * of indicators is validated to prevent errors from corrupted settings.
	 *
	 * Layout Selection:
	 * The actual drawing is handled by layout-specific methods that are conditionally
	 * compiled based on the target device's screen orientation and layout requirements.
	 *
	 * @param dc Graphics drawing context for rendering the indicators
	 */
	function draw(dc) {

		// Issue #123: Protect against null or unexpected type (e.g. String) from corrupted settings
		var indicatorCount = App.getApp().getIntProperty("IndicatorCount", 1);

		// Legacy layout selection code (replaced with conditional compilation)
		// Different devices use different layout orientations:
		// - Horizontal layout: rectangle-148x205, rectangle-320x360
		// - Vertical layout: most other screen shapes and sizes
		// if (mIsHorizontal) {
		// 	drawHorizontal(dc, indicatorCount);
		// } else {
		// 	drawVertical(dc, indicatorCount);
		// }

		// Delegate to layout-specific drawing method (conditionally compiled)
		drawIndicators(dc, indicatorCount);
	}

	/**
	 * HORIZONTAL LAYOUT: Draws indicators arranged left-to-right.
	 *
	 * This method handles horizontal indicator layouts where indicators are positioned
	 * along the X-axis with the center indicator at locX and others spaced horizontally.
	 * Used for rectangular screens with landscape orientation.
	 *
	 * Layout Patterns:
	 * - 1 indicator: [•] (centered at locX)
	 * - 2 indicators: [• •] (centered around locX with half-spacing)
	 * - 3 indicators: [• • •] (center at locX, others at ±mSpacing)
	 *
	 * @param dc Graphics drawing context
	 * @param indicatorCount Number of indicators to display (1-3)
	 */
	(:horizontal_indicators)
	// function drawHorizontal(dc, indicatorCount) {
	function drawIndicators(dc, indicatorCount) {
		if (indicatorCount == 3) {
			// Three indicators: left, center, right positioning
			drawIndicator(dc, mIndicator1Type, locX - mSpacing, locY);
			drawIndicator(dc, mIndicator2Type, locX, locY);
			drawIndicator(dc, mIndicator3Type, locX + mSpacing, locY);
		} else if (indicatorCount == 2) {
			// Two indicators: positioned at half-spacing from center for balance
			drawIndicator(dc, mIndicator1Type, locX - (mSpacing / 2), locY);
			drawIndicator(dc, mIndicator2Type, locX + (mSpacing / 2), locY);
		} else if (indicatorCount == 1) {
			// Single indicator: centered at locX
			drawIndicator(dc, mIndicator1Type, locX, locY);
		}
	}

	/**
	 * VERTICAL LAYOUT: Draws indicators arranged top-to-bottom.
	 *
	 * This method handles vertical indicator layouts where indicators are positioned
	 * along the Y-axis with the center indicator at locY and others spaced vertically.
	 * Used for most watch screens including round and square orientations.
	 *
	 * Layout Patterns:
	 * - 1 indicator: [•] (centered at locY)
	 * - 2 indicators: [•] (centered around locY with half-spacing)
	 *                 [•]
	 * - 3 indicators: [•] (center at locY, others at ±mSpacing)
	 *                 [•]
	 *                 [•]
	 *
	 * @param dc Graphics drawing context
	 * @param indicatorCount Number of indicators to display (1-3)
	 */
	(:vertical_indicators)
	// function drawVertical(dc, indicatorCount) {
	function drawIndicators(dc, indicatorCount) {
		if (indicatorCount == 3) {
			// Three indicators: top, center, bottom positioning
			drawIndicator(dc, mIndicator1Type, locX, locY - mSpacing);
			drawIndicator(dc, mIndicator2Type, locX, locY);
			drawIndicator(dc, mIndicator3Type, locX, locY + mSpacing);
		} else if (indicatorCount == 2) {
			// Two indicators: positioned at half-spacing from center for balance
			drawIndicator(dc, mIndicator1Type, locX, locY - (mSpacing / 2));
			drawIndicator(dc, mIndicator2Type, locX, locY + (mSpacing / 2));
		} else if (indicatorCount == 1) {
			// Single indicator: centered at locY
			drawIndicator(dc, mIndicator1Type, locX, locY);
		}
	}

	/**
	 * Draws an individual indicator at the specified position.
	 *
	 * This method handles the rendering of different indicator types, including special
	 * logic for smart indicators and battery meters. It determines the appropriate
	 * visual state (active/inactive) and renders the corresponding icon or graphic.
	 *
	 * Indicator Processing:
	 * 1. Battery indicators: Rendered as graphical meters using drawBatteryMeter()
	 * 2. Smart indicators: Resolved to specific type based on device state
	 * 3. Standard indicators: Rendered as icons with state-based coloring
	 *
	 * Visual States:
	 * - Active state: Rendered in theme color (gThemeColour)
	 * - Inactive state: Rendered in muted color (gMeterBackgroundColour)
	 *
	 * @param dc Graphics drawing context
	 * @param indicatorType Type of indicator to draw (0-4)
	 * @param x X coordinate for indicator center
	 * @param y Y coordinate for indicator center
	 */
	function drawIndicator(dc, indicatorType, x, y) {

		// Special case: Battery indicator uses graphical meter instead of icon
		if (indicatorType == 4 /* INDICATOR_TYPE_BATTERY */) {
			drawBatteryMeter(dc, x, y, mBatteryWidth, mBatteryWidth / 2);
			return;
		}

		// Get current device settings for indicator state evaluation
		var settings = Sys.getDeviceSettings();

		// Special case: Smart Bluetooth/Notifications indicator
		// Shows notifications when connected and notifications exist, otherwise shows Bluetooth status
		if (indicatorType == 3 /* INDICATOR_TYPE_BLUETOOTH_OR_NOTIFICATIONS */) {
			if (settings.phoneConnected && (settings.notificationCount > 0)) {
				indicatorType = 2; // Switch to INDICATOR_TYPE_NOTIFICATIONS
			} else {
				indicatorType = 0; // Switch to INDICATOR_TYPE_BLUETOOTH
			}
		}

		// Determine indicator state based on device settings
		// Array lookup provides efficient state evaluation for each indicator type
		var value = [
			/* INDICATOR_TYPE_BLUETOOTH */     settings.phoneConnected,        // True if phone is connected
			/* INDICATOR_TYPE_ALARMS */       settings.alarmCount > 0,        // True if any alarms are set
			/* INDICATOR_TYPE_NOTIFICATIONS */ settings.notificationCount > 0  // True if unread notifications exist
		][indicatorType];

		// Set color based on indicator state: active (theme color) or inactive (muted color)
		dc.setColor(value ? gThemeColour : gMeterBackgroundColour, Graphics.COLOR_TRANSPARENT);

		// Draw the indicator icon using the appropriate font character
		dc.drawText(
			x,                                                          // X position (center)
			y,                                                          // Y position (center)
			gIconsFont,                                                // Icon font
			["8", ":", "5"][indicatorType],                            // Icon character: Bluetooth, Alarms, Notifications
			Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER // Center alignment
		);
	}
}
