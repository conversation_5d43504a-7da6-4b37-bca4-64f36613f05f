// Import required Garmin Connect IQ SDK modules for watch face functionality
using Toybox.WatchUi as Ui;
using Toybox.Graphics as Gfx;
using Toybox.System as Sys;
using Toybox.Application as App;
using Toybox.Activity as Activity;
using Toybox.ActivityMonitor as ActivityMonitor;
using Toybox.SensorHistory as SensorHistory;

using Toybox.Time;
using Toybox.Time.Gregorian;

import Toybox.Lang;

/**
 * Enumeration defining all available data field types that can be displayed on the watch face.
 * These correspond to different types of information that can be shown in the data field areas.
 */
enum /* FIELD_TYPES */ {
	// Pseudo-fields: Special field types that don't correspond directly to user-selectable properties
	FIELD_TYPE_SUNRISE = -1,        // Sunrise time (used internally when sunrise is next instead of sunset)
	//FIELD_TYPE_SUNSET = -2,       // Sunset time (commented out, handled by sunrise/sunset field)

	// Real fields: These correspond to user-selectable data field types in the watch face settings
	FIELD_TYPE_HEART_RATE = 0,      // Current heart rate from sensor
	FIELD_TYPE_BATTERY,             // Battery percentage with % symbol
	FIELD_TYPE_NOTIFICATIONS,       // Number of unread notifications
	FIELD_TYPE_CALORIES,            // Calories burned today
	FIELD_TYPE_DISTANCE,            // Distance traveled today (km/mi)
	FIELD_TYPE_ALARMS,              // Number of active alarms
	FIELD_TYPE_ALTITUDE,            // Current altitude (m/ft)
	FIELD_TYPE_TEMPERATURE,         // Current temperature from sensor
	FIELD_TYPE_BATTERY_HIDE_PERCENT,// Battery level without % symbol
	FIELD_TYPE_HR_LIVE_5S,          // Live heart rate with 5-second update indicator
	FIELD_TYPE_SUNRISE_SUNSET,      // Next sunrise or sunset time
	FIELD_TYPE_WEATHER,             // Current weather temperature and icon
	FIELD_TYPE_PRESSURE,            // Atmospheric pressure (mbar)
	FIELD_TYPE_HUMIDITY             // Humidity percentage from weather data
}

/**
 * Type definition for the return value of field type value retrieval functions.
 * Contains the formatted string value to display for a data field.
 */
typedef FieldTypeValue as {
	:value as String
};

/**
 * DataFields class handles the rendering and management of data fields displayed on the watch face.
 * This class is responsible for:
 * - Drawing data field values and their corresponding icons
 * - Managing different field layouts (1, 2, or 3 fields)
 * - Handling live heart rate updates with visual indicators
 * - Retrieving and formatting data from various sensors and system information
 * - Managing weather icons and their day/night variants
 */
class DataFields extends Ui.Drawable {

	// Layout positioning variables
	private var mLeft;              // Left boundary of data fields area
	private var mRight;             // Right boundary of data fields area
	private var mTop;               // Top boundary of data fields area (for icons)
	private var mBottom;            // Bottom boundary of data fields area (for values)

	// Weather icon management
	private var mWeatherIconsFont;  // Currently loaded weather icons font
	private var mWeatherIconsSubset = null; // Current weather icon subset: null, "d" for day, "n" for night

	// Field configuration and state
	private var mFieldCount;        // Number of data fields to display (0-3)
	private var mHasLiveHR = false; // Flag indicating if live HR field is currently shown
	private var mWasHRAvailable = false; // HR availability state from last full draw cycle (high power mode)
	private var mMaxFieldLength;    // Maximum characters allowed per field based on field count
	private var mBatteryWidth;      // Width of battery meter graphic

	// Unit conversion constants (commented out but kept for reference)
	// private const CM_PER_KM = 100000;  // Centimeters per kilometer
	// private const MI_PER_KM = 0.621371; // Miles per kilometer conversion factor
	// private const FT_PER_M = 3.28084;   // Feet per meter conversion factor

	/**
	 * Type definition for DataFields constructor parameters.
	 * Defines the layout boundaries and battery meter dimensions.
	 */
	typedef DataFieldsParams as {
		:left as Number,        // Left boundary coordinate
		:right as Number,       // Right boundary coordinate
		:top as Number,         // Top boundary coordinate (icon area)
		:bottom as Number,      // Bottom boundary coordinate (value area)
		:batteryWidth as Number // Width of battery meter graphic
	};

	/**
	 * Constructor for DataFields drawable component.
	 * Initializes the data fields area with specified layout parameters and triggers settings initialization.
	 *
	 * @param params DataFieldsParams containing layout boundaries and battery meter width
	 */
	function initialize(params as DataFieldsParams) {
		Drawable.initialize(params);

		// Store layout boundaries for field positioning
		mLeft = params[:left];
		mRight = params[:right];
		mTop = params[:top];
		mBottom = params[:bottom];

		// Store battery meter dimensions
		mBatteryWidth = params[:batteryWidth];

		// Initialize field count and maximum field length based on current settings
		onSettingsChanged();
	}

	/**
	 * Updates cached settings when watch face settings change.
	 * This method is called when the user modifies data field settings and handles:
	 * - Field count configuration (0-3 fields)
	 * - Maximum field length calculation based on field count
	 * - Live heart rate field detection
	 * - Weather icon font management
	 */
	function onSettingsChanged() {

		// Issue #123: Protect against null or unexpected type (e.g. String) from corrupted settings
		mFieldCount = App.getApp().getIntProperty("FieldCount", 3);

		/* Legacy switch statement approach (replaced with array lookup for efficiency):
		switch (mFieldCount) {
			case 3:
				mMaxFieldLength = 4;  // 3 fields = 4 chars max each
				break;
			case 2:
				mMaxFieldLength = 6;  // 2 fields = 6 chars max each
				break;
			case 1:
				mMaxFieldLength = 8;  // 1 field = 8 chars max
				break;
		} */

		// Issue #116: Handle FieldCount = 0 correctly using array lookup
		// Array index corresponds to field count, value is max characters per field
		mMaxFieldLength = [0, 8, 6, 4][mFieldCount];

		// Check if live heart rate field is currently configured
		mHasLiveHR = App.getApp().hasField(FIELD_TYPE_HR_LIVE_5S);

		// Reset weather icon font if weather field is not configured (memory optimization)
		if (!App.getApp().hasField(FIELD_TYPE_WEATHER)) {
			mWeatherIconsFont = null;
			mWeatherIconsSubset = null;
		}
	}

	/**
	 * Main draw method called during full screen updates.
	 * Delegates to update() method with full update flag.
	 *
	 * @param dc Graphics drawing context
	 */
	function draw(dc) {
		update(dc, /* isPartialUpdate */ false);
	}

	/**
	 * Updates data fields display, handling both full and partial screen updates.
	 * Partial updates are used for live heart rate indicators to minimize power consumption.
	 *
	 * @param dc Graphics drawing context
	 * @param isPartialUpdate Boolean indicating if this is a partial screen update
	 */
	function update(dc, isPartialUpdate) {
		// Skip partial updates if no live heart rate field is configured
		if (isPartialUpdate && !mHasLiveHR) {
			return;
		}

		// Get current field type configuration from application
		var fieldTypes = App.getApp().mFieldTypes;

		// Draw fields based on configured field count with appropriate positioning
		switch (mFieldCount) {
			case 3:
				// Three fields: left, center, right positioning
				drawDataField(dc, isPartialUpdate, fieldTypes[0], mLeft);
				drawDataField(dc, isPartialUpdate, fieldTypes[1], (mRight + mLeft) / 2);
				drawDataField(dc, isPartialUpdate, fieldTypes[2], mRight);
				break;
			case 2:
				// Two fields: positioned at 15% and 85% of the width for balanced spacing
				drawDataField(dc, isPartialUpdate, fieldTypes[0], mLeft + ((mRight - mLeft) * 0.15));
				drawDataField(dc, isPartialUpdate, fieldTypes[1], mLeft + ((mRight - mLeft) * 0.85));
				break;
			case 1:
				// Single field: centered horizontally
				drawDataField(dc, isPartialUpdate, fieldTypes[0], (mRight + mLeft) / 2);
				break;
			/*
			case 0:
				// No fields to draw
				break;
			*/
		}
	}

	// Both regular and small icon fonts use same spot size for easier optimization
	// Live heart rate spot radius constant (commented out but kept for reference)
	//private const LIVE_HR_SPOT_RADIUS = 3;

	/**
	 * Draws an individual data field including its icon and value.
	 * Handles special cases for heart rate indicators, battery meters, and weather icons.
	 * Supports both full and partial updates for power optimization.
	 *
	 * @param dc Graphics drawing context
	 * @param isPartialUpdate Boolean indicating if this is a partial screen update
	 * @param fieldType The type of data field to draw (from FIELD_TYPES enum)
	 * @param x Horizontal center position for the field
	 */
	private function drawDataField(dc, isPartialUpdate, fieldType, x) {

		// Optimize partial updates: only update live HR spot every 5 seconds
		var isLiveHeartRate = (fieldType == FIELD_TYPE_HR_LIVE_5S);
		var seconds = Sys.getClockTime().sec;
		if (isPartialUpdate && (!isLiveHeartRate || (seconds % 5))) {
			return;
		}

		// Determine if live heart rate spot indicator should be shown based on current time
		var showLiveHRSpot = false;
		var isHeartRate = ((fieldType == FIELD_TYPE_HEART_RATE) || isLiveHeartRate);
		if (isHeartRate) {

			// High power mode: Alternate spot every second (0 on, 1 off, 2 on, etc.)
			if (!App.getApp().getView().isSleeping()) {
				showLiveHRSpot = ((seconds % 2) == 0);

			// Low power mode: Different timing patterns for power conservation
			} else {

				// Live HR: Show spot for 5 seconds, hide for 5 seconds (0-4 on, 5-9 off, 10-14 on, etc.)
				if (isLiveHeartRate) {
					showLiveHRSpot = (((seconds / 5) % 2) == 0);

				// Normal HR: Turn off spot when entering sleep mode
				} else {
					showLiveHRSpot = false;
				}
			}
		}

		// Step 1: Draw field value first (text overlaps icon area for better visual hierarchy)
		var result = getValueForFieldType(fieldType);
		var value = result["value"];

		// Optimization: Skip partial update if live HR remains unavailable since last check
		var isHRAvailable = isHeartRate && (value.length() != 0);
		if (isPartialUpdate && !isHRAvailable && !mWasHRAvailable) {
			return;
		}

		// Issue #34: Set up clipping for live HR value updates to prevent visual artifacts
		// Optimization: Hard-coded clip dimensions work because all watches use same label font
		dc.setColor(gMonoLightColour, gBackgroundColour);

		// Apply clipping rectangle for partial updates to limit drawing area
		if (isPartialUpdate) {
			dc.setClip(
				x - 11,        // Left edge of clip area
				mBottom - 4,   // Top edge of clip area
				25,            // Width of clip area
				12);           // Height of clip area

			dc.clear();
		}

		// Draw the field value text centered at the bottom of the field area
		dc.drawText(
			x,                                                          // X position (horizontal center)
			mBottom,                                                    // Y position (bottom of field area)
			gNormalFont,                                               // Font to use
			value,                                                     // Text to display
			Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER // Center alignment
		);

		// Step 2: Draw field icon

		// Determine icon color: grey out if no value available, use theme color otherwise
		// Issue #37: Battery icon is never greyed out (getValueForFieldType() returns empty string for BATTERY_HIDE_PERCENT)
		var colour = (value.length() == 0) ? gMeterBackgroundColour : gThemeColour;

		// Special case: Battery meter (both regular and hide-percent variants)
		if ((fieldType == FIELD_TYPE_BATTERY) || (fieldType == FIELD_TYPE_BATTERY_HIDE_PERCENT)) {
			drawBatteryMeter(dc, x, mTop, mBatteryWidth, mBatteryWidth / 2);

		// Special case: Live HR in low power mode (Issue #34)
		} else if (isLiveHeartRate && isPartialUpdate) {

			// Handle HR availability state changes during low power mode
			// If availability changes, we need to redraw the full heart icon
			if (isHRAvailable != mWasHRAvailable) {
				mWasHRAvailable = isHRAvailable;

				// Calculate heart icon dimensions and set up clipping area
				var heartDims = dc.getTextDimensions("3", gIconsFont) as Array<Number>; // Heart icon character
				dc.setClip(
					x - (heartDims[0] / 2),     // Left edge of heart icon
					mTop - (heartDims[1] / 2),  // Top edge of heart icon
					heartDims[0] + 1,           // Width with 1px buffer
					heartDims[1] + 1);          // Height with 1px buffer

				// Draw the full heart icon with appropriate color
				dc.setColor(colour, gBackgroundColour);
				dc.drawText(
					x,                                                          // X position (center)
					mTop,                                                       // Y position (top of field)
					gIconsFont,                                                // Icon font
					"3",                                                       // Heart icon character
					Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER // Center alignment
				);
			}

			// Set up clipping area for the live HR spot indicator
			dc.setClip(
				x - 3,  /* LIVE_HR_SPOT_RADIUS */ // Left edge of spot area
				mTop - 3, /* LIVE_HR_SPOT_RADIUS */ // Top edge of spot area
				7,      // Width: (2 * LIVE_HR_SPOT_RADIUS) + 1
				7);     // Height: (2 * LIVE_HR_SPOT_RADIUS) + 1

			// Draw the live HR spot indicator if conditions are met
			// Use font character instead of fillCircle() for better anti-aliasing
			var spotChar;
			if (showLiveHRSpot && (Activity.getActivityInfo().currentHeartRate != null)) {
				// Show bright spot to indicate live HR activity
				dc.setColor(gBackgroundColour, Graphics.COLOR_TRANSPARENT);
				spotChar = "="; // Live HR spot character
			} else {
				// Hide spot by drawing heart icon over it
				dc.setColor(colour, gBackgroundColour);
				spotChar = "3"; // Heart icon character
			}

			// Draw the selected character (spot or heart)
			dc.drawText(
				x,                                                          // X position (center)
				mTop,                                                       // Y position (top of field)
				gIconsFont,                                                // Icon font
				spotChar,                                                  // Character to draw
				Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER // Center alignment
			);

		// Handle all other icon types (non-battery, non-live-HR)
		} else {

			// Issue #19: Show sunrise icon instead of default sunset icon when sunrise is next
			if ((fieldType == FIELD_TYPE_SUNRISE_SUNSET) && (result["isSunriseNext"] == true)) {
				fieldType = FIELD_TYPE_SUNRISE;
			}

			var font;
			var icon;

			// Special handling for weather icons
			if (fieldType == FIELD_TYPE_WEATHER) {

				// Issue #83: Dynamic loading/unloading of day/night weather icons font for memory optimization
				// Extract day/night indicator from weather icon code (3rd character: 'd' or 'n')
				var weatherIconsSubset = (result["weatherIcon"] as String).substring(2, 3);

				// Load appropriate font only if subset has changed since last draw
				if (!weatherIconsSubset.equals(mWeatherIconsSubset)) {
					mWeatherIconsSubset = weatherIconsSubset;
					mWeatherIconsFont = Ui.loadResource((mWeatherIconsSubset.equals("d")) ?
						Rez.Fonts.WeatherIconsFontDay : Rez.Fonts.WeatherIconsFontNight);
				}
				font = mWeatherIconsFont;

				// Issue #89: Map OpenWeatherMap icon codes to ASCII characters to avoid Unicode issues
				// Day icons start from "A", night icons start from "a", "I" is shared for broken clouds
				// Reference: https://openweathermap.org/weather-conditions
				icon = {
					// Weather Code => ASCII Char /* Original Unicode */ // Description
					"01d" => "H" /* 61453 */, "01n" => "f" /* 61486 */, // clear sky
					"02d" => "G" /* 61452 */, "02n" => "g" /* 61569 */, // few clouds
					"03d" => "B" /* 61442 */, "03n" => "h" /* 61574 */, // scattered clouds
					"04d" => "I" /* 61459 */, "04n" => "I" /* 61459 */, // broken clouds (shared icon)
					"09d" => "E" /* 61449 */, "09n" => "d" /* 61481 */, // shower rain
					"10d" => "D" /* 61448 */, "10n" => "c" /* 61480 */, // rain
					"11d" => "C" /* 61445 */, "11n" => "b" /* 61477 */, // thunderstorm
					"13d" => "F" /* 61450 */, "13n" => "e" /* 61482 */, // snow
					"50d" => "A" /* 61441 */, "50n" => "a" /* 61475 */, // mist
				}[result["weatherIcon"]];

			} else {
				// Use standard icon font for all non-weather fields
				font = gIconsFont;

				// Map field types to their corresponding icon font characters
				icon = {
					// Sun/time related icons
					FIELD_TYPE_SUNRISE => ">",              // Sunrise icon
					// FIELD_TYPE_SUNSET => "?",            // Sunset icon (unused, handled by sunrise/sunset field)

					// Health and fitness icons
					FIELD_TYPE_HEART_RATE => "3",           // Heart icon for regular HR
					FIELD_TYPE_HR_LIVE_5S => "3",           // Heart icon for live HR (same as regular)
					// FIELD_TYPE_BATTERY => "4",           // Battery icon (handled separately by drawBatteryMeter)
					// FIELD_TYPE_BATTERY_HIDE_PERCENT => "4", // Battery icon (handled separately)
					FIELD_TYPE_NOTIFICATIONS => "5",        // Notification bell icon
					FIELD_TYPE_CALORIES => "6",             // Calories/flame icon
					FIELD_TYPE_DISTANCE => "7",             // Distance/footsteps icon
					FIELD_TYPE_ALARMS => ":",               // Alarm clock icon

					// Environmental sensors
					FIELD_TYPE_ALTITUDE => ";",             // Mountain/altitude icon
					FIELD_TYPE_TEMPERATURE => "<",          // Thermometer icon
					// FIELD_TYPE_WEATHER => "<",           // Weather icon (handled separately above)
					// LIVE_HR_SPOT => "=",                 // Live HR spot indicator (handled separately)

					// Combined and additional fields
					FIELD_TYPE_SUNRISE_SUNSET => "?",       // Sunset icon (default, changes to sunrise when appropriate)
					FIELD_TYPE_PRESSURE => "@",             // Barometer/pressure icon
					FIELD_TYPE_HUMIDITY => "A",             // Humidity/water drop icon
				}[fieldType];
			}

			// Draw the icon with appropriate color and transparency
			dc.setColor(colour, Graphics.COLOR_TRANSPARENT);
			dc.drawText(
				x,                                                          // X position (center)
				mTop,                                                       // Y position (top of field)
				font,                                                      // Font (weather or standard icons)
				icon,                                                      // Icon character to draw
				Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER // Center alignment
			);

			// Additional processing for heart rate fields
			if (isHeartRate) {

				// Issue #34: Save HR availability state for next partial update cycle
				mWasHRAvailable = isHRAvailable;

				// Issue #34: Draw live HR spot in high power mode if conditions are met
				if (showLiveHRSpot && (Activity.getActivityInfo().currentHeartRate != null)) {
					dc.setColor(gBackgroundColour, Graphics.COLOR_TRANSPARENT);
					dc.drawText(
						x,                                                          // X position (center)
						mTop,                                                       // Y position (top of field)
						gIconsFont,                                                // Standard icon font
						"=",                                                       // Live HR spot character
						Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER // Center alignment
					);
				}
			}
		}
	}

	/**
	 * Retrieves and formats the display value for a specific data field type.
	 * This method handles data collection from various sensors, system information, and external services.
	 *
	 * @param type The field type to retrieve data for (from FIELD_TYPES enum)
	 * @return FieldTypeValue containing:
	 *         - value: Formatted string to display (empty if unavailable/unsupported)
	 *         - isSunriseNext: Boolean indicating sunrise icon should be shown for SUNRISE_SUNSET field
	 *         - weatherIcon: Weather icon code for WEATHER field type
	 */
	private function getValueForFieldType(type) as FieldTypeValue {
		var result = {};
		var value = "";

		// Get device settings for unit preferences and system information
		var settings = Sys.getDeviceSettings();

		// Declare variables for data retrieval (initialized as needed per field type)
		var activityInfo;           // Activity or ActivityMonitor info
		var sample;                 // Sensor history sample
		var altitude;               // Altitude/elevation data
		var pressure = null;        // Atmospheric pressure (may not be available on CIQ 1.x devices)
		var temperature;            // Temperature data
		var weather;                // Weather data from storage
		var weatherValue;           // Processed weather value
		var sunTimes;               // Sunrise/sunset calculation results
		var unit;                   // Unit string for display

		// Process field type and retrieve appropriate data
		switch (type) {
			case FIELD_TYPE_HEART_RATE:
			case FIELD_TYPE_HR_LIVE_5S:
				// Issue #34: Prioritize live HR from current activity over historical data
				// This provides more responsive heart rate readings during activities
				activityInfo = Activity.getActivityInfo();
				sample = activityInfo.currentHeartRate;

				if (sample != null) {
					// Use current activity heart rate if available
					value = sample.format(INTEGER_FORMAT);
				} else if (ActivityMonitor has :getHeartRateHistory) {
					// Fall back to most recent historical heart rate data
					sample = ActivityMonitor.getHeartRateHistory(1, /* newestFirst */ true)
						.next();
					if ((sample != null) && (sample.heartRate != ActivityMonitor.INVALID_HR_SAMPLE)) {
						value = sample.heartRate.format(INTEGER_FORMAT);
					}
				}
				break;

			case FIELD_TYPE_BATTERY:
				// Issue #8: Battery level returned as float, use floor() to match native behavior
				// This must match the calculation used in drawBatteryMeter() for consistency
				value = Math.floor(Sys.getSystemStats().battery);
				value = value.format(INTEGER_FORMAT) + "%";
				break;

			// Issue #37: Battery hide percent returns empty string to hide percentage text
			// Special case in updateDataField() ensures battery icon is not greyed out
			// case FIELD_TYPE_BATTERY_HIDE_PERCENT:
				// break;

			case FIELD_TYPE_NOTIFICATIONS:
				// Show notification count only if there are unread notifications
				if (settings.notificationCount > 0) {
					value = settings.notificationCount.format(INTEGER_FORMAT);
				}
				// If no notifications, value remains empty (icon will be greyed out)
				break;

			case FIELD_TYPE_CALORIES:
				// Get total calories burned today from activity monitor
				activityInfo = ActivityMonitor.getInfo();
				value = activityInfo.calories.format(INTEGER_FORMAT);
				break;

			case FIELD_TYPE_DISTANCE:
				// Get total distance traveled today from activity monitor
				activityInfo = ActivityMonitor.getInfo();
				// Issue #11: Ensure floating point division by converting to float first
				value = activityInfo.distance.toFloat() / /* CM_PER_KM */ 100000;

				// Apply unit conversion based on user's distance unit preference
				if (settings.distanceUnits == System.UNIT_METRIC) {
					unit = "km";
				} else {
					// Convert kilometers to miles
					value *= /* MI_PER_KM */ 0.621371;
					unit = "mi";
				}

				// Format to one decimal place
				value = value.format("%.1f");

				// Show unit only if the complete string fits within field length limit
				if ((value.length() + unit.length()) <= mMaxFieldLength) {
					value += unit;
				}

				break;

			case FIELD_TYPE_ALARMS:
				// Show alarm count only if there are active alarms
				if (settings.alarmCount > 0) {
					value = settings.alarmCount.format(INTEGER_FORMAT);
				}
				// If no alarms, value remains empty (icon will be greyed out)
				break;

			case FIELD_TYPE_ALTITUDE:
				// Issue #67: Prioritize current activity altitude over elevation history
				// Activity::Info.altitude is supported by CIQ 1.x, but elevation history only on select CIQ 2.x devices
				activityInfo = Activity.getActivityInfo();
				altitude = activityInfo.altitude;

				// Fall back to elevation history if current activity altitude is not available
				if ((altitude == null) && (Toybox has :SensorHistory) && (Toybox.SensorHistory has :getElevationHistory)) {
					sample = SensorHistory.getElevationHistory({ :period => 1, :order => SensorHistory.ORDER_NEWEST_FIRST })
						.next();
					if ((sample != null) && (sample.data != null)) {
						altitude = sample.data;
					}
				}

				if (altitude != null) {
					// Apply unit conversion based on user's elevation unit preference
					if (settings.elevationUnits == System.UNIT_METRIC) {
						// Meters (no conversion necessary)
						unit = "m";
					} else {
						// Convert meters to feet
						altitude *= /* FT_PER_M */ 3.28084;
						unit = "ft";
					}

					// Format as integer (altitude precision to nearest meter/foot is sufficient)
					value = altitude.format(INTEGER_FORMAT);

					// Show unit only if the complete string fits within field length limit
					if ((value.length() + unit.length()) <= mMaxFieldLength) {
						value += unit;
					}
				}
				break;

			case FIELD_TYPE_TEMPERATURE:
				// Get temperature from sensor history (only available on devices with temperature sensors)
				if ((Toybox has :SensorHistory) && (Toybox.SensorHistory has :getTemperatureHistory)) {
					sample = SensorHistory.getTemperatureHistory(null).next();
					if ((sample != null) && (sample.data != null)) {
						temperature = sample.data;

						// Apply temperature unit conversion based on user preference
						if (settings.temperatureUnits == System.UNIT_STATUTE) {
							// Convert Celsius to Fahrenheit: ensure floating point division
							temperature = (temperature * (9.0 / 5)) + 32;
						}

						// Format as integer with degree symbol
						value = temperature.format(INTEGER_FORMAT) + "°";
					}
				}
				// If temperature sensor not available or no data, value remains empty (icon greyed out)
				break;

			case FIELD_TYPE_SUNRISE_SUNSET:

				if (gLocationLat != null) {
					var nextSunEvent = 0;
					var now = Gregorian.info(Time.now(), Time.FORMAT_SHORT);

					// Convert to same format as sunTimes, for easier comparison. Add a minute, so that e.g. if sun rises at
					// 07:38:17, then 07:38 is already consided daytime (seconds not shown to user).
					now = now.hour + ((now.min + 1) / 60.0);
					//Sys.println(now);

					// Get today's sunrise/sunset times in current time zone.
					sunTimes = getSunTimes(gLocationLat, gLocationLng, null, /* tomorrow */ false);
					//Sys.println(sunTimes);

					// If sunrise/sunset happens today.
					var sunriseSunsetToday = ((sunTimes[0] != null) && (sunTimes[1] != null));
					if (sunriseSunsetToday) {

						// Before sunrise today: today's sunrise is next.
						if (now < sunTimes[0]) {
							nextSunEvent = sunTimes[0];
							result["isSunriseNext"] = true;

						// After sunrise today, before sunset today: today's sunset is next.
						} else if (now < sunTimes[1]) {
							nextSunEvent = sunTimes[1];

						// After sunset today: tomorrow's sunrise (if any) is next.
						} else {
							sunTimes = getSunTimes(gLocationLat, gLocationLng, null, /* tomorrow */ true);
							nextSunEvent = sunTimes[0];
							result["isSunriseNext"] = true;
						}
					}

					// Sun never rises/sets today.
					if (!sunriseSunsetToday) {
						value = "---";

						// Sun never rises: sunrise is next, but more than a day from now.
						if (sunTimes[0] == null) {
							result["isSunriseNext"] = true;
						}

					// We have a sunrise/sunset time.
					} else {
						var hour = Math.floor(nextSunEvent).toLong() % 24;
						var min = Math.floor((nextSunEvent - Math.floor(nextSunEvent)) * 60); // Math.floor(fractional_part * 60)
						value = App.getApp().getFormattedTime(hour, min);
						value = value[:hour] + ":" + value[:min] + value[:amPm];
					}

				// Waiting for location.
				} else {
					value = "gps?";
				}

				break;

			case FIELD_TYPE_WEATHER:
			case FIELD_TYPE_HUMIDITY:

				// Default = sunshine!
				if (type == FIELD_TYPE_WEATHER) {
					result["weatherIcon"] = "01d";
				}

				weather = getStorageValue("OpenWeatherMapCurrent") as OpenWeatherMapCurrentData?;

				// Awaiting location.
				if (gLocationLat == null) {
					value = "gps?";

				// Stored weather data available.
				} else if (weather != null) {

					// FIELD_TYPE_WEATHER.
					if (type == FIELD_TYPE_WEATHER) {
						weatherValue = weather["temp"]; // Celcius.

						if (settings.temperatureUnits == System.UNIT_STATUTE) {
							weatherValue = (weatherValue * (9.0 / 5)) + 32; // Convert to Farenheit: ensure floating point division.
						}

						value = weatherValue.format(INTEGER_FORMAT) + "°";
						result["weatherIcon"] = weather["icon"];

					// FIELD_TYPE_HUMIDITY.
					} else {
						weatherValue = weather["humidity"];
						value = weatherValue.format(INTEGER_FORMAT) + "%";
					}

				// Awaiting response.
				} else if (((getStorageValue("PendingWebRequests") as PendingWebRequests?) != null) &&
					(getStorageValue("PendingWebRequests") as PendingWebRequests)["OpenWeatherMapCurrent"]) {

					value = "...";
				}
				break;

			case FIELD_TYPE_PRESSURE:

				// Avoid using ActivityInfo.ambientPressure, as this bypasses any manual pressure calibration e.g. on Fenix
				// 5. Pressure is unlikely to change frequently, so there isn't the same concern with getting a "live" value,
				// compared with HR. Use SensorHistory only.
				if ((Toybox has :SensorHistory) && (Toybox.SensorHistory has :getPressureHistory)) {
					sample = SensorHistory.getPressureHistory(null).next();
					if ((sample != null) && (sample.data != null)) {
						pressure = sample.data;
					}
				}

				if (pressure != null) {
					unit = "mb";
					pressure = pressure / 100; // Pa --> mbar;
					value = pressure.format("%.1f");

					// If single decimal place doesn't fit, truncate to integer.
					if (value.length() > mMaxFieldLength) {
						value = pressure.format(INTEGER_FORMAT);

					// Otherwise, if unit fits as well, add it.
					} else if (value.length() + unit.length() <= mMaxFieldLength) {
						value = value + unit;
					}
				}
				break;
		}

		result["value"] = value;
		return result;
	}

	/**
	 * Calculates sunrise and sunset times in local time for a given location and date.
	 *
	 * With thanks to ruiokada. Adapted and translated to Monkey C from:
	 * https://gist.github.com/ruiokada/b28076d4911820ddcbbc
	 *
	 * Uses astronomical equations for precise sunrise/sunset calculation:
	 * - Julian day conversion: https://en.wikipedia.org/wiki/Julian_day#Converting_Julian_or_Gregorian_calendar_date_to_Julian_Day_Number
	 * - Sunrise equation: https://en.wikipedia.org/wiki/Sunrise_equation#Complete_calculation_on_Earth
	 *
	 * @param lat Latitude of location in degrees (South is negative)
	 * @param lng Longitude of location in degrees (West is negative)
	 * @param tz Timezone hour offset from UTC (e.g. Pacific/Los Angeles is -8). Use null for system timezone
	 * @param tomorrow Boolean flag to calculate tomorrow's times instead of today's
	 * @return Array of length 2 containing [sunrise, sunset] as decimal hours in local time
	 *         Special cases:
	 *         - [null, -1]: Sun never rises (polar night)
	 *         - [-1, null]: Sun never sets (polar day)
	 *         - Normal case: [sunrise_hour, sunset_hour] where hours are decimal (e.g. 6.5 = 6:30)
	 */
	private function getSunTimes(lat, lng, tz, tomorrow) as Array<Number?> {

		// Use double precision where possible, as floating point errors can affect result by minutes.
		lat = lat.toDouble();
		lng = lng.toDouble();

		var now = Time.now();
		if (tomorrow) {
			now = now.add(new Time.Duration(24 * 60 * 60));
		}
		var d = Gregorian.info(Time.now(), Time.FORMAT_SHORT);
		var rad = Math.PI / 180.0d;
		var deg = 180.0d / Math.PI;

		// Calculate Julian date from Gregorian.
		var a = Math.floor((14 - d.month) / 12);
		var y = d.year + 4800 - a;
		var m = d.month + (12 * a) - 3;
		var jDate = d.day
			+ Math.floor(((153 * m) + 2) / 5)
			+ (365 * y)
			+ Math.floor(y / 4)
			- Math.floor(y / 100)
			+ Math.floor(y / 400)
			- 32045;

		// Number of days since Jan 1st, 2000 12:00.
		var n = jDate - 2451545.0d + 0.0008d;
		//Sys.println("n " + n);

		// Mean solar noon.
		var jStar = n - (lng / 360.0d);
		//Sys.println("jStar " + jStar);

		// Solar mean anomaly.
		var M = 357.5291d + (0.98560028d * jStar);
		var MFloor = Math.floor(M);
		var MFrac = M - MFloor;
		M = MFloor.toLong() % 360;
		M += MFrac;
		//Sys.println("M " + M);

		// Equation of the centre.
		var C = 1.9148d * Math.sin(M * rad)
			+ 0.02d * Math.sin(2 * M * rad)
			+ 0.0003d * Math.sin(3 * M * rad);
		//Sys.println("C " + C);

		// Ecliptic longitude.
		var lambda = (M + C + 180 + 102.9372d);
		var lambdaFloor = Math.floor(lambda);
		var lambdaFrac = lambda - lambdaFloor;
		lambda = lambdaFloor.toLong() % 360;
		lambda += lambdaFrac;
		//Sys.println("lambda " + lambda);

		// Solar transit.
		var jTransit = 2451545.5d + jStar
			+ 0.0053d * Math.sin(M * rad)
			- 0.0069d * Math.sin(2 * lambda * rad);
		//Sys.println("jTransit " + jTransit);

		// Declination of the sun.
		var delta = Math.asin(Math.sin(lambda * rad) * Math.sin(23.44d * rad));
		//Sys.println("delta " + delta);

		// Hour angle.
		var cosOmega = (Math.sin(-0.83d * rad) - Math.sin(lat * rad) * Math.sin(delta))
			/ (Math.cos(lat * rad) * Math.cos(delta));
		//Sys.println("cosOmega " + cosOmega);

		// Sun never rises.
		if (cosOmega > 1) {
			return [null, -1 as Number];
		}

		// Sun never sets.
		if (cosOmega < -1) {
			return [-1 as Number, null];
		}

		// Calculate times from omega.
		var omega = Math.acos(cosOmega) * deg;
		var jSet = jTransit + (omega / 360.0);
		var jRise = jTransit - (omega / 360.0);
		var deltaJSet = jSet - jDate;
		var deltaJRise = jRise - jDate;

		var tzOffset = (tz == null) ? (Sys.getClockTime().timeZoneOffset / 3600) : tz.toNumber();
		return [
			/* localRise */ ((deltaJRise * 24) + tzOffset).toNumber(),
			/* localSet */ ((deltaJSet * 24) + tzOffset).toNumber()
		];
	}
}
