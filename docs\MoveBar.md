# MoveBar Documentation

## Overview

The `MoveBar` class is a specialized drawable component that renders Garmin's Move Bar indicator on the watch face. The Move Bar is a visual representation of the user's inactivity level, encouraging movement throughout the day by displaying segmented bars that fill up as the user remains inactive.

## Purpose and Context

### Move Bar Concept
The Move Bar is a core Garmin fitness feature designed to promote healthy activity patterns:
- **Inactivity Tracking**: Monitors periods of prolonged sitting or inactivity
- **Visual Feedback**: Provides immediate visual indication of current inactivity level
- **Movement Encouragement**: Motivates users to move regularly throughout the day
- **Health Benefits**: Supports better cardiovascular health through regular movement

### Visual Design Philosophy
- **Segmented Visualization**: Uses discrete bar segments for clear level indication
- **Progressive Filling**: Bars fill from left to right as inactivity increases
- **Hexagonal Aesthetics**: Chevron-shaped segments provide visual direction and appeal
- **Color Coding**: Theme color for filled bars, muted color for empty bars
- **Emphasis Design**: First bar is double-width to highlight initial inactivity

## Technical Architecture

### Class Hierarchy
```
Ui.Drawable
    └── MoveBar
```

### Rendering Modes

#### Buffered Mode
- **Memory Usage**: Uses bitmap buffer for efficient rendering
- **Performance**: High - only updates buffer when move bar level changes
- **Use Case**: Recommended for devices with sufficient memory
- **Optimization**: Selective buffer updates based on change detection

#### Unbuffered Mode
- **Memory Usage**: Minimal - no bitmap buffers
- **Performance**: Moderate - redraws all segments on each update
- **Use Case**: Memory-constrained devices or simple implementations
- **Trade-off**: Lower memory usage vs. higher CPU usage per frame

### Key Components

#### Layout Management
- **Position Control**: X/Y coordinates for precise placement
- **Adaptive Sizing**: Base width vs. full-width modes
- **Dimension Calculation**: Dynamic width calculation based on available space
- **Separator Spacing**: Configurable gaps between bar segments

#### Visual Styling
- **Hexagonal Segments**: 6-sided polygon shapes with angled edges
- **Color Management**: Theme-based color coordination
- **Style Variants**: All segments, filled only, or hidden modes
- **Double-Width First Bar**: Emphasis on initial inactivity level

## Data Integration

### Activity Monitor Integration
```monkey-c
var info = ActivityMonitor.getInfo();
var currentMoveBarLevel = info.moveBarLevel;
```

### Move Bar Levels
- **Range**: `ActivityMonitor.MOVE_BAR_LEVEL_MIN` to `ActivityMonitor.MOVE_BAR_LEVEL_MAX`
- **Meaning**: Higher levels indicate longer periods of inactivity
- **Zero Level**: No inactivity (all bars empty)
- **Maximum Level**: Extended inactivity (all bars filled)

## Visual Styles

### ALL_SEGMENTS (Style 0)
- **Display**: Shows both filled and empty bar segments
- **Use Case**: Complete visualization of inactivity scale
- **Visual Impact**: Full context of current level vs. maximum possible
- **User Benefit**: Clear understanding of progress toward maximum inactivity

### FILLED_SEGMENTS (Style 1)
- **Display**: Shows only filled bar segments
- **Use Case**: Minimalist display focusing on current inactivity
- **Visual Impact**: Cleaner appearance with less visual clutter
- **User Benefit**: Emphasis on current state without distraction

### HIDDEN (Style 2)
- **Display**: Move bar is completely hidden
- **Use Case**: Users who prefer not to see inactivity tracking
- **Visual Impact**: No move bar presence on watch face
- **User Benefit**: Simplified watch face for those who don't use this feature

## Adaptive Width System

### Base Width Mode
- **Usage**: Normal operation with fixed width from layout
- **Calculation**: Uses configured width parameter
- **Layout**: Positioned according to layout specifications
- **Typical Scenario**: When seconds display is visible

### Full Width Mode
- **Usage**: Expanded width when more space is available
- **Calculation**: `dc.getWidth() - (2 * mX) + mTailWidth`
- **Layout**: Expands to fill available horizontal space
- **Typical Scenario**: When seconds display is hidden in sleep mode

### Width Calculation Logic
```monkey-c
mCurrentWidth = mIsFullWidth ?
    (dc.getWidth() - (2 * mX) + mTailWidth) :
    mBaseWidth;
```

## Performance Optimization

### Change Detection
```monkey-c
if (currentMoveBarLevel != mLastMoveBarLevel) {
    mLastMoveBarLevel = currentMoveBarLevel;
    mBufferNeedsRedraw = true;
}
```

### Buffer Management
- **Recreation Triggers**: Color changes, width changes, first draw
- **Update Triggers**: Move bar level changes only
- **Memory Optimization**: 3-color palette for minimal memory usage
- **Clearing Strategy**: Background color clearing for proper "filled segments" mode

### Rendering Efficiency
- **Selective Updates**: Only redraw when data changes
- **Early Exit**: Skip rendering when hidden
- **Clipping**: Precise positioning with minimal overdraw
- **Polygon Optimization**: Efficient 6-point polygon rendering

## Memory Management

### Buffered Mode Memory Usage
```monkey-c
Buffer Size = width × height × bits_per_pixel
Palette = 3 colors (background, empty, filled)
Typical Usage = ~1-2KB per buffer
```

### Memory Optimization Strategies
- **Restricted Palette**: 3-color palette vs. full color depth
- **Conditional Buffers**: Only create buffers when needed
- **Buffer Reuse**: Recreate only when dimensions or colors change
- **Efficient Clearing**: Use background color instead of transparency

## Geometric Calculations

### Bar Width Distribution
```
Available Width = Total Width - Tail Width - Separator Space
Separator Space = (Number of Bars - 1) × Separator Width
Bar Width = Available Width ÷ (Number of Bars + 1)
Note: +1 accounts for double-width first bar
```

### Hexagonal Shape Coordinates
```
Origin Point: (x, y)
Tail Width: mHeight / 2
Points:
  0: (x, y)                           // Center-left origin
  1: (x - tailWidth, y - halfHeight)  // Top-left tail
  2: (x - tailWidth + width, y - halfHeight) // Top-right
  3: (x + width, y)                   // Center-right
  4: (x - tailWidth + width, y + halfHeight) // Bottom-right
  5: (x - tailWidth, y + halfHeight)  // Bottom-left tail
```

## Integration Patterns

### Watch Face Integration
```monkey-c
class MyWatchFace extends WatchUi.WatchFace {
    private var mMoveBar;

    function initialize() {
        var params = {
            :x => 50, :y => 100, :width => 120,
            :height => 8, :separator => 2
        };
        mMoveBar = new MoveBar(params);
    }

    function onUpdate(dc) {
        mMoveBar.draw(dc);
    }

    function onEnterSleep() {
        mMoveBar.setFullWidth(true);  // Expand when seconds hidden
    }

    function onExitSleep() {
        mMoveBar.setFullWidth(false); // Return to base width
    }
}
```

### Settings Integration
```monkey-c
function onSettingsChanged() {
    mMoveBar.onSettingsChanged(); // Handle style changes
}
```

## Configuration Parameters

### Constructor Parameters
```monkey-c
typedef MoveBarParams as {
    :x as Number,        // X coordinate of left edge
    :y as Number,        // Y coordinate of center line
    :width as Number,    // Base width (expandable in full-width mode)
    :height as Number,   // Height of bar segments
    :separator as Number // Width of gaps between segments
};
```

### Style Settings
- **Property Key**: "MoveBarStyle"
- **Values**: 0 (All Segments), 1 (Filled Segments), 2 (Hidden)
- **Default**: Typically 0 (All Segments)
- **Runtime Changes**: Supported via `onSettingsChanged()`

## Error Handling and Edge Cases

### Data Validation
- **Null Activity Info**: Graceful handling of missing activity data
- **Invalid Move Bar Level**: Bounds checking for level values
- **Zero Dimensions**: Minimum size validation for proper rendering

### Rendering Edge Cases
- **Very Small Widths**: Ensure bars remain visible at minimum sizes
- **Large Separator Values**: Handle cases where separators exceed available space
- **Color Palette Issues**: Fallback colors when theme colors unavailable

### Memory Constraints
- **Buffer Creation Failure**: Graceful degradation to unbuffered mode
- **Palette Limitations**: Ensure essential colors are always available
- **Memory Pressure**: Efficient buffer management during low memory conditions

## Code Examples

### Basic Implementation
```monkey-c
// Initialize move bar with standard parameters
var moveBarParams = {
    :x => 30,           // Left margin
    :y => 180,          // Vertical center position
    :width => 160,      // Base width
    :height => 6,       // Bar height
    :separator => 1     // Gap between bars
};
var mMoveBar = new MoveBar(moveBarParams);

// In draw method
function onUpdate(dc) {
    // Draw other watch face elements first
    drawBackground(dc);
    drawTime(dc);

    // Draw move bar
    mMoveBar.draw(dc);
}
```

### Advanced Usage with Full-Width Mode
```monkey-c
class AdaptiveMoveBarWatchFace extends WatchUi.WatchFace {
    private var mMoveBar;
    private var mShowSeconds = true;

    function initialize() {
        WatchFace.initialize();

        var params = {
            :x => 20, :y => 200, :width => 120,
            :height => 8, :separator => 2
        };
        mMoveBar = new MoveBar(params);
    }

    function onUpdate(dc) {
        // Update full-width mode based on seconds visibility
        mMoveBar.setFullWidth(!mShowSeconds);

        // Draw move bar
        mMoveBar.draw(dc);
    }

    function onEnterSleep() {
        mShowSeconds = false;
        // Move bar will expand to full width on next draw
    }

    function onExitSleep() {
        mShowSeconds = true;
        // Move bar will return to base width on next draw
    }

    function onSettingsChanged() {
        mMoveBar.onSettingsChanged();
    }
}
```

### Custom Styling Integration
```monkey-c
// Handle move bar style changes
function updateMoveBarStyle() {
    var style = getPropertyValue("MoveBarStyle");

    switch (style) {
        case 0: // ALL_SEGMENTS
            // Move bar shows filled and empty segments
            break;
        case 1: // FILLED_SEGMENTS
            // Move bar shows only filled segments
            break;
        case 2: // HIDDEN
            // Move bar is completely hidden
            break;
    }

    // Notify move bar of style change
    mMoveBar.onSettingsChanged();
}
```

## Best Practices

### Performance Optimization
✅ **Recommended:**
- Use buffered mode on devices with sufficient memory
- Call `setFullWidth()` only when width actually changes
- Handle settings changes through `onSettingsChanged()`
- Position move bar to avoid overlap with other UI elements

❌ **Avoid:**
- Calling `setFullWidth()` on every draw cycle
- Ignoring move bar style settings
- Creating multiple MoveBar instances unnecessarily
- Placing move bar where it might be obscured

### Memory Management
✅ **Recommended:**
- Monitor memory usage when using buffered mode
- Use unbuffered mode on memory-constrained devices
- Handle buffer creation failures gracefully
- Optimize color palette for memory efficiency

❌ **Avoid:**
- Creating oversized buffers
- Ignoring memory constraints
- Using full color depth when palette is sufficient
- Keeping unused buffers in memory

### Visual Design
✅ **Recommended:**
- Choose appropriate height for device screen size
- Use consistent separator width across UI elements
- Coordinate move bar colors with overall theme
- Position move bar for optimal visibility

❌ **Avoid:**
- Making bars too small to see clearly
- Using colors that don't contrast with background
- Positioning move bar where it interferes with other elements
- Ignoring user style preferences

## Troubleshooting

### Common Issues

#### Move Bar Not Visible
**Symptoms:** Move bar doesn't appear on screen
**Causes:**
- Style set to HIDDEN (2)
- Positioning outside screen bounds
- Height or width set to zero
- Color matching background exactly

**Solutions:**
- Check MoveBarStyle property value
- Verify position parameters are within screen bounds
- Ensure height and width are positive values
- Verify color contrast with background

#### Performance Issues
**Symptoms:** Slow rendering, frame rate drops
**Causes:**
- Inefficient buffer management
- Unnecessary redraws
- Memory pressure from large buffers
- Complex polygon rendering on slow devices

**Solutions:**
- Use unbuffered mode on slower devices
- Optimize buffer recreation triggers
- Reduce move bar dimensions if necessary
- Profile rendering performance

#### Memory Problems
**Symptoms:** Out of memory errors, buffer creation failures
**Causes:**
- Insufficient memory for bitmap buffers
- Large buffer dimensions
- Multiple simultaneous buffers
- Memory fragmentation

**Solutions:**
- Switch to unbuffered mode
- Reduce buffer dimensions
- Implement buffer pooling
- Monitor total memory usage

#### Visual Artifacts
**Symptoms:** Incorrect colors, missing segments, rendering glitches
**Causes:**
- Color palette limitations
- Buffer clearing issues
- Clipping problems
- Polygon coordinate errors

**Solutions:**
- Verify color palette includes all needed colors
- Ensure proper buffer clearing
- Check clipping rectangle calculations
- Validate polygon coordinate calculations

## Testing and Validation

### Functional Testing
```monkey-c
// Test move bar level changes
function testMoveBarLevels() {
    for (var level = 0; level <= ActivityMonitor.MOVE_BAR_LEVEL_MAX; level++) {
        // Simulate different move bar levels
        simulateMoveBarLevel(level);
        validateMoveBarRendering(level);
    }
}

// Test style changes
function testMoveBarStyles() {
    var styles = [0, 1, 2]; // ALL_SEGMENTS, FILLED_SEGMENTS, HIDDEN

    for (var i = 0; i < styles.size(); i++) {
        setPropertyValue("MoveBarStyle", styles[i]);
        mMoveBar.onSettingsChanged();
        validateStyleRendering(styles[i]);
    }
}

// Test width modes
function testWidthModes() {
    mMoveBar.setFullWidth(false);
    validateBaseWidth();

    mMoveBar.setFullWidth(true);
    validateFullWidth();
}
```

### Performance Testing
1. **Rendering Speed**: Measure time per draw() call
2. **Memory Usage**: Monitor buffer memory consumption
3. **Change Detection**: Verify efficient update triggers
4. **Buffer Management**: Test recreation and redraw logic

### Visual Testing
1. **Segment Appearance**: Verify hexagonal shape rendering
2. **Color Accuracy**: Check filled vs. empty segment colors
3. **Positioning**: Validate placement and alignment
4. **Style Variants**: Test all visual style modes

## Future Enhancements

### Potential Improvements
- **Animation Support**: Smooth transitions between move bar levels
- **Custom Shapes**: Alternative segment shapes beyond hexagons
- **Gradient Effects**: Color gradients for enhanced visual appeal
- **Size Adaptation**: Dynamic sizing based on screen dimensions

### Advanced Features
- **Historical Visualization**: Show move bar trends over time
- **Goal Integration**: Visual connection to daily movement goals
- **Customization Options**: User-configurable colors and shapes
- **Accessibility**: Enhanced visibility options for different users

### Performance Optimizations
- **Vectorized Rendering**: Use vector graphics for scalability
- **GPU Acceleration**: Leverage hardware acceleration where available
- **Predictive Caching**: Anticipate and pre-render common states
- **Adaptive Quality**: Adjust rendering quality based on device capabilities
