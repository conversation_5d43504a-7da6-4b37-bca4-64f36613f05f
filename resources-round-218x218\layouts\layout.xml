<layout id="WatchFace">
	<drawable class="Background" />

	<drawable id="LeftGoalMeter" class="GoalMeter">
		<param name="side">:left</param>
		<param name="stroke">8</param>
		<param name="height">138</param>
		<param name="separator">2</param>
	</drawable>

	<drawable id="RightGoalMeter" class="GoalMeter">
		<param name="side">:right</param>
		<param name="stroke">8</param>
		<param name="height">138</param>
		<param name="separator">2</param>
	</drawable>

	<drawable id="DataArea" class="DataArea">
		<param name="locX">67</param>
		<param name="width">84</param>
		<param name="row1Y">170</param>
		<param name="row2Y">186</param>
		<param name="goalIconY">166</param>
		<param name="goalIconLeftX">40</param>
		<param name="goalIconRightX">178</param>
	</drawable>

	<drawable id="DataFields" class="DataFields">
		<param name="left">69</param>
		<param name="right">149</param>
		<param name="top">24</param>
		<param name="bottom">45</param>
		<param name="batteryWidth">28</param>
	</drawable>

	<drawable id="Date" class="DateLine">
		<param name="y">69</param>
	</drawable>

	<drawable id="Indicators" class="Indicators">
		<param name="locX">30</param>
		<param name="locY">109</param>
		<param name="spacingY">30</param>
		<param name="batteryWidth">22</param>
	</drawable>

	<drawable id="Time" class="ThickThinTime">
		<param name="secondsX">149</param>
		<param name="secondsY">129</param>		
		<param name="secondsClipY">139</param>
		<param name="secondsClipWidth">22</param>
		<param name="secondsClipHeight">15</param>
	</drawable>

	<drawable id="MoveBar" class="MoveBar">
		<param name="x">49</param>
		<param name="y">146</param>
		<param name="width">94</param>
		<param name="height">9</param>
		<param name="separator">3</param>
	</drawable>
	
</layout>