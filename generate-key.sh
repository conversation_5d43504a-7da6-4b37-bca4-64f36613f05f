#!/bin/bash
# Bash Script to Generate Garmin Connect IQ Developer Key
# Based on: https://medium.com/@bgallois/garmin-app-development-without-the-visual-studio-code-85628e4b6ba1

# Configuration
KEY_NAME="${1:-developer_key}"
FORCE="$2"
PEM_FILE="${KEY_NAME}.pem"
DER_FILE="${KEY_NAME}.der"
KEY_SIZE=4096

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to show help
show_help() {
    echo -e "${CYAN}Garmin Connect IQ Developer Key Generator${NC}"
    echo ""
    echo -e "${YELLOW}Usage: $0 [key_name] [--force]${NC}"
    echo ""
    echo -e "${GREEN}Parameters:${NC}"
    echo "  key_name       - Base name for key files (default: developer_key)"
    echo "  --force        - Overwrite existing key files"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0                           # Generate developer_key.pem and developer_key.der"
    echo "  $0 my_key                    # Generate my_key.pem and my_key.der"
    echo "  $0 developer_key --force     # Overwrite existing keys"
    echo ""
    echo -e "${BLUE}Requirements:${NC}"
    echo "  - OpenSSL must be installed"
    echo "  - On macOS: brew install openssl"
    echo "  - On Ubuntu/Debian: sudo apt-get install openssl"
    echo "  - On CentOS/RHEL: sudo yum install openssl"
    echo ""
    echo -e "${GREEN}Output files:${NC}"
    echo "  ${PEM_FILE}    - Private key in PEM format (intermediate)"
    echo "  ${DER_FILE}    - Private key in DER format (for Garmin SDK)"
}

# Function to check if OpenSSL is available
check_openssl() {
    echo -e "${YELLOW}Checking for OpenSSL...${NC}"
    
    if command -v openssl >/dev/null 2>&1; then
        OPENSSL_VERSION=$(openssl version)
        echo -e "${GREEN}✓ OpenSSL found: ${OPENSSL_VERSION}${NC}"
        return 0
    else
        echo -e "${RED}✗ OpenSSL not found!${NC}"
        echo ""
        echo -e "${YELLOW}Please install OpenSSL:${NC}"
        echo "  macOS:        brew install openssl"
        echo "  Ubuntu/Debian: sudo apt-get install openssl"
        echo "  CentOS/RHEL:   sudo yum install openssl"
        echo "  Arch Linux:    sudo pacman -S openssl"
        return 1
    fi
}

# Function to generate the key
generate_key() {
    echo -e "${CYAN}Generating Garmin Connect IQ Developer Key...${NC}"
    echo "Key size: ${KEY_SIZE} bits"
    echo "Output files: ${PEM_FILE}, ${DER_FILE}"
    echo ""
    
    # Check if files exist
    if [[ -f "$PEM_FILE" || -f "$DER_FILE" ]]; then
        if [[ "$FORCE" != "--force" ]]; then
            echo -e "${YELLOW}Key files already exist!${NC}"
            [[ -f "$PEM_FILE" ]] && echo "  ${PEM_FILE}: EXISTS"
            [[ -f "$DER_FILE" ]] && echo "  ${DER_FILE}: EXISTS"
            echo ""
            echo -e "${RED}Use --force to overwrite existing files${NC}"
            return 1
        else
            echo -e "${YELLOW}Overwriting existing key files...${NC}"
        fi
    fi
    
    # Step 1: Generate private key in PEM format
    echo -e "${GREEN}Step 1: Generating RSA private key (${KEY_SIZE} bits)...${NC}"
    echo -e "${BLUE}Command: openssl genpkey -algorithm RSA -out ${PEM_FILE} -outform PEM -pkeyopt rsa_keygen_bits:${KEY_SIZE}${NC}"
    
    if ! openssl genpkey -algorithm RSA -out "$PEM_FILE" -outform PEM -pkeyopt rsa_keygen_bits:$KEY_SIZE; then
        echo -e "${RED}✗ Error: Failed to generate PEM key${NC}"
        cleanup_and_exit
        return 1
    fi
    
    if [[ ! -f "$PEM_FILE" ]]; then
        echo -e "${RED}✗ Error: PEM file was not created${NC}"
        cleanup_and_exit
        return 1
    fi
    
    echo -e "${GREEN}✓ PEM key generated: ${PEM_FILE}${NC}"
    
    # Step 2: Convert to DER format
    echo -e "${GREEN}Step 2: Converting to DER format...${NC}"
    echo -e "${BLUE}Command: openssl pkcs8 -topk8 -inform PEM -outform DER -in ${PEM_FILE} -out ${DER_FILE} -nocrypt${NC}"
    
    if ! openssl pkcs8 -topk8 -inform PEM -outform DER -in "$PEM_FILE" -out "$DER_FILE" -nocrypt; then
        echo -e "${RED}✗ Error: Failed to convert to DER format${NC}"
        cleanup_and_exit
        return 1
    fi
    
    if [[ ! -f "$DER_FILE" ]]; then
        echo -e "${RED}✗ Error: DER file was not created${NC}"
        cleanup_and_exit
        return 1
    fi
    
    echo -e "${GREEN}✓ DER key generated: ${DER_FILE}${NC}"
    echo ""
    
    # Show file information
    PEM_SIZE=$(stat -f%z "$PEM_FILE" 2>/dev/null || stat -c%s "$PEM_FILE" 2>/dev/null)
    DER_SIZE=$(stat -f%z "$DER_FILE" 2>/dev/null || stat -c%s "$DER_FILE" 2>/dev/null)
    
    echo -e "${GREEN}Key Generation Successful!${NC}"
    echo -e "${CYAN}Files created:${NC}"
    echo "  ${PEM_FILE} - ${PEM_SIZE} bytes (PEM format - keep secure)"
    echo "  ${DER_FILE} - ${DER_SIZE} bytes (DER format - for Garmin SDK)"
    echo ""
    
    # Set appropriate permissions
    chmod 600 "$PEM_FILE" "$DER_FILE"
    echo -e "${YELLOW}File permissions set to 600 (owner read/write only)${NC}"
    
    show_usage
    return 0
}

# Function to show usage information
show_usage() {
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo "  1. Keep both files secure and backed up"
    echo "  2. Use ${DER_FILE} with MonkeyC compiler (-y parameter)"
    echo "  3. Never share your private keys publicly"
    echo ""
    echo -e "${CYAN}Usage in build scripts:${NC}"
    echo "  monkeyc -d device -f monkey.jungle -o app.prg -y ${DER_FILE}"
    echo ""
    echo -e "${CYAN}Integration with build scripts:${NC}"
    echo "  make build PRODUCT=instinct3amoled45mm"
    echo "  ./build.sh build venu2"
}

# Function to cleanup on failure
cleanup_and_exit() {
    [[ -f "$PEM_FILE" ]] && rm -f "$PEM_FILE" && echo -e "${YELLOW}Cleaned up: ${PEM_FILE}${NC}"
    [[ -f "$DER_FILE" ]] && rm -f "$DER_FILE" && echo -e "${YELLOW}Cleaned up: ${DER_FILE}${NC}"
}

# Function to show existing key info
show_key_info() {
    if [[ -f "$DER_FILE" ]]; then
        echo -e "${CYAN}Existing key information:${NC}"
        echo "  File: ${DER_FILE}"
        
        # Get file size (cross-platform)
        if command -v stat >/dev/null 2>&1; then
            SIZE=$(stat -f%z "$DER_FILE" 2>/dev/null || stat -c%s "$DER_FILE" 2>/dev/null)
            echo "  Size: ${SIZE} bytes"
        fi
        
        # Get file dates (cross-platform)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            echo "  Created: $(stat -f%SB -t%Y-%m-%d\ %H:%M:%S "$DER_FILE")"
        else
            # Linux
            echo "  Modified: $(stat -c%y "$DER_FILE" | cut -d. -f1)"
        fi
        
        [[ -f "$PEM_FILE" ]] && echo "  PEM file: ${PEM_FILE} ($(stat -f%z "$PEM_FILE" 2>/dev/null || stat -c%s "$PEM_FILE" 2>/dev/null) bytes)"
    else
        echo -e "${YELLOW}No existing developer key found.${NC}"
        echo -e "${GREEN}Run $0 to create one.${NC}"
    fi
}

# Main execution
case "$1" in
    help|--help|-h)
        show_help
        exit 0
        ;;
    info|--info)
        show_key_info
        exit 0
        ;;
esac

echo -e "${CYAN}Garmin Connect IQ Developer Key Generator${NC}"
echo -e "${CYAN}=========================================${NC}"
echo ""

# Check for OpenSSL
if ! check_openssl; then
    exit 1
fi

# Generate key
if generate_key; then
    echo -e "${GREEN}Key generation completed successfully!${NC}"
    exit 0
else
    echo -e "${RED}Key generation failed!${NC}"
    exit 1
fi
