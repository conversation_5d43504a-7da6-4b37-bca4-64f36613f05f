# DataArea Documentation

## Overview

The `DataArea` class manages the central information display area of the watch face. It provides adaptive content display that switches between city local time information and goal meter values based on user configuration.

## Purpose and Architecture

### Adaptive Content Strategy
The DataArea implements an adaptive content strategy where the central area displays different information based on configuration:

**City Mode**: When a city is configured
- Row 1: City name (truncated to 10 characters)
- Row 2: Local time in that city with timezone calculations

**Goal Mode**: When no city is configured
- Left/Right: Goal meter icons and values
- Adaptive positioning based on goal meter digit style

### Key Responsibilities
1. **Content Switching**: Automatic mode selection based on city configuration
2. **Timezone Calculations**: Complex timezone math for city local time
3. **Goal Integration**: Display of goal meter icons and values
4. **Error Handling**: Graceful handling of invalid cities and network errors
5. **Layout Management**: Responsive positioning and text alignment
6. **Data Formatting**: Consistent formatting of numeric values and text

## Technical Implementation

### Class Hierarchy
```
Ui.Drawable
    └── DataArea
```

### Layout System

#### Positioning Parameters
```monkey-c
typedef DataAreaParams as {
    :locX as Number,            // Left edge X coordinate
    :width as Number,           // Total width of data area
    :row1Y as Number,           // Y coordinate for first text row
    :row2Y as Number,           // Y coordinate for second text row
    :goalIconY as Number,       // Y coordinate for goal icons
    :goalIconLeftX as Number,   // X coordinate for left goal icon
    :goalIconRightX as Number   // X coordinate for right goal icon
};
```

#### Responsive Design
- **Text Centering**: City information centered horizontally
- **Goal Alignment**: Left goal left-aligned, right goal right-aligned
- **Vertical Spacing**: Consistent row spacing for readability
- **Icon Positioning**: Goal icons positioned relative to text

### Goal Meter Integration

#### Goal Value Caching
The component caches formatted goal values for efficient rendering:

```monkey-c
// Left goal meter cache
private var mLeftGoalType;      // Goal type (steps, calories, etc.)
private var mLeftGoalIsValid;   // Data validity flag
private var mLeftGoalCurrent;   // Formatted current value string
private var mLeftGoalMax;       // Formatted max/target value string

// Right goal meter cache (same structure)
```

#### Goal Type Support
- **GOAL_TYPE_STEPS**: Step count with target
- **GOAL_TYPE_FLOORS_CLIMBED**: Floors climbed with target
- **GOAL_TYPE_ACTIVE_MINUTES**: Active minutes with target
- **GOAL_TYPE_BATTERY**: Battery percentage (special formatting)
- **GOAL_TYPE_CALORIES**: Calories burned with target
- **GOAL_TYPE_OFF**: Hidden/disabled goal meter

#### Special Formatting
- **Battery Goals**: Show "%" symbol instead of max value (100)
- **Integer Formatting**: All numeric values displayed as integers
- **Null Handling**: Invalid goals show no text (icon may be grayed)

### City Local Time System

#### Timezone Calculation Algorithm
Complex timezone math for accurate city time display:

```monkey-c
// Get timezone offset for target city
var timeZoneGmtOffset = cityLocalTime["current"]["gmtOffset"];

// Get local timezone offset
var localGmtOffset = Sys.getClockTime().timeZoneOffset;

// Calculate city time: (Local time) - (Local offset) + (City offset)
var cityTime = Time.now().subtract(localGmtOffset).add(timeZoneGmtOffset);
```

#### DST Transition Handling
Automatic handling of daylight saving time transitions:
- **Current Offset**: Uses current GMT offset for city
- **Transition Detection**: Checks if current time >= transition time
- **Next Offset**: Automatically switches to next GMT offset when applicable
- **Data Refresh**: New data requested shortly after transitions

#### Error State Management
Graceful handling of various error conditions:
- **Network Errors**: Shows "..." while awaiting response
- **Invalid City**: Shows "???" for unrecognized city names
- **API Errors**: Displays error indicator instead of time
- **Data Staleness**: Requests fresh data when timezone transitions occur

### Display Modes

#### City Display Mode
When `LocalTimeInCity` setting is configured:

**Row 1 (City Name)**:
- Color: `gMonoDarkColour` (secondary text color)
- Font: `gNormalFont` (may be optimized based on city usage)
- Alignment: Center justified
- Truncation: Limited to 10 characters for display consistency

**Row 2 (Local Time)**:
- Color: `gMonoLightColour` (primary text color)
- Font: `gNormalFont`
- Alignment: Center justified
- Format: Uses application's time formatting (12/24 hour, AM/PM)

#### Goal Display Mode
When no city is configured:

**Goal Icons**:
- Position: Left and right icon positions
- Color: Theme color (valid) or meter background color (invalid)
- Font: `gIconsFont`
- Alignment: Left-aligned for left goal, right-aligned for right goal

**Goal Values**:
- Style: Controlled by `GoalMeterDigitsStyle` setting
- Colors: Light color (current), dark color (target)
- Positioning: Adaptive based on digit style setting

### Goal Meter Digit Styles

#### Style Options
- **CURRENT_TARGET (0)**: Shows both current and target values
- **CURRENT (1)**: Shows only current value (vertically centered)
- **HIDDEN (2)**: Shows no digit values (icons only)

#### Positioning Logic
```monkey-c
// Current value positioning
var currentY = (digitStyle == 1 /* CURRENT */) ? 
    ((mRow1Y + mRow2Y) / 2) :  // Centered for current-only
    mRow1Y;                    // Top row for current/target

// Target value (only shown for CURRENT_TARGET style)
var targetY = mRow2Y;  // Bottom row
```

## Data Flow Architecture

### Goal Data Flow
```
MadreView → setGoalValues() → Cache Formatting → draw() → Goal Display
```

### City Data Flow
```
Background Service → Storage → draw() → Timezone Calculation → City Display
```

### Mode Selection Flow
```
draw() → Check City Setting → City Mode OR Goal Mode → Render Content
```

## Performance Optimizations

### Caching Strategy
- **Pre-formatted Strings**: Goal values formatted once and cached
- **Validity Flags**: Avoid processing invalid goal data
- **Mode Detection**: Single city setting check determines entire render mode

### Efficient Rendering
- **Conditional Drawing**: Only draw elements that should be visible
- **String Operations**: Minimize string manipulation during rendering
- **Color Setting**: Batch color changes to reduce graphics state changes

### Memory Management
- **String Caching**: Reuse formatted strings until values change
- **Null Handling**: Graceful handling of missing or invalid data
- **Resource Cleanup**: No persistent resources requiring cleanup

## Integration Points

### MadreView Integration
- **Goal Value Updates**: Receives processed goal data from main view
- **Layout Coordination**: Positioning coordinated with overall watch face layout
- **Theme Integration**: Uses global color variables from theme system

### Background Service Integration
- **City Data**: Consumes city local time data from background web requests
- **Error Handling**: Displays appropriate indicators for various error states
- **Data Freshness**: Participates in data refresh cycle for timezone transitions

### Settings Integration
- **City Configuration**: Responds to `LocalTimeInCity` setting changes
- **Digit Style**: Adapts display based on `GoalMeterDigitsStyle` setting
- **Font Selection**: Uses optimized font based on city feature usage

## Best Practices

### Goal Display
✅ **Recommended:**
- Cache formatted goal values for performance
- Handle invalid goal data gracefully
- Use appropriate colors for goal validity indication
- Respect user digit style preferences

### City Display
✅ **Recommended:**
- Implement robust timezone calculations
- Handle DST transitions automatically
- Provide clear error indicators for network issues
- Truncate city names consistently for layout stability

### Performance
✅ **Recommended:**
- Cache formatted strings until data changes
- Minimize string operations during rendering
- Use conditional rendering based on data availability
- Batch graphics state changes for efficiency

## Troubleshooting

### Common Issues

#### City Time Not Updating
**Symptoms:** City time shows old time or "..." indefinitely
**Causes:** Background service not running or network issues
**Solutions:** Check background service registration and network connectivity

#### Goal Values Not Displaying
**Symptoms:** Goal icons show but no numeric values
**Causes:** Goal digit style set to HIDDEN or invalid goal data
**Solutions:** Verify digit style setting and goal data validity

#### Timezone Calculations Incorrect
**Symptoms:** City time shows wrong time, especially around DST transitions
**Causes:** Timezone offset calculation errors or stale transition data
**Solutions:** Verify timezone math and data refresh logic

#### Layout Issues
**Symptoms:** Text overlapping or misaligned
**Causes:** Incorrect positioning parameters or font size issues
**Solutions:** Check DataAreaParams values and font selection logic

### Debugging Strategies
1. **Mode Detection**: Verify city setting evaluation and mode selection
2. **Data Flow**: Trace goal value caching and city data retrieval
3. **Timezone Math**: Validate timezone offset calculations step by step
4. **Rendering**: Check color settings and text positioning logic
