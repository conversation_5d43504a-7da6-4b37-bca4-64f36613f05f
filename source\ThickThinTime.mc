// Import required Garmin Connect IQ SDK modules for thick/thin time display functionality
using Toybox.WatchUi as Ui;        // User interface components and drawable base class
using Toybox.System as Sys;        // System services for time and device information
using Toybox.Application as App;   // Application context for time formatting

import Toybox.Lang;

/**
 * ThickThinTime class renders the main time display using different font weights.
 *
 * This class implements a sophisticated time display system where hours and minutes
 * are rendered using different font weights (thick/thin) to create visual hierarchy
 * and improve readability. The design emphasizes the current time while providing
 * secondary information like seconds and AM/PM indicators.
 *
 * Key Features:
 * - Mixed font weights for visual hierarchy (thick hours, thin minutes, or vice versa)
 * - Precise text positioning and centering calculations
 * - Optimized partial updates for seconds display (power saving)
 * - Support for both 12-hour and 24-hour time formats
 * - Configurable AM/PM indicator positioning
 * - Adaptive layout for different screen shapes and sizes
 * - Clipping-based optimization for low-power mode updates
 *
 * Design Philosophy:
 * The thick/thin approach creates visual contrast that makes time reading more intuitive.
 * Hours typically use a heavier font weight to emphasize the primary time component,
 * while minutes use a lighter weight for secondary information. This hierarchy helps
 * users quickly parse the time at a glance.
 *
 * Performance Optimizations:
 * - Partial updates for seconds (only redraws seconds area in low power mode)
 * - Precise clipping rectangles to minimize screen updates
 * - Font resource caching for efficient rendering
 * - Tabular figure fonts for consistent spacing
 */
class ThickThinTime extends Ui.Drawable {

	// Font resources for different time components with varying weights
	private var mHoursFont, mMinutesFont, mSecondsFont;

	// Y coordinate for seconds text positioning (read from layout configuration)
	private var mSecondsY;

	// Vertical adjustment for time positioning on wide rectangular screens
	// Used to center time display within available space when screen aspect ratio requires it
	private var mAdjustY = 0;

	// Precise clipping rectangle coordinates for seconds partial updates
	// These define the minimal screen area that needs to be updated when seconds change
	// Note: Y coordinate corresponds to top of glyph area, which is lower than drawText() Y parameter
	// because drawText() starts from font ascent (above most glyph tops)
	private var mSecondsClipRectX;      // Left edge of seconds clipping area
	private var mSecondsClipRectY;      // Top edge of seconds clipping area (glyph-based)
	private var mSecondsClipRectWidth;  // Width of seconds clipping area
	private var mSecondsClipRectHeight; // Height of seconds clipping area

	// Display control flags and positioning constants
	private var mHideSeconds = false;   // Flag to hide seconds display (battery saving mode)
	private var AM_PM_X_OFFSET = 2;     // Horizontal spacing between minutes and AM/PM indicator

	// Issue #10: Horizontal adjustment for seconds position when hours leading zero is hidden
	// Compensates for layout shifts when hour format changes (e.g., "9:30" vs "09:30")
	private var mSecondsClipXAdjust = 0;

	/**
	 * Type definition for ThickThinTime constructor parameters.
	 * Defines the layout properties needed to position and configure the time display.
	 */
	typedef ThickThinTimeParams as {
		:secondsX as Number,        // X coordinate for seconds text positioning
		:secondsY as Number,        // Y coordinate for seconds text positioning
		:secondsClipY as Number,    // Y coordinate for seconds clipping rectangle (glyph-based)
		:secondsClipWidth as Number,    // Width of seconds clipping rectangle
		:secondsClipheight as Number,   // Height of seconds clipping rectangle
		:adjustY as Number?,        // Optional vertical adjustment for time centering
		:amPmOffset as Number?      // Optional horizontal offset for AM/PM indicator
	};

	/**
	 * Constructor for ThickThinTime drawable component.
	 * Initializes the time display with specified layout parameters and loads font resources.
	 *
	 * @param params ThickThinTimeParams containing layout configuration and positioning data
	 */
	function initialize(params as ThickThinTimeParams) {
		Drawable.initialize(params);

		// Apply optional vertical adjustment for wide rectangular screens
		if (params[:adjustY] != null) {
			mAdjustY = params[:adjustY];
		}

		// Apply optional AM/PM offset for custom spacing
		if (params[:amPmOffset] != null) {
			AM_PM_X_OFFSET = params[:amPmOffset];
		}

		// Store seconds positioning parameters
		mSecondsY = params[:secondsY];

		// Configure clipping rectangle for seconds partial updates
		mSecondsClipRectX = params[:secondsX];
		mSecondsClipRectY = params[:secondsClipY];
		mSecondsClipRectWidth = params[:secondsClipWidth];
		mSecondsClipRectHeight = params[:secondsClipHeight];

		// Load font resources for different time components
		// These fonts typically have different weights (thick/thin) for visual hierarchy
		mHoursFont = Ui.loadResource(Rez.Fonts.HoursFont);       // Primary time component font
		mMinutesFont = Ui.loadResource(Rez.Fonts.MinutesFont);   // Secondary time component font
		mSecondsFont = Ui.loadResource(Rez.Fonts.SecondsFont);   // Tertiary time component font
	}

	/**
	 * Sets the visibility state of the seconds display.
	 * Used to hide seconds in battery-saving modes or when user preferences disable seconds.
	 *
	 * @param hideSeconds Boolean flag indicating whether to hide seconds display
	 */
	function setHideSeconds(hideSeconds) {
		mHideSeconds = hideSeconds;
	}

	/**
	 * Main draw method for full screen updates.
	 * Renders both hours/minutes and seconds components in a complete screen refresh.
	 *
	 * @param dc Graphics drawing context for rendering
	 */
	function draw(dc) {
		drawHoursMinutes(dc);                    // Draw primary time display (hours and minutes)
		drawSeconds(dc, /* isPartialUpdate */ false);  // Draw seconds with full update flag
	}

	/**
	 * Draws the hours and minutes display with mixed font weights.
	 *
	 * This method handles the primary time display, implementing sophisticated text positioning
	 * to center the combined hours and minutes as a single unit. It uses different font weights
	 * for hours and minutes to create visual hierarchy, and includes optional AM/PM indicator.
	 *
	 * Key Features:
	 * - Precise centering of combined hours+minutes text
	 * - Mixed font weights for visual hierarchy
	 * - Tabular figure fonts for consistent spacing
	 * - Optional AM/PM indicator with configurable spacing
	 * - Color coordination with global theme colors
	 *
	 * @param dc Graphics drawing context for rendering
	 */
	function drawHoursMinutes(dc) {
		var clockTime = Sys.getClockTime();
		var formattedTime = App.getApp().getFormattedTime(clockTime.hour, clockTime.min);
		formattedTime[:amPm] = formattedTime[:amPm].toUpper();

		// Extract formatted time components
		var hours = formattedTime[:hour];
		var minutes = formattedTime[:min];
		var amPmText = formattedTime[:amPm];

		// Calculate screen center coordinates with optional vertical adjustment
		var halfDCWidth = dc.getWidth() / 2;
		var halfDCHeight = (dc.getHeight() / 2) + mAdjustY;

		// Center the combined hours and minutes text as a single unit
		// This is more visually pleasing than right-aligning hours and left-aligning minutes
		// Tabular figures (monospaced numbers) ensure consistent spacing across different font weights
		var totalWidth = dc.getTextWidthInPixels(hours + minutes, mHoursFont);
		var x = halfDCWidth - (totalWidth / 2);

		// Draw hours with primary time color and font weight
		dc.setColor(gHoursColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			x,                                                          // X position (left edge of hours)
			halfDCHeight,                                              // Y position (vertical center)
			mHoursFont,                                                // Hours font (typically heavier weight)
			hours,                                                     // Hours text
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER // Left-aligned, vertically centered
		);
		x += dc.getTextWidthInPixels(hours, mHoursFont);

		// Draw minutes with secondary time color and font weight
		dc.setColor(gMinutesColour, Graphics.COLOR_TRANSPARENT);
		dc.drawText(
			x,                                                          // X position (immediately after hours)
			halfDCHeight,                                              // Y position (same as hours)
			mMinutesFont,                                              // Minutes font (typically lighter weight)
			minutes,                                                   // Minutes text
			Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER // Left-aligned, vertically centered
		);

		// Draw optional AM/PM indicator after minutes
		if (amPmText.length() > 0) {
			dc.setColor(gThemeColour, Graphics.COLOR_TRANSPARENT);
			x += dc.getTextWidthInPixels(minutes, mMinutesFont);
			dc.drawText(
				x + AM_PM_X_OFFSET,                                       // X position with breathing space
				halfDCHeight,                                              // Y position (same as hours/minutes)
				mSecondsFont,                                              // Seconds font (smaller size for AM/PM)
				amPmText,                                                  // AM/PM text
				Graphics.TEXT_JUSTIFY_LEFT | Graphics.TEXT_JUSTIFY_VCENTER // Left-aligned, vertically centered
			);
		}
	}

	/**
	 * Draws the seconds display with support for both full and partial updates.
	 *
	 * This method is called in two contexts:
	 * 1. Full draw() - Complete screen refresh with transparent background
	 * 2. Partial update - Low power mode optimization with precise clipping
	 *
	 * Partial Update Optimization:
	 * In low power mode, only the seconds area is updated to save battery. This requires:
	 * - Precise clipping rectangle to limit screen updates
	 * - Background clearing to remove old seconds text
	 * - Consistent clipping area between updates
	 *
	 * Performance Benefits:
	 * - Reduces screen update area by ~95% in low power mode
	 * - Minimizes power consumption for frequent seconds updates
	 * - Maintains smooth seconds animation without full screen refresh
	 *
	 * @param dc Graphics drawing context for rendering
	 * @param isPartialUpdate Boolean flag indicating partial update mode (low power)
	 */
	function drawSeconds(dc, isPartialUpdate) {
		// Early exit if seconds display is disabled
		if (mHideSeconds) {
			return;
		}

		// Get current time and format seconds with leading zero
		var clockTime = Sys.getClockTime();
		var seconds = clockTime.sec.format("%02d");

		if (isPartialUpdate) {
			// PARTIAL UPDATE MODE: Optimize for low power consumption

			// Set precise clipping rectangle to limit screen update area
			dc.setClip(
				mSecondsClipRectX + mSecondsClipXAdjust,  // X position with adjustment for layout shifts
				mSecondsClipRectY,                        // Y position (top of glyph area)
				mSecondsClipRectWidth,                    // Width of clipping area
				mSecondsClipRectHeight                    // Height of clipping area
			);

			// Set colors for partial update
			// Note: Cannot optimize by setting color once at start of low power mode
			// This causes color inversion issues on real hardware (alternates between normal and inverse)
			dc.setColor(gThemeColour, /* Graphics.COLOR_RED */ gBackgroundColour);

			// Clear the clipped area to remove old seconds text
			// Assumption: Nothing else overlaps with seconds text area
			dc.clear();

		} else {
			// FULL UPDATE MODE: Complete screen refresh

			// Use transparent background to prevent overlap issues with other drawables
			// Font height may extend beyond expected bounds and overlap with other elements
			dc.setColor(gThemeColour, Graphics.COLOR_TRANSPARENT);
		}

		// Draw the seconds text at the configured position
		dc.drawText(
			mSecondsClipRectX + mSecondsClipXAdjust,  // X position with layout adjustment
			mSecondsY,                                // Y position for text baseline
			mSecondsFont,                             // Seconds font (typically smallest weight)
			seconds,                                  // Formatted seconds string
			Graphics.TEXT_JUSTIFY_LEFT                // Left-aligned text
		);
	}
}