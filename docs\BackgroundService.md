# BackgroundService Documentation

## Overview

The `BackgroundService` class is a critical component of the watch face that handles background data fetching for external services. It runs independently of the main watch face application and is triggered by temporal events to fetch weather information and city local time data.

## Purpose and Architecture

### Background Processing Model
The service operates within Garmin's background processing framework:
- **Temporal Events**: Triggered periodically by the system (typically every 15-30 minutes)
- **Limited Execution**: Constrained execution time and memory to preserve battery life
- **Data Transfer**: Results passed back to main app via `Background.exit()`
- **Independent Operation**: Runs separately from the main watch face UI thread

### Key Responsibilities
1. **Web Request Management**: Handles HTTP requests to external APIs
2. **Priority Processing**: Manages multiple pending requests with priority ordering
3. **Data Filtering**: Reduces memory usage by extracting only essential data
4. **Error Handling**: Manages network failures and API errors gracefully
5. **Resource Management**: Operates within strict memory and time constraints

## Supported Services

### 1. City Local Time Service
- **Purpose**: Fetches timezone information for user-specified cities
- **API**: Custom Google Apps Script endpoint
- **Data**: GMT offset, DST status, and transition information
- **Priority**: High (processed first when multiple requests are pending)

### 2. OpenWeatherMap Current Weather
- **Purpose**: Fetches current weather conditions for user's location
- **API**: OpenWeatherMap Current Weather API
- **Data**: Temperature, humidity, weather icons, and location verification
- **Priority**: Lower (processed after city time requests)

## Technical Implementation

### Class Hierarchy
```
System.ServiceDelegate
    └── BackgroundService
```

### Key Methods

#### `initialize()`
- **Purpose**: Initializes the background service delegate
- **Execution**: Called once when service is first created
- **Resources**: Minimal setup, no heavy initialization

#### `onTemporalEvent()`
- **Purpose**: Main processing method triggered by system temporal events
- **Execution**: Called periodically by the system (15-30 minute intervals)
- **Logic**: Priority-based request processing with single request per event
- **Constraints**: Must complete within system-imposed time limits

#### `onReceiveCityLocalTime(responseCode, data)`
- **Purpose**: Processes city local time API responses
- **Error Handling**: Converts HTTP errors to structured error data
- **Data Processing**: Passes through API response data with minimal processing
- **Exit Strategy**: Immediately exits background service with results

#### `onReceiveOpenWeatherMapCurrent(responseCode, data)`
- **Purpose**: Processes OpenWeatherMap API responses
- **Data Filtering**: Extracts essential data from ~2KB response to ~200 bytes
- **Memory Optimization**: Critical for background service memory constraints
- **Error Handling**: Manages both HTTP and API-level errors

#### `makeWebRequest(url, params, callback)`
- **Purpose**: Standardized web request utility method
- **Configuration**: GET method, JSON response type, URL-encoded parameters
- **Consistency**: Ensures all requests use same headers and options

## Data Flow Architecture

### Request Initiation
```
Main App → Sets PendingWebRequests flag → Background Service triggered
```

### Processing Flow
```
1. onTemporalEvent() called by system
2. Check PendingWebRequests storage
3. Process highest priority pending request
4. Make HTTP request to external API
5. Receive response in callback method
6. Process and filter response data
7. Exit background service with results
8. Main app receives data and updates UI
```

### Priority System
```
Priority 1: City Local Time (user-initiated, less frequent)
Priority 2: Weather Data (location-based, more frequent)
```

## Memory Management

### Background Service Constraints
- **Memory Limit**: Strict memory constraints in background environment
- **Execution Time**: Limited processing time per temporal event
- **Resource Cleanup**: Automatic cleanup when service exits

### Data Filtering Strategy
```
OpenWeatherMap Full Response: ~2KB JSON
├── coord: {lat, lon}
├── weather: [{id, main, description, icon}]
├── main: {temp, pressure, humidity, temp_min, temp_max}
├── wind: {speed, deg}
├── clouds: {all}
├── sys: {country, sunrise, sunset}
└── ... (additional fields)

Filtered Response: ~200 bytes
├── cod: response code
├── lat, lon: location verification
├── dt: timestamp
├── temp: temperature
├── humidity: humidity percentage
└── icon: weather icon code
```

### Memory Optimization Benefits
- **90% Size Reduction**: From ~2KB to ~200 bytes
- **Faster Transfer**: Quicker data passing to main app
- **Lower Memory Pressure**: Reduced impact on main app memory
- **Better Performance**: Faster JSON parsing and processing

## API Integration

### City Local Time API
```
Endpoint: Google Apps Script custom service
Method: GET
Parameters: {city: "london"}
Response: {
  requestCity: "london",
  city: "London",
  current: {gmtOffset: 3600, dst: true},
  next: {when: 1540688400, gmtOffset: 0, dst: false}
}
```

### OpenWeatherMap API
```
Endpoint: https://api.openweathermap.org/data/2.5/weather
Method: GET
Parameters: {
  lat: 51.75,
  lon: -0.46,
  appid: "api_key",
  units: "metric"
}
Response: Full weather data structure (see sample in code)
```

## Error Handling

### HTTP Error Types
- **Network Failures**: Connection timeouts, DNS resolution failures
- **Server Errors**: 5xx HTTP status codes
- **Client Errors**: 4xx HTTP status codes (except API-specific errors)

### API Error Types
- **Invalid API Key**: 401 Unauthorized
- **Rate Limiting**: 429 Too Many Requests
- **City Not Found**: Custom error codes in response data
- **Service Unavailable**: Temporary API outages

### Error Response Structure
```monkey-c
// HTTP Errors
{
  "httpError": 404  // HTTP status code
}

// API Errors (passed through from service)
{
  "cod": 401,
  "message": "Invalid API key"
}
```

## Configuration and Settings

### API Key Management
- **Default Key**: Provided for Crystal watch face users
- **Custom Override**: Users can specify their own OpenWeatherMap API key
- **Open Source Plan**: Default key registered under OWM Open Source Plan
- **Usage Guidelines**: Restrictions on API usage to prevent key blocking

### Request Parameters
- **Weather Location**: Uses last known GPS coordinates
- **City Selection**: User-configurable city name for local time
- **Units**: Metric units for temperature (Celsius)
- **Response Format**: JSON for all APIs

## Performance Characteristics

### Execution Timing
- **Temporal Event Frequency**: 15-30 minutes (system-controlled)
- **Request Processing Time**: < 5 seconds typical
- **Background Execution Limit**: System-imposed timeout
- **Data Transfer Time**: Minimal due to data filtering

### Resource Usage
- **Memory Footprint**: Minimal during execution
- **Network Usage**: Single HTTP request per temporal event
- **Battery Impact**: Optimized for minimal power consumption
- **CPU Usage**: Brief processing bursts during temporal events

## Integration with Main Application

### Data Storage
- **Pending Requests**: Stored in persistent storage by main app
- **Response Data**: Passed via Background.exit() mechanism
- **Error States**: Communicated through structured error responses

### Synchronization
- **Request Flags**: Main app sets flags, background service clears them
- **Data Freshness**: Timestamps used to validate data currency
- **Retry Logic**: Failed requests remain flagged for retry

### UI Updates
- **Asynchronous Updates**: Main app updates UI when background data received
- **Error Display**: User-friendly error messages for failed requests
- **Loading States**: Visual indicators during background processing

## Code Examples

### Basic Background Service Usage
```monkey-c
// In main application - requesting weather data
function requestWeatherUpdate() {
    var pendingRequests = {
        "OpenWeatherMapCurrent" => true
    };
    setStorageValue("PendingWebRequests", pendingRequests);

    // Background service will be triggered by next temporal event
}

// Handling background service results
function onBackgroundData(data) {
    if (data["OpenWeatherMapCurrent"] != null) {
        var weatherData = data["OpenWeatherMapCurrent"];

        if (weatherData["httpError"] != null) {
            // Handle HTTP error
            handleWeatherError(weatherData["httpError"]);
        } else if (weatherData["cod"] != 200) {
            // Handle API error
            handleApiError(weatherData["cod"], weatherData["message"]);
        } else {
            // Process successful weather data
            updateWeatherDisplay(weatherData);
        }
    }
}
```

### Custom API Key Configuration
```monkey-c
// In settings or properties
function setCustomWeatherApiKey(apiKey) {
    setPropertyValue("OWMKeyOverride", apiKey);
    // Next weather request will use custom key
}

// Background service automatically uses custom key if provided
var owmKeyOverride = getPropertyValue("OWMKeyOverride");
var apiKey = ((owmKeyOverride != null) && (owmKeyOverride.length() > 0))
    ? owmKeyOverride
    : "default_key";
```

### Error Handling Patterns
```monkey-c
function handleBackgroundResponse(data) {
    if (data["httpError"] != null) {
        switch (data["httpError"]) {
            case 404:
                showError("Service not found");
                break;
            case 429:
                showError("Rate limit exceeded");
                break;
            case 500:
                showError("Server error");
                break;
            default:
                showError("Network error: " + data["httpError"]);
        }
    }
}
```

## Best Practices

### Request Management
✅ **Recommended:**
- Set pending request flags only when data is actually needed
- Clear flags after successful data reception
- Implement exponential backoff for repeated failures
- Validate data freshness using timestamps

❌ **Avoid:**
- Setting multiple request flags simultaneously (only one processed per event)
- Ignoring error responses (leads to stuck pending flags)
- Requesting data more frequently than temporal event intervals
- Storing large amounts of data in background service

### Memory Optimization
✅ **Recommended:**
- Filter response data to essential fields only
- Use structured error responses instead of full error objects
- Minimize string operations in background service
- Exit background service as quickly as possible

❌ **Avoid:**
- Storing full API responses without filtering
- Complex data processing in background service
- Creating large temporary objects
- Keeping background service running longer than necessary

### Error Handling
✅ **Recommended:**
- Distinguish between HTTP errors and API errors
- Provide user-friendly error messages
- Implement retry logic with appropriate delays
- Log errors for debugging purposes

❌ **Avoid:**
- Ignoring error responses
- Retrying failed requests immediately
- Showing technical error messages to users
- Blocking UI updates due to background errors

## Troubleshooting

### Common Issues

#### Background Service Not Triggering
**Symptoms:** No data updates, pending requests remain set
**Causes:**
- Temporal events disabled in device settings
- Background processing disabled for the app
- Device in power-saving mode

**Solutions:**
- Check device background app settings
- Verify temporal event registration in manifest
- Test on different devices/simulators

#### API Key Issues
**Symptoms:** 401 errors, "Invalid API key" messages
**Causes:**
- Incorrect API key configuration
- API key rate limits exceeded
- API key not activated for required services

**Solutions:**
- Verify API key in OpenWeatherMap dashboard
- Check API key permissions and rate limits
- Test with different API key

#### Memory Issues
**Symptoms:** Background service crashes, incomplete data
**Causes:**
- Insufficient memory filtering
- Large response data structures
- Memory leaks in background processing

**Solutions:**
- Enhance data filtering in response callbacks
- Minimize object creation in background service
- Profile memory usage during background execution

#### Network Connectivity
**Symptoms:** HTTP timeout errors, connection failures
**Causes:**
- Poor network connectivity
- Firewall or proxy restrictions
- API service outages

**Solutions:**
- Test network connectivity on device
- Check API service status pages
- Implement retry logic with exponential backoff

## Testing and Validation

### Unit Testing
```monkey-c
// Test data filtering
function testWeatherDataFiltering() {
    var fullResponse = loadSampleWeatherResponse();
    var filtered = filterWeatherData(fullResponse);

    assert(filtered.size() < fullResponse.size() / 10); // 90% reduction
    assert(filtered["temp"] != null);
    assert(filtered["icon"] != null);
}

// Test error handling
function testErrorHandling() {
    var httpError = {httpError: 404};
    var apiError = {cod: 401, message: "Invalid key"};

    assert(isHttpError(httpError));
    assert(isApiError(apiError));
}
```

### Integration Testing
1. **Background Service Execution**: Verify temporal events trigger service
2. **Data Transfer**: Confirm data passes correctly to main app
3. **Error Propagation**: Test error handling end-to-end
4. **Memory Usage**: Monitor memory consumption during execution
5. **Network Resilience**: Test with poor network conditions

### Performance Testing
1. **Response Time**: Measure time from request to data availability
2. **Memory Usage**: Profile memory consumption patterns
3. **Battery Impact**: Monitor power consumption during background execution
4. **Data Size**: Verify data filtering effectiveness

## Future Enhancements

### Potential Improvements
- **Batch Requests**: Process multiple requests in single temporal event
- **Caching Strategy**: Implement intelligent data caching
- **Compression**: Add response compression for large data sets
- **Offline Mode**: Graceful degradation when network unavailable

### API Expansion
- **Additional Weather Services**: Support for alternative weather APIs
- **Extended Data**: Additional weather parameters (UV index, air quality)
- **Forecast Data**: Multi-day weather forecasting
- **Location Services**: Enhanced location-based data fetching

### Performance Optimizations
- **Request Batching**: Combine multiple API calls
- **Delta Updates**: Only fetch changed data
- **Predictive Fetching**: Anticipate data needs
- **Adaptive Intervals**: Dynamic temporal event frequency
