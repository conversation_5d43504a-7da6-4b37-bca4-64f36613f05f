// Import required Garmin Connect IQ SDK modules for data area functionality
using Toybox.WatchUi as Ui;        // User interface framework and drawable base classes
using Toybox.Graphics as Gfx;      // Graphics drawing and rendering capabilities
using Toybox.Application as App;   // Application context and property access
using Toybox.System as Sys;        // System services and device information
using Toybox.Time;                 // Time and date handling utilities
using Toybox.Time.Gregorian;       // Gregorian calendar date formatting

import Toybox.Lang;

/**
 * DataArea class manages the central information display area of the watch face.
 *
 * This class is responsible for rendering the central content area which can display:
 * - City local time with timezone information (when city is configured)
 * - Goal meter icons and values (when no city is configured)
 * - Adaptive layout based on available data and user preferences
 *
 * Layout Modes:
 * 1. City Mode: Displays city name and local time in two rows
 * 2. Goal Mode: Displays goal icons and values for left/right goal meters
 *
 * Key Features:
 * - Timezone-aware time calculations and display
 * - Goal meter integration with activity monitoring
 * - Conditional content display based on data availability
 * - Error handling for invalid city names and network failures
 * - Responsive design that adapts to different content types
 * - Color coding for goal validity and data freshness
 *
 * Performance Optimizations:
 * - Cached goal values to avoid repeated calculations
 * - Efficient string formatting and truncation
 * - Minimal redraw operations for unchanged data
 */
class DataArea extends Ui.Drawable {

	// Layout positioning variables
	private var mRow1Y;             // Y coordinate for first row of text
	private var mRow2Y;             // Y coordinate for second row of text

	// Left goal meter data cache
	private var mLeftGoalType;      // Type of left goal (steps, calories, etc.)
	private var mLeftGoalIsValid;   // Whether left goal data is valid
	private var mLeftGoalCurrent;   // Current value for left goal (formatted string)
	private var mLeftGoalMax;       // Maximum/target value for left goal (formatted string)

	// Right goal meter data cache
	private var mRightGoalType;     // Type of right goal (steps, calories, etc.)
	private var mRightGoalIsValid;  // Whether right goal data is valid
	private var mRightGoalCurrent;  // Current value for right goal (formatted string)
	private var mRightGoalMax;      // Maximum/target value for right goal (formatted string)

	// Goal icon positioning
	private var mGoalIconY;         // Y coordinate for goal icons
	private var mGoalIconLeftX;     // X coordinate for left goal icon
	private var mGoalIconRightX;    // X coordinate for right goal icon

	/**
	 * Type definition for DataArea constructor parameters.
	 * Defines all layout positioning information needed for proper content placement.
	 */
	typedef DataAreaParams as {
		:locX as Number,            // X coordinate of the data area (left edge)
		:width as Number,           // Width of the data area
		:row1Y as Number,           // Y coordinate for first row of text (city name or current values)
		:row2Y as Number,           // Y coordinate for second row of text (city time or target values)
		:goalIconY as Number,       // Y coordinate for goal meter icons
		:goalIconLeftX as Number,   // X coordinate for left goal meter icon
		:goalIconRightX as Number   // X coordinate for right goal meter icon
	};

	/**
	 * Constructor for DataArea drawable component.
	 * Initializes the data area with layout positioning information.
	 *
	 * @param params DataAreaParams containing all layout coordinates and dimensions
	 */
	function initialize(params as DataAreaParams) {
		Drawable.initialize(params);

		// Store text row positioning
		mRow1Y = params[:row1Y];
		mRow2Y = params[:row2Y];

		// Store goal icon positioning
		mGoalIconY = params[:goalIconY];
		mGoalIconLeftX = params[:goalIconLeftX];
		mGoalIconRightX = params[:goalIconRightX];
	}

	/**
	 * Updates the cached goal meter values for display.
	 *
	 * This method processes goal data from both left and right goal meters and caches
	 * formatted strings for efficient rendering. It handles different goal types with
	 * appropriate formatting and special cases.
	 *
	 * Data Processing:
	 * - Formats numeric values as integers for display
	 * - Handles battery percentage special case (shows "%" instead of max value)
	 * - Caches null values for invalid goals to prevent display
	 * - Maintains goal type and validity flags for icon rendering
	 *
	 * @param leftType Type of left goal meter (GOAL_TYPE_* constants)
	 * @param leftValues GoalValues structure containing current, max, and validity
	 * @param rightType Type of right goal meter (GOAL_TYPE_* constants)
	 * @param rightValues GoalValues structure containing current, max, and validity
	 */
	function setGoalValues(leftType, leftValues as GoalValues, rightType, rightValues as GoalValues) {
		// Process left goal meter data
		mLeftGoalType = leftType;
		mLeftGoalIsValid = leftValues[:isValid];

		if (leftValues[:isValid]) {
			// Format current value as integer
			mLeftGoalCurrent = leftValues[:current].format(INTEGER_FORMAT);
			// Special case: battery shows "%" symbol instead of max value (100)
			mLeftGoalMax = (mLeftGoalType == GOAL_TYPE_BATTERY) ? "%" : leftValues[:max].format(INTEGER_FORMAT);
		} else {
			// Invalid goal data - clear cached values to prevent display
			mLeftGoalCurrent = null;
			mLeftGoalMax = null;
		}

		// Process right goal meter data
		mRightGoalType = rightType;
		mRightGoalIsValid = rightValues[:isValid];

		if (rightValues[:isValid]) {
			// Format current value as integer
			mRightGoalCurrent = rightValues[:current].format(INTEGER_FORMAT);
			// Special case: battery shows "%" symbol instead of max value (100)
			mRightGoalMax = (mRightGoalType == GOAL_TYPE_BATTERY) ? "%" : rightValues[:max].format(INTEGER_FORMAT);
		} else {
			// Invalid goal data - clear cached values to prevent display
			mRightGoalCurrent = null;
			mRightGoalMax = null;
		}
	}

	function draw(dc) {
		drawGoalIcon(dc, mGoalIconLeftX, mLeftGoalType, mLeftGoalIsValid, Graphics.TEXT_JUSTIFY_LEFT);
		drawGoalIcon(dc, mGoalIconRightX, mRightGoalType, mRightGoalIsValid, Graphics.TEXT_JUSTIFY_RIGHT);

		var city = getPropertyValue("LocalTimeInCity");

		// #78 Setting with value of empty string may cause corresponding property to be null.
		if ((city != null) && (city.length() != 0)) {
			//drawTimeZone();
			var cityLocalTime = getStorageValue("CityLocalTime") as CityLocalTimeData?;

			// If available, use city returned from web request; otherwise, use raw city from settings.
			// N.B. error response will NOT contain city.
			if ((cityLocalTime != null) && (cityLocalTime["city"] != null)) {
				city = cityLocalTime["city"];
			}

			// Time zone 1 city.
			dc.setColor(gMonoDarkColour, Gfx.COLOR_TRANSPARENT);
			dc.drawText(
				locX + (width / 2),
				mRow1Y,
				gNormalFont,
				// Limit string length.
				city.substring(0, 10),
				Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER
			);

			// Time zone 1 time.
			var time;
			if (cityLocalTime != null) {

				// Web request responded with server error e.g. unknown city.
				if (cityLocalTime["error"] != null) {

					time = "???";

				// Web request responded with time zone data for city.
				} else {
					var timeZoneGmtOffset;

					// Use next GMT offset if it's now applicable (new data will be requested shortly).
					if ((cityLocalTime["next"] != null) && (Time.now().value() >= (cityLocalTime as CityLocalTimeSuccessResponse)["next"]["when"])) {
						timeZoneGmtOffset = (cityLocalTime as CityLocalTimeSuccessResponse)["next"]["gmtOffset"];
					} else {
						timeZoneGmtOffset = (cityLocalTime as CityLocalTimeSuccessResponse)["current"]["gmtOffset"];
					}
					timeZoneGmtOffset = new Time.Duration(timeZoneGmtOffset);

					var localGmtOffset = Sys.getClockTime().timeZoneOffset;
					localGmtOffset = new Time.Duration(localGmtOffset);

					// (Local time) - (Local GMT offset) + (Time zone GMT offset)
					time = Time.now().subtract(localGmtOffset).add(timeZoneGmtOffset);
					time = Gregorian.info(time, Time.FORMAT_SHORT);
					time = App.getApp().getFormattedTime(time.hour, time.min) as FormattedTime;
					time = time[:hour] + ":" + time[:min] + time[:amPm];
				}

			// Awaiting response to web request sent by BackgroundService.
			} else {
				time = "...";
			}

			dc.setColor(gMonoLightColour, Gfx.COLOR_TRANSPARENT);
			dc.drawText(
				locX + (width / 2),
				mRow2Y,
				gNormalFont,
				time,
				Graphics.TEXT_JUSTIFY_CENTER | Graphics.TEXT_JUSTIFY_VCENTER
			);

		} else {
			drawGoalValues(dc, locX, mLeftGoalCurrent, mLeftGoalMax, Graphics.TEXT_JUSTIFY_LEFT);
			drawGoalValues(dc, locX + width, mRightGoalCurrent, mRightGoalMax, Graphics.TEXT_JUSTIFY_RIGHT);
		}
	}

	function drawGoalIcon(dc, x, type, isValid, align) {
		if (type == GOAL_TYPE_OFF) {
			return;
		}

		var icon = {
			GOAL_TYPE_BATTERY => "9",
			GOAL_TYPE_CALORIES => "6",
			GOAL_TYPE_STEPS => "0",
			GOAL_TYPE_FLOORS_CLIMBED => "1",
			GOAL_TYPE_ACTIVE_MINUTES => "2",
		}[type];

		dc.setColor(isValid ? gThemeColour : gMeterBackgroundColour, Gfx.COLOR_TRANSPARENT);
		dc.drawText(
			x,
			mGoalIconY,
			gIconsFont,
			icon,
			align
		);
	}

	function drawGoalValues(dc, x, currentValue, maxValue, align) {
		var digitStyle = getPropertyValue("GoalMeterDigitsStyle");

		// #107 Only draw values if digit style is not Hidden.
		if (digitStyle != 2 /* HIDDEN */) {
			if (currentValue != null) {
				dc.setColor(gMonoLightColour, Gfx.COLOR_TRANSPARENT);
				dc.drawText(
					x,

					// #107 Draw current value vertically centred if digit style is Current (i.e. not drawing max/target).
					(digitStyle == 1 /* CURRENT */) ? ((mRow1Y + mRow2Y) / 2) : mRow1Y,

					gNormalFont,
					currentValue,
					align | Graphics.TEXT_JUSTIFY_VCENTER
				);
			}

			// #107 Only draw max/target goal value if digit style is set to Current/Target.
			if ((maxValue != null) && (digitStyle == 0) /* CURRENT_TARGET */) {
				dc.setColor(gMonoDarkColour, Gfx.COLOR_TRANSPARENT);
				dc.drawText(
					x,
					mRow2Y,
					gNormalFont,
					maxValue,
					align | Graphics.TEXT_JUSTIFY_VCENTER
				);
			}
		}
	}
}
