# monkey.jungle Configuration Guide

This guide explains how `monkey.jungle` works and how to determine which `sourcePath` is used for different devices.

## 🎯 **Quick Answer: How to Know Which sourcePath is Used**

### **Use the Analyzer Tool:**
```powershell
# Check specific device
.\analyze-jungle.ps1 instinct3amoled45mm

# List all devices with custom sourcePath
.\analyze-jungle.ps1 -ListAll

# Show help
.\analyze-jungle.ps1 -Help
```

## 🏗️ **How monkey.jungle Works**

### **1. Hierarchy System**
Connect IQ uses a **hierarchy** to determine configuration:

```
1. Device-specific settings (highest priority)
2. Base settings (fallback)
3. Default SDK settings (lowest priority)
```

### **2. sourcePath Resolution**

#### **For `instinct3amoled45mm`:**
```
Line 4:   base.sourcePath = source
Line 111: instinct3amoled45mm.sourcePath = $(instinct3amoled45mm.sourcePath);always-on-source

Result: source;always-on-source
```

#### **For `fenix7` (no custom sourcePath):**
```
Line 4: base.sourcePath = source
(no fenix7.sourcePath defined)

Result: source
```

## 📊 **Analysis of Your monkey.jungle**

### **Base Configuration:**
```
base.sourcePath = source
base.excludeAnnotations = unbuffered;object_store;double_line_date;horizontal_indicators
```

### **Device Categories:**

#### **🔋 Always-On Display Devices (36 devices):**
These devices have **always-on display support** and use additional source code:

**sourcePath:** `source;always-on-source`

**Devices include:**
- `instinct3amoled45mm`, `instinct3amoled50mm`
- `venu`, `venu2`, `venu2s`, `venu3`, `venu3s`
- `fenix843mm`, `fenix847mm`, `fenixe`
- `fr165`, `fr265`, `fr965`
- `epix2`, `epix2pro42mm`, `epix2pro47mm`, `epix2pro51mm`
- And many more...

#### **📱 Standard Devices:**
These devices use **base sourcePath only**:

**sourcePath:** `source`

**Examples:**
- `fenix7`, `fenix6`, `fenix5`
- `fr55`, `fr245`, `fr945`
- `vivoactive4`, `vivomove3`
- Most older devices

### **🔍 How to Check Any Device:**

#### **Method 1: Use Analyzer Tool**
```powershell
.\analyze-jungle.ps1 [device_name]
```

#### **Method 2: Manual Check**
1. Open `monkey.jungle`
2. Look for `[device_name].sourcePath = ...`
3. If found: device uses custom sourcePath
4. If not found: device uses `base.sourcePath`

## 📁 **Directory Structure Impact**

### **For Always-On Devices (e.g., instinct3amoled45mm):**
```
Project Root/
├── source/                    # Base source code
│   ├── MandreApp.mc          # Main app
│   ├── MadreView.mc          # Watch face view
│   └── *.mc                  # Other components
└── always-on-source/         # Always-on specific code
    └── AlwaysOnDisplay.mc    # Always-on display logic
```

**Compiler includes:** `source/*.mc` + `always-on-source/*.mc`

### **For Standard Devices (e.g., fenix7):**
```
Project Root/
└── source/                   # Base source code only
    ├── MandreApp.mc         # Main app
    ├── MadreView.mc         # Watch face view
    └── *.mc                 # Other components
```

**Compiler includes:** `source/*.mc` only

## 🔧 **Other Configuration Examples**

### **resourcePath (Similar Logic):**
```
# Base resources
base.resourcePath = resources

# Device-specific additions
round-218x218.resourcePath = $(round-218x218.resourcePath);resources-small-time
rectangle.resourcePath = $(rectangle.resourcePath);resources-small-time;resources-small-icons
```

### **excludeAnnotations:**
```
# Base exclusions
base.excludeAnnotations = unbuffered;object_store;double_line_date;horizontal_indicators

# Device-specific exclusions (for memory-limited devices)
fr45.excludeAnnotations = buffered;properties_and_storage;background_method;double_line_date;horizontal_indicators
```

## 🎯 **Practical Examples**

### **Example 1: Building for instinct3amoled45mm**
```bash
# monkey.jungle resolution:
# base.sourcePath = source
# instinct3amoled45mm.sourcePath = $(instinct3amoled45mm.sourcePath);always-on-source
# Final: source;always-on-source

# Compiler will include:
# - source/MandreApp.mc
# - source/MadreView.mc
# - source/*.mc
# - always-on-source/AlwaysOnDisplay.mc
```

### **Example 2: Building for fenix7**
```bash
# monkey.jungle resolution:
# base.sourcePath = source
# (no fenix7.sourcePath defined)
# Final: source

# Compiler will include:
# - source/MandreApp.mc
# - source/MadreView.mc
# - source/*.mc
# (always-on-source/ is NOT included)
```

## 🚨 **Common Issues & Solutions**

### **Issue 1: "File not found" errors**
**Cause:** Device expects `always-on-source/` but it doesn't exist
**Solution:** Check if device needs always-on support

### **Issue 2: "Undefined symbol" errors**
**Cause:** Code references always-on classes but device doesn't include always-on-source
**Solution:** Use conditional compilation or check device capabilities

### **Issue 3: "Memory exceeded" errors**
**Cause:** Including too much source code for memory-limited devices
**Solution:** Use `excludeAnnotations` to exclude features

## 🛠️ **Tools & Commands**

### **Analyzer Tool Commands:**
```powershell
# Check specific device sourcePath
.\analyze-jungle.ps1 instinct3amoled45mm
.\analyze-jungle.ps1 venu2
.\analyze-jungle.ps1 fenix7

# List all devices with custom sourcePath
.\analyze-jungle.ps1 -ListAll

# Show help
.\analyze-jungle.ps1 -Help
```

### **Manual Verification:**
```bash
# Check what files are actually included
monkeyc -f monkey.jungle -d instinct3amoled45mm --list-files

# Build with verbose output
monkeyc -f monkey.jungle -d instinct3amoled45mm -o test.prg -v
```

## 📋 **Quick Reference**

### **Device Categories in Your Project:**

| Category | sourcePath | Count | Examples |
|----------|------------|-------|----------|
| **Always-On Devices** | `source;always-on-source` | 36 | instinct3amoled45mm, venu2, fenix843mm |
| **Standard Devices** | `source` | All others | fenix7, fr245, vivoactive4 |

### **Key Files:**
- **`monkey.jungle`** - Build configuration
- **`source/`** - Base MonkeyC source code
- **`always-on-source/`** - Always-on display specific code
- **`analyze-jungle.ps1`** - Tool to analyze sourcePath

### **Commands to Remember:**
```powershell
# Quick check for any device
.\analyze-jungle.ps1 [device_name]

# See all always-on devices
.\analyze-jungle.ps1 -ListAll
```

## 🔗 **Related Documentation**

- [ARCHITECTURE.md](ARCHITECTURE.md) - Build flow diagrams
- [Official Connect IQ Documentation](https://developer.garmin.com/connect-iq/reference-guides/jungle-reference/)
- [monkey.jungle Reference](https://developer.garmin.com/connect-iq/reference-guides/jungle-reference/)

---

**Now you know exactly which sourcePath is used for any device!** 🎯
