// Import required Garmin Connect IQ SDK modules for background service functionality
using Toybox.Background as Bg;        // Background processing and temporal events
using Toybox.System as Sys;           // System services and device information
using Toybox.Communications as Comms; // HTTP web requests and network communication
using Toybox.Application as App;      // Application context and property access

import Toybox.Lang;

/**
 * Type definition for HTTP error responses.
 * Used when web requests fail due to network issues, server errors, or other HTTP problems.
 */
typedef HttpErrorData as {
	"httpError" as Number    // HTTP status code (e.g., 404, 500, timeout codes)
};

/**
 * Type definition for successful city local time API responses.
 * Contains timezone information for a requested city including current and next DST transition.
 */
typedef CityLocalTimeSuccessResponse as {
	"requestCity" as String,    // Original city name as requested
	"city" as String,           // Formatted city name as returned by service
	"current" as {              // Current timezone information
		"gmtOffset" as Number,  // Current GMT offset in seconds
		"dst" as Boolean        // Whether daylight saving time is currently active
	},
	"next" as {                 // Next DST transition information
		"when" as Number,       // Unix timestamp when next transition occurs
		"gmtOffset" as Number,  // GMT offset after the transition
		"dst" as Boolean        // DST status after the transition
	}
};

/**
 * Type definition for city local time API error responses.
 * Used when the requested city cannot be found or other API errors occur.
 */
typedef CityLocalTimeErrorResponse as {
	"requestCity" as String,    // Original city name that was requested
	"error" as {                // Error details
		"code" as Number,       // Error code (e.g., 2 for CITY_NOT_FOUND)
		"message" as String     // Human-readable error message
	}
};

/**
 * Union type for city local time API responses.
 * Can be either a successful response with timezone data or an error response.
 */
typedef CityLocalTimeResponse as CityLocalTimeSuccessResponse or CityLocalTimeErrorResponse;

/**
 * Type alias for city local time data storage.
 * Used for consistent typing when storing/retrieving city time data.
 */
typedef CityLocalTimeData as CityLocalTimeResponse;

/**
 * Type definition for successful OpenWeatherMap Current Weather API responses.
 * Represents the complete JSON structure returned by the OWM API for current weather data.
 * This is the full response structure as documented in the OpenWeatherMap API specification.
 */
typedef OpenWeatherMapCurrentSuccessResponse as {
	"coord" as {                // Geographic coordinates of the location
		"lon" as Number,        // Longitude in decimal degrees
		"lat" as Number         // Latitude in decimal degrees
	},
	"weather" as Array<{        // Array of weather condition objects (usually contains 1 item)
		"id" as Number,         // Weather condition ID (used for mapping to icons)
		"main" as String,       // Group of weather parameters (Rain, Snow, Clear, etc.)
		"description" as String,// Weather condition description (light rain, clear sky, etc.)
		"icon" as String        // Weather icon ID (e.g., "01d", "10n") for day/night variants
	}>,
	"base" as String,           // Internal parameter (stations data source)
	"main" as {                 // Main weather data
		"temp" as Number,       // Temperature in Celsius (when units=metric)
		"pressure" as Number,   // Atmospheric pressure in hPa
		"humidity" as Number,   // Humidity percentage (0-100)
		"temp_min" as Number,   // Minimum temperature (for large cities/areas)
		"temp_max" as Number    // Maximum temperature (for large cities/areas)
	},
	"visibility" as Number,     // Visibility in meters (max 10000)
	"wind" as {                 // Wind information
		"speed" as Number,      // Wind speed in meters/second
		"deg" as Number         // Wind direction in degrees (0-360)
	},
	"clouds" as {               // Cloudiness information
		"all" as Number         // Cloudiness percentage (0-100)
	},
	"dt" as Number,             // Unix timestamp of data calculation time
	"sys" as {                  // System/country information
		"type" as Number,       // Internal parameter
		"id" as Number,         // Internal parameter
		"message" as Number,    // Internal parameter
		"country" as String,    // Country code (ISO 3166-1 alpha-2)
		"sunrise" as Number,    // Unix timestamp of sunrise time
		"sunset" as Number,     // Unix timestamp of sunset time
	},
	"id" as Number,             // City ID (OpenWeatherMap internal identifier)
	"name" as String,           // City name
	"cod" as Number             // HTTP response code (200 for success)
};

/**
 * Type definition for OpenWeatherMap Current Weather API error responses.
 * Used when API requests fail due to invalid API keys, rate limits, or other API errors.
 */
typedef OpenWeatherMapCurrentErrorResponse as {
	"cod" as Number,        // HTTP error code (e.g., 401 for invalid API key, 429 for rate limit)
	"message" as String     // Error message explaining the failure
};

/**
 * Union type for OpenWeatherMap Current Weather API responses.
 * Can be either a successful response with weather data or an error response.
 */
typedef OpenWeatherMapCurrentResponse as OpenWeatherMapCurrentSuccessResponse or OpenWeatherMapCurrentErrorResponse;

/**
 * Type definition for processed OpenWeatherMap current weather data.
 * This is a filtered and flattened version of the full API response, containing only
 * the essential data needed by the watch face. This reduces memory usage and simplifies
 * data handling in the main application.
 */
typedef OpenWeatherMapCurrentData as {
	"cod" as Number,        // Response code (200 for success, error codes for failures)
	"lat" as Number,        // Latitude of the weather location
	"lon" as Number,        // Longitude of the weather location
	"dt" as Number,         // Unix timestamp when weather data was calculated
	"temp" as Number,       // Temperature in Celsius
	"humidity" as Number,   // Humidity percentage (0-100)
	"icon" as String        // Weather icon code for display (e.g., "01d", "10n")
};

/**
 * BackgroundService class handles background data fetching for the watch face.
 *
 * This service runs in the background and is triggered by temporal events to fetch
 * external data such as weather information and city local time data. It manages
 * web requests efficiently by processing them based on priority and availability.
 *
 * Key Features:
 * - Temporal event-driven execution (runs periodically in background)
 * - Priority-based web request processing
 * - Efficient data filtering to reduce memory usage
 * - Error handling for network failures and API errors
 * - Support for custom API keys and fallback mechanisms
 *
 * Background Processing:
 * - Runs independently of the main watch face application
 * - Limited execution time and memory constraints
 * - Results are passed back to main app via Background.exit()
 * - Triggered by system temporal events (typically every 15-30 minutes)
 */
(:background)
class BackgroundService extends Sys.ServiceDelegate {

	/**
	 * Constructor for the background service.
	 * Initializes the service delegate for background processing.
	 */
	(:background_method)
	function initialize() {
		Sys.ServiceDelegate.initialize();
	}

	/**
	 * Main background processing method triggered by temporal events.
	 *
	 * This method is called periodically by the system to process pending web requests.
	 * It implements a priority system where city local time requests are processed before
	 * weather requests. Only one request is processed per temporal event to manage
	 * background execution time limits.
	 *
	 * Priority Order:
	 * 1. City local time requests (higher priority - less frequent, user-specific)
	 * 2. Weather requests (lower priority - more frequent, location-based)
	 *
	 * The pending web request flags are only cleared after successful data reception,
	 * ensuring requests are retried if they fail due to network issues.
	 */
	(:background_method)
	function onTemporalEvent() {
		//Sys.println("onTemporalEvent");

		// Check for pending web requests that need to be processed
		var pendingWebRequests = getStorageValue("PendingWebRequests") as PendingWebRequests?;
		if (pendingWebRequests != null) {

			// PRIORITY 1: City local time requests
			// These are processed first as they are user-initiated and less frequent
			if (pendingWebRequests["CityLocalTime"] != null) {
				makeWebRequest(
					"https://script.google.com/macros/s/AKfycbwPas8x0JMVWRhLaraJSJUcTkdznRifXPDovVZh8mviaf8cTw/exec",
					{
						"city" => getPropertyValue("LocalTimeInCity")
					},
					method(:onReceiveCityLocalTime)
				);

			// PRIORITY 2: Weather data requests
			// Processed after city time requests, using current location coordinates
			} else if (pendingWebRequests["OpenWeatherMapCurrent"] != null) {
				var owmKeyOverride = getPropertyValue("OWMKeyOverride");
				makeWebRequest(
					"https://api.openweathermap.org/data/2.5/weather",
					{
						"lat" => getStorageValue("LastLocationLat"),
						"lon" => getStorageValue("LastLocationLng"),

						// API Key Management:
						// Default key is provided for Madre watch face users under Open Source Plan
						// Users can override with their own key if needed
						//
						// IMPORTANT NOTICE from Madre developer:
						// This key is registered under OpenWeatherMap's Open Source Plan with lifted limits
						// for the Current Weather API only. Please do not abuse this key or use it for
						// other APIs (especially One Call API) as it may result in blocking for all users.
						//
						// For your own applications, please register for your own OWM account and key.
						// You can apply for the Open Source Plan to get similar benefits.
						"appid" => ((owmKeyOverride != null) && (owmKeyOverride.length() > 0)) ? owmKeyOverride : "2651f49cb20de925fc57590709b86ce6",

						"units" => "metric" // Request temperature in Celsius
					},
					method(:onReceiveOpenWeatherMapCurrent)
				);
			}
		} /* else {
			// No pending requests - this is normal and expected most of the time
			Sys.println("onTemporalEvent() called with no pending web requests!");
		} */
	}

	/**
	 * Sample successful city local time response:
	 * {
	 *   "requestCity": "london",
	 *   "city": "London",
	 *   "current": {
	 *     "gmtOffset": 3600,    // 1 hour ahead of GMT (BST)
	 *     "dst": true           // Currently in daylight saving time
	 *   },
	 *   "next": {
	 *     "when": **********,   // Unix timestamp of next DST transition
	 *     "gmtOffset": 0,       // Will be GMT after transition
	 *     "dst": false          // Will not be in DST after transition
	 *   }
	 * }
	 */

	/**
	 * Sample error response when city is not found:
	 * {
	 *   "requestCity": "atlantis",
	 *   "error": {
	 *     "code": 2,                              // CITY_NOT_FOUND error code
	 *     "message": "City \"atlantis\" not found." // Human-readable error message
	 *   }
	 * }
	 */

	/**
	 * Callback method for city local time web request responses.
	 *
	 * This method processes the response from the city local time API and handles both
	 * successful responses and HTTP errors. It formats the data appropriately and
	 * exits the background service with the results.
	 *
	 * @param responseCode HTTP response code (200 for success, other codes for errors)
	 * @param data The parsed JSON response data, or null if parsing failed
	 */
	(:background_method)
	function onReceiveCityLocalTime(responseCode as Number, data as CityLocalTimeResponse?) {

		// Handle HTTP failures by creating an error data structure
		if (responseCode != 200) {
			data = {
				"httpError" => responseCode
			};
		}

		// Exit background service and return data to main application
		// The main app will receive this data and can update the UI accordingly
		Bg.exit({
			"CityLocalTime" => data as CityLocalTimeData or HttpErrorData
		});
	}

	/**
	 * Sample OpenWeatherMap API error response (invalid API key):
	 * {
	 *   "cod": 401,
	 *   "message": "Invalid API key. Please see http://openweathermap.org/faq#error401 for more info."
	 * }
	 *
	 * Other common error codes:
	 * - 429: Rate limit exceeded
	 * - 404: City not found
	 * - 500: Internal server error
	 */

	/**
	 * Sample successful OpenWeatherMap current weather response:
	 * This is the full JSON structure returned by the API. The background service
	 * filters this down to only essential data to reduce memory usage.
	 *
	 * {
	 *   "coord": {
	 *     "lon": -0.46,        // Longitude in decimal degrees
	 *     "lat": 51.75         // Latitude in decimal degrees
	 *   },
	 *   "weather": [
	 *     {
	 *       "id": 521,                    // Weather condition ID
	 *       "main": "Rain",               // Weather group (Rain, Snow, Clear, etc.)
	 *       "description": "shower rain", // Detailed description
	 *       "icon": "09d"                 // Icon code (day/night variant)
	 *     }
	 *   ],
	 *   "base": "stations",               // Data source
	 *   "main": {
	 *     "temp": 281.82,                 // Temperature in Kelvin (converted to Celsius with units=metric)
	 *     "pressure": 1018,               // Atmospheric pressure in hPa
	 *     "humidity": 70,                 // Humidity percentage
	 *     "temp_min": 280.15,             // Minimum temperature
	 *     "temp_max": 283.15              // Maximum temperature
	 *   },
	 *   "visibility": 10000,              // Visibility in meters
	 *   "wind": {
	 *     "speed": 6.2,                   // Wind speed in m/s
	 *     "deg": 10                       // Wind direction in degrees
	 *   },
	 *   "clouds": {
	 *     "all": 0                        // Cloudiness percentage
	 *   },
	 *   "dt": 1540741800,                 // Unix timestamp of data calculation
	 *   "sys": {
	 *     "type": 1,                      // Internal parameter
	 *     "id": 5078,                     // Internal parameter
	 *     "message": 0.0036,              // Internal parameter
	 *     "country": "GB",                // Country code
	 *     "sunrise": 1540709390,          // Sunrise time (Unix timestamp)
	 *     "sunset": 1540744829            // Sunset time (Unix timestamp)
	 *   },
	 *   "id": 2647138,                    // City ID
	 *   "name": "Hemel Hempstead",        // City name
	 *   "cod": 200                        // HTTP response code
	 * }
	 */
	/**
	 * Callback method for OpenWeatherMap current weather web request responses.
	 *
	 * This method processes responses from the OpenWeatherMap Current Weather API,
	 * performing critical data filtering to extract only essential information needed
	 * by the watch face. This filtering is crucial for memory management in the
	 * background service environment.
	 *
	 * Data Filtering Rationale:
	 * - Full API response: ~2KB of JSON data with extensive weather details
	 * - Filtered response: ~200 bytes containing only essential watch face data
	 * - Memory reduction: ~90% smaller data structure
	 * - Performance: Faster data transfer and processing in main application
	 *
	 * Error Handling:
	 * - HTTP errors (network issues): Creates httpError structure with response code
	 * - API errors (invalid key, rate limits): Handled by checking data["cod"] field
	 *
	 * @param responseCode HTTP response code (200 for success, other codes for HTTP errors)
	 * @param data The parsed JSON response data, or null if parsing failed
	 */
	(:background_method)
	function onReceiveOpenWeatherMapCurrent(responseCode as Number, data as OpenWeatherMapCurrentResponse?) {
		var result;

		// Process successful HTTP responses and filter essential data
		if (responseCode == 200) {
			data = (data as OpenWeatherMapCurrentSuccessResponse);

			// Extract only the data fields needed by the watch face
			// This dramatically reduces memory usage compared to storing the full response
			result = {
				"cod" => data["cod"],                    // API response code (200 for success)
				"lat" => data["coord"]["lat"],           // Location latitude for verification
				"lon" => data["coord"]["lon"],           // Location longitude for verification
				"dt" => data["dt"],                      // Data timestamp for freshness checking
				"temp" => data["main"]["temp"],          // Temperature in Celsius (primary display data)
				"humidity" => data["main"]["humidity"],  // Humidity percentage (secondary display data)
				"icon" => data["weather"][0]["icon"]     // Weather icon code for visual representation
			};

		// Handle HTTP errors (network failures, server errors, timeouts)
		} else {
			result = {
				"httpError" => responseCode
			};
		}

		// Exit background service and return processed data to main application
		Bg.exit({
			"OpenWeatherMapCurrent" => result as OpenWeatherMapCurrentData or HttpErrorData
		});
	}

	/**
	 * Utility method for making HTTP web requests with standardized configuration.
	 *
	 * This method provides a consistent interface for all web requests made by the
	 * background service, ensuring proper headers, response type handling, and
	 * parameter encoding.
	 *
	 * Configuration:
	 * - Method: GET (suitable for both weather and city time APIs)
	 * - Content-Type: URL encoded (standard for GET requests with parameters)
	 * - Response Type: JSON (both APIs return JSON responses)
	 *
	 * Usage Pattern:
	 * 1. Background service calls this method with URL, parameters, and callback
	 * 2. Garmin Communications API handles the actual HTTP request
	 * 3. Response is parsed as JSON and passed to the specified callback method
	 * 4. Callback processes the response and exits the background service
	 *
	 * @param url The complete URL for the web request
	 * @param params Dictionary of query parameters to include in the request
	 * @param callback Method reference to call when response is received
	 */
	(:background_method)
	function makeWebRequest(url, params, callback) {
		var options = {
			:method => Comms.HTTP_REQUEST_METHOD_GET,                    // Use GET method for all requests
			:headers => {
				"Content-Type" => Communications.REQUEST_CONTENT_TYPE_URL_ENCODED  // Standard content type for GET with params
			},
			:responseType => Comms.HTTP_RESPONSE_CONTENT_TYPE_JSON       // Expect JSON response for automatic parsing
		};

		// Make the actual web request using Garmin Communications API
		Comms.makeWebRequest(url, params, options, callback);
	}
}
