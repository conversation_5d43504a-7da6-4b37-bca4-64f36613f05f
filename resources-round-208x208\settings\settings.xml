<!-- Trouble overriding resources-ciq_1.x with fr45's limited colours, so repeat entire resources-ciq_1.x settings.xml with 
     fr45's overrides -->
<settings>
    <!-- fr45 has 8 colours: black, white, red, green, blue, yellow, cyan, magenta -->
	<setting propertyKey="@Properties.Theme" title="@Strings.ThemeTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.ThemeBlueDark</listEntry><!-- <PERSON><PERSON> is closest -->
			<listEntry value="1">@Strings.ThemePinkDark</listEntry><!-- Magenta -->
			<listEntry value="7">@Strings.ThemeRedDark</listEntry><!-- Red -->
			<listEntry value="2">@Strings.ThemeGreenDark</listEntry><!-- Green -->
			<!--listEntry value="4">@Strings.ThemeCornflowerBlueDark</listEntry-->
			<!--listEntry value="5">@Strings.ThemeLemonCreamDark</listEntry-->
			<!--listEntry value="12">@Strings.ThemeVividYellowDark</listEntry-->
			<listEntry value="14">@Strings.ThemeCornYellowDark</listEntry>
			<!--listEntry value="6">@Strings.ThemeDaygloOrangeDark</listEntry-->
			<!--listEntry value="8">@Strings.ThemeMonoDark</listEntry-->
			<!--listEntry value="3">@Strings.ThemeMonoLight</listEntry-->
			<listEntry value="9">@Strings.ThemeBlueLight</listEntry>
			<listEntry value="10">@Strings.ThemeGreenLight</listEntry>
			<listEntry value="11">@Strings.ThemeRedLight</listEntry>
			<!--listEntry value="13">@Strings.ThemeDaygloOrangeLight</listEntry-->
		</settingConfig>
	</setting>

	<!-- Same as resources-ciq_1.x -->
	<setting propertyKey="@Properties.LeftGoalType" title="@Strings.LeftGoalMeterTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Steps</listEntry>
			<!--listEntry value="1">@Strings.FloorsClimbed</listEntry-->
			<!--listEntry value="2">@Strings.ActiveMinutes</listEntry-->
			<listEntry value="-1">@Strings.Battery</listEntry>
			<listEntry value="-2">@Strings.CaloriesManualGoal</listEntry>
			<listEntry value="-3">@Strings.Off</listEntry>
		</settingConfig>
	</setting>

	<!-- Same as resources-ciq_1.x -->
	<setting propertyKey="@Properties.RightGoalType" title="@Strings.RightGoalMeterTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Steps</listEntry>
			<!--listEntry value="1">@Strings.FloorsClimbed</listEntry-->
			<!--listEntry value="2">@Strings.ActiveMinutes</listEntry-->
			<listEntry value="-1">@Strings.Battery</listEntry>
			<listEntry value="-2">@Strings.CaloriesManualGoal</listEntry>
			<listEntry value="-3">@Strings.Off</listEntry>
		</settingConfig>
	</setting>

	<!-- Same as resources-ciq_1.x -->
	<setting propertyKey="@Properties.Field1Type" title="@Strings.DataField1Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<!--listEntry value="9">@Strings.HeartRateLive5s</listEntry-->
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<!--listEntry value="12">@Strings.Pressure</listEntry-->
			<!--listEntry value="7">@Strings.Temperature</listEntry-->
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<!--listEntry value="11">@Strings.Weather</listEntry-->
			<!--listEntry value="13">@Strings.Humidity</listEntry-->
		</settingConfig>
	</setting>

	<!-- Same as resources-ciq_1.x -->
	<setting propertyKey="@Properties.Field2Type" title="@Strings.DataField2Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<!--listEntry value="9">@Strings.HeartRateLive5s</listEntry-->
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<!--listEntry value="12">@Strings.Pressure</listEntry-->
			<!--listEntry value="7">@Strings.Temperature</listEntry-->
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<!--listEntry value="11">@Strings.Weather</listEntry-->
			<!--listEntry value="13">@Strings.Humidity</listEntry-->
		</settingConfig>
	</setting>

	<!-- Same as resources-ciq_1.x -->
	<setting propertyKey="@Properties.Field3Type" title="@Strings.DataField3Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<!--listEntry value="9">@Strings.HeartRateLive5s</listEntry-->
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<!--listEntry value="12">@Strings.Pressure</listEntry-->
			<!--listEntry value="7">@Strings.Temperature</listEntry-->
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<!--listEntry value="11">@Strings.Weather</listEntry-->
			<!--listEntry value="13">@Strings.Humidity</listEntry-->
		</settingConfig>
	</setting>
	
	<!-- Same as resources-ciq_1.x -->
	<setting propertyKey="@Properties.LocalTimeInCity" title="@Strings.LocalTimeInCityTitle">
		<settingConfig type="alphaNumeric" readonly="true"/>
	</setting>

	<!-- fr45 cannot show grey -->
	<setting propertyKey="@Properties.HoursColourOverride" title="@Strings.HoursColourTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.FromTheme</listEntry>
			<!--listEntry value="1">@Strings.MonoHighlight</listEntry-->
			<listEntry value="2">@Strings.Mono</listEntry>
		</settingConfig>
	</setting>

	<!-- fr45 cannot show grey -->
	<setting propertyKey="@Properties.MinutesColourOverride" title="@Strings.MinutesColourTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.FromTheme</listEntry>
			<!--listEntry value="1">@Strings.MonoHighlight</listEntry-->
			<listEntry value="2">@Strings.Mono</listEntry>
		</settingConfig>
	</setting>
</settings>
