# MonkeyC Debug Troubleshooting Guide

## Masalah: Drawable tidak hilang setelah dihapus dari layout.xml

### Penyebab:
1. **Build cache tidak ter-refresh**
2. **Resource compiler masih menggunakan versi lama**
3. **Simulator cache tidak ter-update**

### Solusi Step-by-Step:

#### 1. Clean Build (WAJIB)
```bash
# Hapus semua file build
rmdir /s /q bin
mkdir bin

# Atau gunakan script
./clean-build.bat
```

#### 2. Verify Layout Changes
- Pastikan perubahan di `resources/layouts/layout.xml` sudah disave
- Check juga layout di folder device-specific seperti:
  - `resources-round-280x280/layouts/layout.xml`
  - `resources-rectangle-240x240/layouts/layout.xml`

#### 3. Full Rebuild
```bash
# Build ulang dengan target device
monkeyc -d fenix7x -f monkey.jungle -o bin/madreface.prg -y %GARMIN_DEVELOPER_KEY%
```

#### 4. Debug di VSCode
1. Set breakpoint di kode
2. Press F5 atau pilih "Debug Madre Face"
3. Pilih target device/simulator

### Common Issues & Solutions:

#### Issue 1: "Program not found"
**Solution:**
- Check apakah file `.prg` ada di folder `bin/`
- Rebuild project dengan `Ctrl+Shift+B`

#### Issue 2: "Debug symbols not found"
**Solution:**
- Pastikan build dengan flag debug: `-g`
- Check file `.prg.debug.xml` ada di `bin/`

#### Issue 3: "Simulator tidak update"
**Solution:**
- Restart Connect IQ Simulator
- Clear simulator cache
- Rebuild project

#### Issue 4: "Layout changes tidak terlihat"
**Solution:**
1. **PENTING:** Clean build dulu!
2. Check multiple layout files untuk device yang berbeda
3. Verify resource path di `monkey.jungle`

### Debug Commands:

#### Manual Build Commands:
```bash
# Clean
rmdir /s /q bin && mkdir bin

# Build for device
monkeyc -d fenix7x -f monkey.jungle -o bin/madreface.prg -y %GARMIN_DEVELOPER_KEY%

# Build for simulator
monkeyc -d simulator -f monkey.jungle -o bin/madreface.prg

# Build with debug info
monkeyc -g -d fenix7x -f monkey.jungle -o bin/madreface.prg -y %GARMIN_DEVELOPER_KEY%
```

#### VSCode Commands:
- `Ctrl+Shift+P` → "Monkey C: Clean"
- `Ctrl+Shift+P` → "Monkey C: Build for Device"
- `F5` → Start Debugging

### Resource Path Priority:
MonkeyC menggunakan resource berdasarkan prioritas:
1. Device-specific folder (e.g., `resources-round-280x280/`)
2. Base resource folder (`resources/`)

**Check semua layout.xml yang relevan!**

### Verification Steps:
1. ✅ Layout.xml sudah diubah dan disave
2. ✅ Clean build dilakukan
3. ✅ Rebuild berhasil tanpa error
4. ✅ File .prg dan .prg.debug.xml ter-generate
5. ✅ Debug session dimulai dengan target yang benar

### Tools yang Tersedia:
- `clean-build.bat` - Clean build artifacts
- `debug-helper.bat` - Interactive debug helper
- VSCode tasks - Build dan clean tasks

### Pro Tips:
1. **Selalu clean build** setelah mengubah resource
2. **Check multiple layout files** untuk device berbeda
3. **Restart simulator** jika perubahan tidak terlihat
4. **Use breakpoints** untuk verify code execution
5. **Check console output** untuk error messages
