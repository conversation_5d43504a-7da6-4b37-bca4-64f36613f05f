@echo off
echo ========================================
echo MonkeyC Debug Setup - Madre Face
echo ========================================
echo.

echo This script will help you setup MonkeyC debugging properly.
echo.

:check_extension
echo 1. Checking Connect IQ Extension...
echo.
echo Please install Connect IQ Extension in VSCode:
echo - Press Ctrl+Shift+X in VSCode
echo - Search for "Connect IQ"
echo - Install "Connect IQ" by Garmin
echo.
echo After installing the extension, the debug configurations will work properly.
echo.

:check_key
echo 2. Checking Developer Key...
echo.
if "%GARMIN_DEVELOPER_KEY%"=="" (
    echo WARNING: GARMIN_DEVELOPER_KEY not set
    echo.
    echo For development/testing, you can create a dummy key:
    echo.
    set /p create_dummy="Create dummy developer key for testing? (y/n): "
    if /i "%create_dummy%"=="y" goto create_dummy_key
    echo.
    echo To get a real developer key:
    echo 1. Go to https://developer.garmin.com/connect-iq/
    echo 2. Create account and download developer key
    echo 3. Set environment variable: set GARMIN_DEVELOPER_KEY=path\to\key.der
    echo.
) else (
    echo ✓ GARMIN_DEVELOPER_KEY is set: %GARMIN_DEVELOPER_KEY%
)

:check_sdk
echo 3. Checking Connect IQ SDK...
echo.
monkeyc --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Connect IQ SDK is installed
    monkeyc --version
) else (
    echo ✗ Connect IQ SDK not found
    echo Please install Connect IQ SDK from:
    echo https://developer.garmin.com/connect-iq/sdk/
)
echo.

:summary
echo ========================================
echo SUMMARY - How to Debug MonkeyC
echo ========================================
echo.
echo Method 1: Using VSCode (Recommended)
echo 1. Install Connect IQ Extension
echo 2. Press F5 in VSCode
echo 3. Choose debug configuration
echo.
echo Method 2: Manual Build + Test
echo 1. Clean build: rmdir /s /q bin && mkdir bin
echo 2. Build: monkeyc -d fenix7x -f monkey.jungle -o bin/madreface.prg -y "path\to\key.der"
echo 3. Test: connectiq bin/madreface.prg
echo.
echo Method 3: Use VSCode Tasks
echo 1. Ctrl+Shift+B (Build)
echo 2. Choose "buildForDevice" or "buildForSimulator"
echo.
echo For your drawable issue:
echo - Always do CLEAN BUILD after changing layout.xml
echo - Use: rmdir /s /q bin && mkdir bin
echo - Then rebuild completely
echo.
pause
goto end

:create_dummy_key
echo Creating dummy developer key for testing...
mkdir keys 2>nul
echo dummy > keys\dummy_key.der
set GARMIN_DEVELOPER_KEY=%cd%\keys\dummy_key.der
echo ✓ Dummy key created: %GARMIN_DEVELOPER_KEY%
echo.
echo NOTE: This is only for testing. Get real key from Garmin for actual development.
echo.
goto check_sdk

:end
echo.
echo Setup complete! You can now debug MonkeyC properly.
echo.
echo Quick test:
echo 1. Make sure Connect IQ Extension is installed
echo 2. Press F5 in VSCode
echo 3. Choose debug configuration
echo.
exit
