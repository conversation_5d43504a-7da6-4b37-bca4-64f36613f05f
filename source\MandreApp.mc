// Import required Garmin Connect IQ SDK modules for main application functionality
using Toybox.Application as App;           // Application base class and lifecycle management
using Toybox.Background as Bg;             // Background processing and temporal events
using Toybox.System as Sys;                // System services and device information
using Toybox.WatchUi as Ui;                // User interface components and watch face framework
using Toybox.Time;                         // Time and date handling utilities
using Toybox.Application.Storage as Storage;    // Persistent data storage (legacy)
using Toybox.Application.Properties as Properties; // Application properties and settings

import Toybox.Lang;
import Toybox.Application;

/**
 * Type definition for tracking pending web requests.
 * Used to coordinate between main application and background service for data fetching.
 */
typedef PendingWebRequests as Dictionary<String, Boolean>;

/**
 * Type definition for formatted time display data.
 * Contains all components needed to display time in various formats (12/24 hour, AM/PM).
 */
typedef FormattedTime as {
	:hour as String,    // Formatted hour string (with or without leading zero)
	:min as String,     // Formatted minute string (always with leading zero)
	:amPm as String     // AM/PM indicator ("a", "p", or empty for 24-hour mode)
};

/**
 * Global variables for current GPS location coordinates.
 *
 * Location Management Strategy:
 * - Previously persisted in App.Storage, but migrated to Object Store due to Issue #86
 * - Issue #86: App.Storage firmware bug causing data corruption on some devices
 * - Current location retrieved/saved in checkPendingWebRequests() method
 * - Persistence enables weather and sunrise/sunset features after watch face restart
 * - Fallback mechanism when GPS location is no longer available from current activity
 *
 * Usage:
 * - Weather API requests require location coordinates
 * - Sunrise/sunset calculations need geographic position
 * - Location cached to reduce GPS power consumption
 * - Coordinates stored as floats for memory efficiency
 */
var gLocationLat = null;    // Current latitude in decimal degrees (null if unavailable)
var gLocationLng = null;    // Current longitude in decimal degrees (null if unavailable)

/**
 * LEGACY STORAGE IMPLEMENTATION (EXCLUDED FROM BUILD)
 *
 * These functions provide the original implementation using separate Properties and Storage APIs.
 * They are excluded from the build due to Issue #86 (App.Storage firmware bug) but kept for
 * reference and potential future use if the firmware bug is resolved.
 *
 * Original Design:
 * - Properties API: User settings and configuration data
 * - Storage API: Runtime data like location coordinates and cached web responses
 *
 * Issue #86 Details:
 * - App.Storage corruption on certain firmware versions
 * - Data loss during watch face restarts
 * - Inconsistent behavior across different device models
 *
 * Migration Strategy:
 * - All data now stored using Object Store (unified storage)
 * - Object Store provides better reliability and consistency
 * - Conditional compilation allows easy rollback if needed
 */

(:properties_and_storage,:background)
function getPropertyValue(key as PropertyKeyType) as PropertyValueType {
	return Properties.getValue(key);
}

(:properties_and_storage,:background)
function setPropertyValue(key as PropertyKeyType, value as PropertyValueType) as Void {
	Properties.setValue(key, value);
}

(:properties_and_storage,:background)
function getStorageValue(key as PropertyKeyType) as PropertyValueType {
	return Storage.getValue(key);
}

(:properties_and_storage,:background)
function setStorageValue(key as PropertyKeyType, value as PropertyValueType) as Void {
	Storage.setValue(key, value);
}

(:properties_and_storage,:background)
function deleteStorageValue(key as PropertyKeyType) as Void {
	Storage.deleteValue(key);
}

/**
 * CURRENT STORAGE IMPLEMENTATION (OBJECT STORE)
 *
 * These functions provide the current implementation using the unified Object Store API.
 * This approach was adopted to work around Issue #86 (App.Storage firmware bug) and
 * provides better reliability and consistency across all device models.
 *
 * Object Store Benefits:
 * - Unified storage for both settings and runtime data
 * - Better reliability compared to separate Properties/Storage APIs
 * - Consistent behavior across different firmware versions
 * - Simplified data management and debugging
 *
 * Implementation Details:
 * - All data stored as application properties in Object Store
 * - No distinction between "properties" and "storage" at API level
 * - Background service can access same data using identical functions
 * - Automatic persistence and synchronization handled by framework
 *
 * Migration Notes:
 * - Property keys remain unchanged for backward compatibility
 * - Data format and structure preserved during migration
 * - Conditional compilation allows easy testing and rollback
 */

(:object_store)
function getPropertyValue(key as PropertyKeyType) as PropertyValueType {
	return App.getApp().getProperty(key);
}

(:object_store)
function setPropertyValue(key as PropertyKeyType, value as PropertyValueType) as Void {
	App.getApp().setProperty(key, value);
}

(:object_store)
function getStorageValue(key as PropertyKeyType) as PropertyValueType {
	return App.getApp().getProperty(key);
}

(:object_store)
function setStorageValue(key as PropertyKeyType, value as PropertyValueType) as Void {
	App.getApp().setProperty(key, value);
}

(:object_store)
function deleteStorageValue(key as PropertyKeyType) as Void {
	App.getApp().deleteProperty(key);
}

/**
 * MadreApp class serves as the main application controller for the Madre watch face.
 *
 * This class extends AppBase and manages the overall application lifecycle, settings,
 * background data processing, and coordination between different watch face components.
 *
 * Key Responsibilities:
 * - Application lifecycle management (initialization, settings changes)
 * - Data field type configuration and validation
 * - Background web request coordination
 * - Location tracking and persistence
 * - Time formatting and display logic
 * - Integration between UI components and data sources
 *
 * Background Processing:
 * - Coordinates with BackgroundService for external data fetching
 * - Manages temporal events for periodic data updates
 * - Handles background data reception and UI updates
 *
 * Settings Management:
 * - Processes user configuration changes
 * - Validates and sanitizes property values
 * - Propagates settings to appropriate UI components
 */
(:background)
class MadreApp extends App.AppBase {

	// Core application components
	var mView;                                  // Reference to main watch face view
	var mFieldTypes as Array<Number?> = new [3]; // Array of configured data field types [Field1, Field2, Field3]

	/**
	 * Constructor for the main application.
	 * Initializes the application base class and prepares for watch face operation.
	 */
	function initialize() {
		AppBase.initialize();
	}

	/*
	// Application lifecycle methods (commented out as they're not currently needed)

	// onStart() is called on application start up
	function onStart(state) {
		// Could be used for initialization that requires the app to be fully loaded
	}

	// onStop() is called when your application is exiting
	function onStop(state) {
		// Could be used for cleanup operations before app termination
	}
	*/

	/**
	 * Returns the initial view of the application.
	 * This method is called by the system to get the main watch face view.
	 *
	 * @return Array containing the main watch face view
	 */
	function getInitialView() {
		mView = new MadreView();
		onSettingsChanged(); // Apply initial settings after creating view
		return [mView];
	}

	/**
	 * Provides access to the main watch face view.
	 * Used by other components that need to interact with the view.
	 *
	 * @return MadreView The main watch face view instance
	 */
	function getView() {
		return mView;
	}

	/**
	 * Safely retrieves an integer property value with type validation and default fallback.
	 *
	 * This method provides robust property access that handles various edge cases:
	 * - Null values (returns default)
	 * - Non-numeric values (converts to number)
	 * - Type safety for property system integration
	 *
	 * @param key The property key to retrieve
	 * @param defaultValue The default value to return if property is null or invalid
	 * @return Number The property value as an integer, or default value
	 */
	function getIntProperty(key, defaultValue) {
		var value = getPropertyValue(key);
		if (value == null) {
			value = defaultValue;
		} else if (!(value instanceof Number)) {
			value = value.toNumber();
		}
		return value;
	}

	/**
	 * Handles application settings changes from the user.
	 *
	 * This method is called when the user modifies watch face settings and is responsible for:
	 * - Updating data field type configuration
	 * - Propagating settings changes to the view
	 * - Triggering background web request checks
	 * - Requesting UI update to reflect changes
	 *
	 * Settings Processing Order:
	 * 1. Update field type configuration (Field1Type, Field2Type, Field3Type)
	 * 2. Notify view of settings changes (triggers theme updates, font changes, etc.)
	 * 3. Check for new background web request requirements
	 * 4. Request UI update to display changes immediately
	 */
	function onSettingsChanged() {
		// Update data field type configuration with safe defaults
		mFieldTypes[0] = getIntProperty("Field1Type", 0);  // Default: Heart Rate
		mFieldTypes[1] = getIntProperty("Field2Type", 1);  // Default: Battery
		mFieldTypes[2] = getIntProperty("Field3Type", 2);  // Default: Notifications

		// Propagate settings changes to view (handles themes, fonts, layout updates)
		mView.onSettingsChanged(); // Also calls checkPendingWebRequests()

		// Request immediate UI update to show changes
		Ui.requestUpdate();
	}

	/**
	 * Checks if a specific field type is currently configured for display.
	 *
	 * This method is used to determine if certain data fields are active, which affects:
	 * - Background web request requirements (weather, humidity fields)
	 * - Data processing and caching decisions
	 * - UI component visibility and updates
	 *
	 * @param fieldType The field type to check for (from FIELD_TYPES enum)
	 * @return Boolean True if the field type is configured in any of the three field slots
	 */
	function hasField(fieldType) {
		return ((mFieldTypes[0] == fieldType) ||
			(mFieldTypes[1] == fieldType) ||
			(mFieldTypes[2] == fieldType));
	}

	/**
	 * Determines if any web requests are needed and schedules background processing.
	 *
	 * This method is the central coordinator for background data fetching and performs:
	 * - Location tracking and persistence
	 * - Analysis of data freshness and requirements
	 * - Scheduling of background temporal events
	 * - Management of pending web request flags
	 *
	 * Called From:
	 * - Layout initialization (first run setup)
	 * - Settings changes (new data field requirements)
	 * - Exiting sleep mode (periodic data refresh)
	 *
	 * Background Processing Flow:
	 * 1. Update and persist current GPS location
	 * 2. Check city local time data requirements
	 * 3. Check weather data requirements
	 * 4. Schedule background temporal event if requests pending
	 * 5. Store pending request flags for BackgroundService
	 */
	(:background_method)
	function checkPendingWebRequests() {

		// LOCATION MANAGEMENT
		// Attempt to update current location for Sunrise/Sunset and Weather features
		// Location persistence ensures features work after watch face restart even if GPS unavailable
		var location = Activity.getActivityInfo().currentLocation;
		if (location != null) {
			// Fresh GPS location available - save it for future use
			// Sys.println("Saving location");
			location = location.toDegrees(); // Convert to Array of Doubles (decimal degrees)
			gLocationLat = location[0].toFloat();  // Store as float for memory efficiency
			gLocationLng = location[1].toFloat();

			// Persist location to Object Store for use after restarts
			setStorageValue("LastLocationLat", gLocationLat);
			setStorageValue("LastLocationLng", gLocationLng);

		// GPS location not available - try to restore from persistent storage
		} else {
			// Carefully restore stored location without overwriting valid in-memory values
			var lat = getStorageValue("LastLocationLat");
			if (lat != null) {
				gLocationLat = lat;
			}

			var lng = getStorageValue("LastLocationLng");
			if (lng != null) {
				gLocationLng = lng;
			}
		}
		// Debug output: Sys.println(gLocationLat + ", " + gLocationLng);

		// Early exit if background processing not supported on this device
		if (!(Sys has :ServiceDelegate)) {
			return;
		}

		// Initialize or retrieve existing pending web requests tracking
		var pendingWebRequests = getStorageValue("PendingWebRequests") as PendingWebRequests?;
		if (pendingWebRequests == null) {
			pendingWebRequests = {};
		}

		// CITY LOCAL TIME REQUEST ANALYSIS
		// Check if city local time data is needed and current
		var city = getPropertyValue("LocalTimeInCity");

		// Issue #78: Setting with empty string value may cause property to be null
		if ((city != null) && (city.length() > 0)) {

			var cityLocalTime = getStorageValue("CityLocalTime") as CityLocalTimeResponse?;

			// Request new data if no existing data available
			if ((cityLocalTime == null) ||

			// Request new data if existing data has expired (past DST transition time)
			(((cityLocalTime as CityLocalTimeSuccessResponse)["next"] != null) &&
			 (Time.now().value() >= (cityLocalTime as CityLocalTimeSuccessResponse)["next"]["when"]))) {

				pendingWebRequests["CityLocalTime"] = true;

			// Request new data if existing data is for a different city
			// This handles both city changes and error responses from server
			// Error responses contain requestCity field to prevent repeated requests for invalid cities
			} else if (!cityLocalTime["requestCity"].equals(city)) {

				deleteStorageValue("CityLocalTime");  // Clear old city data
				pendingWebRequests["CityLocalTime"] = true;
			}
		}

		// WEATHER DATA REQUEST ANALYSIS
		// Check if weather data is needed based on location availability and field configuration
		// Weather data required if: location available AND (weather field OR humidity field displayed)
		if ((gLocationLat != null) &&
			(hasField(FIELD_TYPE_WEATHER) || hasField(FIELD_TYPE_HUMIDITY))) {

			var owmCurrent = getStorageValue("OpenWeatherMapCurrent") as OpenWeatherMapCurrentData?;

			// Request new data if no existing weather data available
			if (owmCurrent == null) {

				pendingWebRequests["OpenWeatherMapCurrent"] = true;

			// Analyze existing weather data for freshness and location accuracy
			} else if (owmCurrent["cod"] == 200) {

				// Request new data if existing data is older than 30 minutes (1800 seconds)
				// TODO: Consider requesting weather at sunrise/sunset to update day/night weather icons
				if ((Time.now().value() > (owmCurrent["dt"] + 1800)) ||

				// Request new data if existing data is for a significantly different location
				// Location comparison uses 0.02 degree threshold (~1.4 miles at equator)
				// Note: This is an approximation since longitude distance varies by latitude
				// (69 miles at equator, 0 miles at poles), but simpler than true distance calculation
				(((gLocationLat - owmCurrent["lat"]).abs() > 0.02) ||
				 ((gLocationLng - owmCurrent["lon"]).abs() > 0.02))) {

					pendingWebRequests["OpenWeatherMapCurrent"] = true;
				}
			}
		}

		// TEMPORAL EVENT SCHEDULING
		// If any web requests are pending, schedule background processing
		if (pendingWebRequests.keys().size() > 0) {

			// Register for background temporal event as soon as possible
			var lastTime = Bg.getLastTemporalEventTime();
			if (lastTime != null) {
				// Schedule next event 5 minutes after the last one
				// Events scheduled for a time in the past trigger immediately
				var nextTime = lastTime.add(new Time.Duration(5 * 60));
				Bg.registerForTemporalEvent(nextTime);
			} else {
				// No previous temporal event - schedule immediately
				Bg.registerForTemporalEvent(Time.now());
			}
		}

		// Persist pending request flags for BackgroundService to process
		setStorageValue("PendingWebRequests", pendingWebRequests);
	}

	/**
	 * Provides the background service delegate for temporal event processing.
	 *
	 * This method is called by the system when background processing is needed.
	 * Returns an array containing the BackgroundService instance that will handle
	 * web requests and data processing in the background.
	 *
	 * @return Array containing BackgroundService delegate instance
	 */
	(:background_method)
	function getServiceDelegate() {
		return [new BackgroundService()];
	}

	/**
	 * Handles data received from BackgroundService after web request completion.
	 *
	 * This method processes the results of background web requests and manages:
	 * - Data validation and error handling
	 * - Clearing of pending request flags on success
	 * - Storage of received data for UI consumption
	 * - UI update triggering to display new data
	 *
	 * Data Structure:
	 * The data parameter is a Dictionary with a single key indicating the data type
	 * (e.g., "CityLocalTime", "OpenWeatherMapCurrent") and the corresponding data value.
	 * This structure matches Object Store keys and pendingWebRequests flags.
	 *
	 * Error Handling:
	 * - HTTP errors are ignored (no UI update, flag remains set for retry)
	 * - API errors are stored and displayed to user
	 * - Successful data clears pending flags and triggers UI update
	 *
	 * @param data Dictionary containing received background data
	 */
	(:background_method)
	function onBackgroundData(data) {
		var pendingWebRequests = getStorageValue("PendingWebRequests");
		if (pendingWebRequests == null) {
			// This shouldn't happen, but handle gracefully
			//Sys.println("onBackgroundData() called with no pending web requests!");
			pendingWebRequests = {};
		}

		var type = data.keys()[0]; // Extract data type (e.g., "CityLocalTime")
		var storedData = getStorageValue(type);
		var receivedData = (data as Dictionary<String, CityLocalTimeData or OpenWeatherMapCurrentData or HttpErrorData>)[type]; // Extract actual data

		// Handle HTTP errors by leaving pending flag set for retry
		// No value in showing HTTP errors to user (network issues, timeouts, etc.)
		if (receivedData["httpError"]) {
			return;
		}

		// Process successful data reception
		storedData = receivedData;
		pendingWebRequests.remove(type);  // Clear pending flag for this data type
		setStorageValue("PendingWebRequests", pendingWebRequests);
		setStorageValue(type, storedData);

		// Trigger UI update to display new data
		Ui.requestUpdate();
	}

	/**
	 * Returns a formatted time dictionary that respects user preferences and device settings.
	 *
	 * This method handles all time formatting logic for the watch face, including:
	 * - 12-hour vs 24-hour format based on device settings
	 * - AM/PM indicator handling for 12-hour mode
	 * - Leading zero display preferences
	 * - Special cases for noon and midnight display
	 *
	 * Input Parameters:
	 * @param hour Hour value (0-23 in 24-hour format)
	 * @param min Minute value (0-59)
	 *
	 * @return FormattedTime Dictionary containing formatted time components
	 *
	 * Special Cases Handled:
	 * - Issue #6: Noon (12:00) correctly shown as PM, not AM
	 * - Issue #27: Midnight (00:00) shown as 12:00 AM, not 00:00 AM
	 * - Issue #10: Leading zero hiding in 12-hour mode when setting enabled
	 * - Issue #69: Leading zero setting now applies to both 12-hour and 24-hour modes
	 *
	 * Format Examples:
	 * - 24-hour mode: "09:30" or "9:30" (based on leading zero setting)
	 * - 12-hour mode: "9:30a" or "09:30a" (based on leading zero setting)
	 */
	function getFormattedTime(hour, min) as FormattedTime {
		var amPm = "";

		// Handle 12-hour format conversion and AM/PM indicators
		if (!Sys.getDeviceSettings().is24Hour) {

			// Issue #6: Ensure noon (12:xx) is correctly shown as PM
			var isPm = (hour >= 12);
			if (isPm) {

				// Convert 13-23 to 1-11, but keep 12 as 12 (noon)
				if (hour > 12) {
					hour = hour - 12;
				}
				amPm = "p";
			} else {

				// Issue #27: Ensure midnight (00:xx) is shown as 12:xx AM, not 00:xx AM
				if (hour == 0) {
					hour = 12;
				}
				amPm = "a";
			}
		}

		// Apply leading zero formatting based on user preference
		// Issue #10: Originally applied only to 12-hour mode
		// Issue #69: Setting now applies to both 12-hour and 24-hour modes
		hour = hour.format(getPropertyValue("HideHoursLeadingZero") ? INTEGER_FORMAT : "%02d");

		return {
			:hour => hour,                    // Formatted hour string
			:min => min.format("%02d"),       // Formatted minute string (always with leading zero)
			:amPm => amPm                     // AM/PM indicator ("a", "p", or empty for 24-hour)
		};
	}
}
