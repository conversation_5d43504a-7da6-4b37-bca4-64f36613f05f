# Madre Face - Build Instructions

This document explains how to build the Madre Face Connect IQ watch face using the provided Makefile.

## Prerequisites

1. **Connect IQ SDK** installed
2. **OpenSSL** installed (for key generation)
3. **Developer Key** (`developer_key.der`) - can be generated automatically
4. **MonkeyC compiler** accessible

## First Time Setup

### Generate Developer Key

Before building your app, you need a developer key. You can generate one automatically:

**Windows:**
```cmd
# Using batch script
.\build.bat gen-key

# Using PowerShell
.\build.ps1 gen-key

# Or manually using dedicated scripts
.\generate-key.bat
.\generate-key.ps1
```

**Linux/macOS:**
```bash
# Using bash script
./generate-key.sh

# Or with custom name
./generate-key.sh my_custom_key
```

This will create:
- `developer_key.pem` - Private key in PEM format (keep secure)
- `developer_key.der` - Private key in DER format (used by Garmin SDK)

## Quick Start

### Windows (Batch Script - Recommended)
```cmd
# Generate developer key (first time setup)
.\build.bat gen-key

# Build for default device (Instinct 3 AMOLED 45mm)
.\build.bat build

# Build and run in simulator
.\build.bat run

# Build for specific device
.\build.bat build venu2

# Build release version
.\build.bat publish
```

### Windows (PowerShell)
```powershell
# Generate developer key (first time setup)
.\build.ps1 gen-key

# Build for default device
.\build.ps1 build

# Build and run in simulator
.\build.ps1 run

# Build for specific device
.\build.ps1 build venu2

# Build release version
.\build.ps1 publish
```

### Windows (Make - if installed)
```bash
# Build for default device
make build

# Build and run in simulator
make run

# Build for specific device
make build PRODUCT=venu2

# Build release version
make publish
```

### Linux/macOS
```bash
# Use the Linux Makefile
make -f Makefile.linux build

# Or copy it as default
cp Makefile.linux Makefile
make build
```

## Available Commands

### Development Commands

| Command | Description | Example |
|---------|-------------|---------|
| `make build` | Build for development | `make build PRODUCT=fenix7` |
| `make run` | Build and run in simulator | `make run PRODUCT=round-240x240` |
| `make build-sim` | Build for round simulator | `make build-sim` |
| `make run-sim` | Build and run round simulator | `make run-sim` |
| `make simulator` | Launch Connect IQ Simulator | `make simulator` |

### Release Commands

| Command | Description |
|---------|-------------|
| `make publish` | Build release .iq file |
| `make release` | Clean and build release |
| `make build-release-test` | Build with release optimizations for testing |

### Key Generation Commands

| Command | Description | Platform |
|---------|-------------|----------|
| `.\build.bat gen-key` | Generate developer key via build script | Windows |
| `.\build.ps1 gen-key` | Generate developer key via PowerShell | Windows |
| `.\generate-key.bat` | Standalone key generation (batch) | Windows |
| `.\generate-key.ps1` | Standalone key generation (PowerShell) | Windows |
| `./generate-key.sh` | Standalone key generation (bash) | Linux/macOS |

### Utility Commands

| Command | Description |
|---------|-------------|
| `make clean` | Clean build artifacts |
| `make clean-all` | Clean all generated files |
| `make test-devices` | Build for multiple common devices |
| `make validate` | Validate project structure |
| `make info` | Show project information |
| `make devices` | List available devices |
| `make help` | Show help information |

## Configuration

### SDK Path Configuration

**Windows (Makefile):**
```makefile
SDK_PATH = C:/Users/<USER>/AppData/Roaming/Garmin/ConnectIQ/Sdks/connectiq-sdk-win-8.1.1-2025-03-27-66dae750f
```

**Linux/macOS (Makefile.linux):**
```makefile
SDK_PATH = ~/.Garmin/ConnectIQ/Sdks/connectiq-sdk-lin-8.1.1-2025-03-27-66dae750f
```

Update these paths to match your Connect IQ SDK installation.

### Project Configuration

```makefile
PROJECT_NAME = madreface           # Output .prg filename
APP_NAME = madre-face             # Output .iq filename
DEVELOPER_KEY = developer_key.der # Your developer key file
PRODUCT = instinct3amoled45mm     # Default target device
```

## Common Device IDs

### AMOLED Devices (with Always-On Display)
- `instinct3amoled45mm` - Garmin Instinct 3 AMOLED 45mm
- `instinct3amoled50mm` - Garmin Instinct 3 AMOLED 50mm
- `venu2` - Garmin Venu 2
- `venu3` - Garmin Venu 3
- `epix2` - Garmin Epix 2

### Popular Devices
- `fenix7` - Garmin Fenix 7
- `fr965` - Forerunner 965
- `fr255` - Forerunner 255
- `vivoactive4` - Vivoactive 4

### Simulator
- `round-240x240` - Round simulator (240x240 pixels)
- `rectangle-240x240` - Rectangle simulator

## Build Examples

### Development Workflow
```bash
# 1. Build for your target device
make build PRODUCT=instinct3amoled45mm

# 2. Test in simulator
make run-sim

# 3. Build for multiple devices to test compatibility
make test-devices

# 4. Clean and build release when ready
make release
```

### Device-Specific Builds
```bash
# Build for Garmin Instinct 3 AMOLED
make build PRODUCT=instinct3amoled45mm

# Build for Garmin Venu 2
make build PRODUCT=venu2

# Build for Garmin Fenix 7
make build PRODUCT=fenix7

# Build for simulator testing
make build PRODUCT=round-240x240
```

### Release Build
```bash
# Clean and build release .iq file
make release

# The output will be: bin/madre-face.iq
```

## Troubleshooting

### Common Issues

1. **SDK Path Not Found**
   ```
   Error: monkeyc command not found
   ```
   **Solution:** Update `SDK_PATH` in Makefile to match your installation.

2. **Developer Key Missing**
   ```
   Error: Developer key not found: developer_key.der
   ```
   **Solution:** Place your developer key file in the project root or update `DEVELOPER_KEY` path.

3. **Build Fails**
   ```
   Error: Unable to load private key
   ```
   **Solution:** Ensure your developer key file is valid and accessible.

4. **Key Generation Fails**
   ```
   Error: OpenSSL not found
   ```
   **Solution:** Install OpenSSL:
   - Windows: Install Git for Windows (includes OpenSSL) or download from https://slproweb.com/products/Win32OpenSSL.html
   - macOS: `brew install openssl`
   - Ubuntu/Debian: `sudo apt-get install openssl`
   - CentOS/RHEL: `sudo yum install openssl`

5. **Key Already Exists**
   ```
   Key files already exist!
   ```
   **Solution:** Use `--force` or `-Force` parameter to overwrite existing keys.

### Validation
```bash
# Check project structure and dependencies
make validate

# Show current configuration
make info

# List all available devices
make devices
```

## File Structure

```
madre-face/
├── Makefile                    # Windows Makefile (requires Make utility)
├── Makefile.linux              # Linux/macOS Makefile
├── build.bat                   # Windows Batch Script (recommended for Windows)
├── build.ps1                   # Windows PowerShell Script
├── generate-key.bat            # Windows Batch Key Generator
├── generate-key.ps1            # Windows PowerShell Key Generator
├── generate-key.sh             # Linux/macOS Bash Key Generator
├── BUILD.md                    # This file
├── developer_key.der           # Your developer key (generated)
├── developer_key.pem           # Your developer key PEM format (generated)
├── monkey.jungle               # Project configuration
├── manifest.xml                # App manifest
├── source/                     # Source code
├── resources/                  # Resources
├── always-on-source/           # Always-on display code
└── bin/                        # Build output
    ├── madreface.prg           # Development build
    └── madre-face.iq           # Release build
```

## Build Scripts Overview

| Script | Platform | Requirements | Recommended |
|--------|----------|--------------|-------------|
| `build.bat` | Windows | Command Prompt/PowerShell | ✅ Yes |
| `build.ps1` | Windows | PowerShell | ✅ Yes |
| `Makefile` | Windows | Make utility | ❌ Optional |
| `Makefile.linux` | Linux/macOS | Make utility | ✅ Yes |

## Advanced Usage

### Custom Build Flags
```bash
# Build with maximum debug output
make build-debug PRODUCT=round-240x240

# Build with release optimizations but keep debug info
make build-release-test PRODUCT=instinct3amoled45mm
```

### Environment Variables
```bash
# Override default product
export PRODUCT=venu2
make build

# Use different developer key
make build DEVELOPER_KEY=my-other-key.der
```

## Integration with IDEs

### VS Code
Add to `.vscode/tasks.json`:
```json
{
    "label": "Build Watch Face",
    "type": "shell",
    "command": "make",
    "args": ["build", "PRODUCT=${input:device}"],
    "group": "build"
}
```

### Command Line Aliases
```bash
# Add to your shell profile (.bashrc, .zshrc, etc.)
alias ciq-build='make build'
alias ciq-run='make run-sim'
alias ciq-release='make release'
```
