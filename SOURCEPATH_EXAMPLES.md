# monkey.jungle sourcePath Examples

This document shows practical examples of how different devices use different sourcePath configurations.

## 🎯 **Real Examples from Your Project**

### **Example 1: instinct3amoled45mm (Always-On Device)**

#### **monkey.jungle Configuration:**
```
Line 4:   base.sourcePath = source
Line 111: instinct3amoled45mm.sourcePath = $(instinct3amoled45mm.sourcePath);always-on-source
```

#### **Resolution Process:**
```
1. Base sourcePath: "source"
2. Device-specific: "$(instinct3amoled45mm.sourcePath);always-on-source"
3. $(instinct3amoled45mm.sourcePath) resolves to base: "source"
4. Final sourcePath: "source;always-on-source"
```

#### **Files Included in Build:**
```
📁 source/
├── 📄 MandreApp.mc           ✅ Included
├── 📄 MadreView.mc           ✅ Included
├── 📄 BackgroundService.mc   ✅ Included
├── 📄 DataArea.mc            ✅ Included
└── 📄 *.mc                   ✅ All included

📁 always-on-source/
└── 📄 AlwaysOnDisplay.mc     ✅ Included (Always-on support)
```

#### **Analyzer Tool Output:**
```powershell
PS> .\analyze-jungle.ps1 instinct3amoled45mm

SOURCEPATH ANALYSIS FOR: instinct3amoled45mm
======================================

Base sourcePath:
  source

Device-specific sourcePath:
  $(instinct3amoled45mm.sourcePath);always-on-source

Parsed device sourcePath:
  Base: source
  Addition: ;always-on-source
  Final: source;always-on-source
```

### **Example 2: fenix7 (Standard Device)**

#### **monkey.jungle Configuration:**
```
Line 4: base.sourcePath = source
(No fenix7.sourcePath defined)
```

#### **Resolution Process:**
```
1. Base sourcePath: "source"
2. No device-specific sourcePath found
3. Final sourcePath: "source"
```

#### **Files Included in Build:**
```
📁 source/
├── 📄 MandreApp.mc           ✅ Included
├── 📄 MadreView.mc           ✅ Included
├── 📄 BackgroundService.mc   ✅ Included
├── 📄 DataArea.mc            ✅ Included
└── 📄 *.mc                   ✅ All included

📁 always-on-source/
└── 📄 AlwaysOnDisplay.mc     ❌ NOT Included (No always-on support)
```

#### **Analyzer Tool Output:**
```powershell
PS> .\analyze-jungle.ps1 fenix7

SOURCEPATH ANALYSIS FOR: fenix7
======================================

Base sourcePath:
  source

Device-specific sourcePath:
  (not defined - using base only)

Final sourcePath:
  source
```

## 🔄 **Build Process Comparison**

### **Always-On Device Build (instinct3amoled45mm):**
```bash
# MonkeyC compiler includes:
monkeyc -f monkey.jungle -d instinct3amoled45mm -o app.prg

# Equivalent to:
monkeyc \
  --source source \
  --source always-on-source \
  --device instinct3amoled45mm \
  --output app.prg
```

### **Standard Device Build (fenix7):**
```bash
# MonkeyC compiler includes:
monkeyc -f monkey.jungle -d fenix7 -o app.prg

# Equivalent to:
monkeyc \
  --source source \
  --device fenix7 \
  --output app.prg
```

## 📊 **Device Categories in Your Project**

### **🔋 Always-On Display Devices (36 total):**

#### **Instinct Series:**
- `instinct3amoled45mm` ✅
- `instinct3amoled50mm` ✅

#### **Venu Series:**
- `venu` ✅
- `venu2` ✅
- `venu2s` ✅
- `venu2plus` ✅
- `venu3` ✅
- `venu3s` ✅
- `venud` ✅
- `venusq` ✅
- `venusq2` ✅
- `venusq2m` ✅
- `venusqm` ✅

#### **Fenix 8 Series:**
- `fenix843mm` ✅
- `fenix847mm` ✅
- `fenixe` ✅

#### **Forerunner Series:**
- `fr165` ✅
- `fr165m` ✅
- `fr265` ✅
- `fr265s` ✅
- `fr965` ✅

#### **Epix 2 Series:**
- `epix2` ✅
- `epix2pro42mm` ✅
- `epix2pro47mm` ✅
- `epix2pro51mm` ✅

#### **Other Series:**
- `vivoactive5` ✅
- `vivoactive6` ✅
- `approachs7042mm` ✅
- `approachs7047mm` ✅
- `d2air` ✅
- `d2airx10` ✅
- `d2mach1` ✅
- `descentmk343mm` ✅
- `descentmk351mm` ✅
- `marq2` ✅
- `marq2aviator` ✅

### **📱 Standard Devices (No custom sourcePath):**

#### **Fenix 7 Series:**
- `fenix7` ❌
- `fenix7s` ❌
- `fenix7x` ❌

#### **Forerunner Series:**
- `fr245` ❌
- `fr245m` ❌
- `fr945` ❌
- `fr955` ❌

#### **Vivoactive Series:**
- `vivoactive4` ❌
- `vivoactive4s` ❌

#### **And many more older devices...**

## 🛠️ **Practical Usage**

### **Check Before Building:**
```powershell
# Always check sourcePath before building
.\analyze-jungle.ps1 instinct3amoled45mm
# Result: source;always-on-source (includes always-on code)

.\analyze-jungle.ps1 fenix7
# Result: source (standard build only)
```

### **Conditional Code Based on Device:**
```monkeyc
// In your MonkeyC code, you can check device capabilities
using Toybox.System as System;

class MadreView extends Ui.WatchFace {
    function initialize() {
        WatchFace.initialize();
        
        // Check if device supports always-on display
        var deviceSettings = System.getDeviceSettings();
        if (deviceSettings has :isAlwaysOnDisplaySupported && 
            deviceSettings.isAlwaysOnDisplaySupported) {
            System.println("Device supports always-on display");
        } else {
            System.println("Device does NOT support always-on display");
        }
    }
}
```

### **Build Commands for Different Devices:**
```bash
# Always-on device (includes always-on-source/)
monkeyc -f monkey.jungle -d instinct3amoled45mm -o instinct3.prg

# Standard device (source/ only)
monkeyc -f monkey.jungle -d fenix7 -o fenix7.prg

# Check what files are included (verbose)
monkeyc -f monkey.jungle -d instinct3amoled45mm -o test.prg -v
```

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **Issue 1: "AlwaysOnDisplay class not found"**
**Cause:** Building for always-on device but `always-on-source/` missing
**Check:** `.\analyze-jungle.ps1 [device]` - should show `source;always-on-source`
**Solution:** Ensure `always-on-source/AlwaysOnDisplay.mc` exists

#### **Issue 2: "Undefined symbol" for always-on methods**
**Cause:** Code references always-on classes but device doesn't include always-on-source
**Check:** `.\analyze-jungle.ps1 [device]` - should show `source` only
**Solution:** Use conditional compilation or check device capabilities

#### **Issue 3: Build works for one device but not another**
**Cause:** Different sourcePath configurations
**Check:** Compare devices: `.\analyze-jungle.ps1 device1` vs `.\analyze-jungle.ps1 device2`
**Solution:** Ensure code is compatible with both configurations

## 📋 **Quick Commands Reference**

```powershell
# Check any device
.\analyze-jungle.ps1 [device_name]

# List all always-on devices
.\analyze-jungle.ps1 -ListAll

# Examples
.\analyze-jungle.ps1 instinct3amoled45mm  # Always-on device
.\analyze-jungle.ps1 fenix7               # Standard device
.\analyze-jungle.ps1 venu2                # Always-on device
.\analyze-jungle.ps1 fr245                # Standard device
```

## 🎯 **Summary**

- **36 devices** use `source;always-on-source` (always-on display support)
- **All other devices** use `source` only (standard build)
- **Use analyzer tool** to check any device: `.\analyze-jungle.ps1 [device]`
- **Always-on devices** include additional `AlwaysOnDisplay.mc` file
- **Standard devices** build faster with smaller memory footprint

**Now you know exactly which sourcePath any device uses!** 🚀
