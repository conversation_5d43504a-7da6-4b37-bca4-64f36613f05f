<settings>

	<setting propertyKey="@Properties.LeftGoalType" title="@Strings.LeftGoalMeterTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Steps</listEntry>
			<!--listEntry value="1">@Strings.FloorsClimbed</listEntry-->
			<listEntry value="2">@Strings.ActiveMinutes</listEntry>
			<listEntry value="-1">@Strings.Battery</listEntry>
			<listEntry value="-2">@Strings.CaloriesManualGoal</listEntry>
			<listEntry value="-3">@Strings.Off</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.RightGoalType" title="@Strings.RightGoalMeterTitle">
		<settingConfig type="list">
			<listEntry value="0">@Strings.Steps</listEntry>
			<!--listEntry value="1">@Strings.FloorsClimbed</listEntry-->
			<listEntry value="2">@Strings.ActiveMinutes</listEntry>
			<listEntry value="-1">@Strings.Battery</listEntry>
			<listEntry value="-2">@Strings.CaloriesManualGoal</listEntry>
			<listEntry value="-3">@Strings.Off</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Field1Type" title="@Strings.DataField1Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<listEntry value="9">@Strings.HeartRateLive5s</listEntry>
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<!--listEntry value="12">@Strings.Pressure</listEntry-->
			<!--listEntry value="7">@Strings.Temperature</listEntry-->
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<listEntry value="11">@Strings.Weather</listEntry>
			<listEntry value="13">@Strings.Humidity</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Field2Type" title="@Strings.DataField2Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<listEntry value="9">@Strings.HeartRateLive5s</listEntry>
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<!--listEntry value="12">@Strings.Pressure</listEntry-->
			<!--listEntry value="7">@Strings.Temperature</listEntry-->
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<listEntry value="11">@Strings.Weather</listEntry>
			<listEntry value="13">@Strings.Humidity</listEntry>
		</settingConfig>
	</setting>

	<setting propertyKey="@Properties.Field3Type" title="@Strings.DataField3Title">
		<settingConfig type="list">
			<listEntry value="0">@Strings.HeartRate</listEntry>
			<listEntry value="9">@Strings.HeartRateLive5s</listEntry>
			<listEntry value="1">@Strings.Battery</listEntry>
			<listEntry value="8">@Strings.BatteryHidePercentage</listEntry>
			<listEntry value="2">@Strings.Notifications</listEntry>
			<listEntry value="3">@Strings.Calories</listEntry>
			<listEntry value="4">@Strings.Distance</listEntry>
			<listEntry value="5">@Strings.Alarms</listEntry>
			<listEntry value="6">@Strings.Altitude</listEntry>
			<!--listEntry value="12">@Strings.Pressure</listEntry-->
			<!--listEntry value="7">@Strings.Temperature</listEntry-->
			<listEntry value="10">@Strings.SunriseSunset</listEntry>
			<listEntry value="11">@Strings.Weather</listEntry>
			<listEntry value="13">@Strings.Humidity</listEntry>
		</settingConfig>
	</setting>

</settings>
