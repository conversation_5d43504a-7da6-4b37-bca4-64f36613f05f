# Indicators Documentation

## Overview

The `Indicators` class is a flexible and configurable system for displaying device status indicators on the watch face. It provides visual feedback for various device states including Bluetooth connectivity, notifications, alarms, and battery level through a unified interface that adapts to different screen layouts and user preferences.

## Purpose and Design Philosophy

### Core Objectives
- **Status Visibility**: Provide at-a-glance information about important device states
- **User Customization**: Allow users to choose which indicators are most important to them
- **Layout Flexibility**: Support both horizontal and vertical layouts for different screen types
- **Visual Clarity**: Use clear, recognizable icons with intuitive active/inactive states
- **Performance**: Efficient rendering with minimal resource usage

### Design Principles
- **Modularity**: Each indicator type is independent and can be configured separately
- **Scalability**: Support for 1-3 indicators with automatic spacing and positioning
- **Adaptability**: Conditional compilation for different layout orientations
- **Consistency**: Unified visual language across all indicator types

## Architecture

### Class Hierarchy
```
Ui.Drawable
    └── Indicators
```

### Component Structure
```
Indicators
├── Layout Management
│   ├── Horizontal Layout (conditional compilation)
│   └── Vertical Layout (conditional compilation)
├── Indicator Types
│   ├── Bluetooth Connection
│   ├── Active Alarms
│   ├── Unread Notifications
│   ├── Smart Bluetooth/Notifications
│   └── Battery Level Meter
└── Rendering System
    ├── Icon-based Indicators
    └── Graphical Battery Meter
```

## Indicator Types

### 1. Bluetooth Connection (Type 0)
- **Purpose**: Shows phone connection status
- **Icon**: Bluetooth symbol ("8" in icon font)
- **Active State**: Phone is connected (theme color)
- **Inactive State**: Phone is disconnected (muted color)
- **Use Case**: Monitor connectivity for notifications and data sync

### 2. Active Alarms (Type 1)
- **Purpose**: Indicates if any alarms are currently set
- **Icon**: Alarm clock symbol (":" in icon font)
- **Active State**: One or more alarms are active (theme color)
- **Inactive State**: No alarms are set (muted color)
- **Use Case**: Quick verification that alarms are configured

### 3. Unread Notifications (Type 2)
- **Purpose**: Shows presence of unread notifications
- **Icon**: Notification bell ("5" in icon font)
- **Active State**: Unread notifications exist (theme color)
- **Inactive State**: No unread notifications (muted color)
- **Use Case**: Alert user to pending messages or alerts

### 4. Smart Bluetooth/Notifications (Type 3)
- **Purpose**: Intelligent indicator that prioritizes notifications over Bluetooth
- **Logic**: Shows notifications when connected AND notifications exist, otherwise shows Bluetooth
- **Icons**: Dynamically switches between notification bell and Bluetooth symbol
- **Use Case**: Maximize information density by combining related indicators

### 5. Battery Level Meter (Type 4)
- **Purpose**: Graphical representation of battery level
- **Rendering**: Custom battery meter graphic (not icon-based)
- **Visual**: Filled rectangle with battery outline
- **Use Case**: Precise battery level indication

## Layout Systems

### Horizontal Layout
**Target Devices**: Rectangular screens with landscape orientation
- **Compilation Flag**: `:horizontal_indicators`
- **Arrangement**: Left-to-right positioning along X-axis
- **Spacing**: Uses `spacingX` parameter for horizontal distance
- **Positioning**:
  - 1 indicator: Centered at `locX`
  - 2 indicators: `locX ± spacing/2`
  - 3 indicators: `locX - spacing`, `locX`, `locX + spacing`

### Vertical Layout
**Target Devices**: Most watch screens (round, square, portrait rectangular)
- **Compilation Flag**: `:vertical_indicators`
- **Arrangement**: Top-to-bottom positioning along Y-axis
- **Spacing**: Uses `spacingY` parameter for vertical distance
- **Positioning**:
  - 1 indicator: Centered at `locY`
  - 2 indicators: `locY ± spacing/2`
  - 3 indicators: `locY - spacing`, `locY`, `locY + spacing`

## Configuration System

### Constructor Parameters
```monkey-c
typedef IndicatorsParams as {
    :locX as Number,        // Center X coordinate
    :locY as Number,        // Center Y coordinate
    :spacingX as Number,    // Horizontal spacing (horizontal layout)
    :spacingY as Number,    // Vertical spacing (vertical layout)
    :batteryWidth as Number // Battery meter width
};
```

### User Settings
- **IndicatorCount**: Number of indicators to display (1-3)
- **Indicator1Type**: Type of first indicator (0-4)
- **Indicator2Type**: Type of second indicator (0-4)
- **Indicator3Type**: Type of third indicator (0-4)

### Layout Selection
The layout is determined at compile time based on target device characteristics:
- Horizontal layout for specific rectangular screen sizes
- Vertical layout for most other screen configurations

## Visual Design

### Color Scheme
- **Active State**: Uses global theme color (`gThemeColour`)
- **Inactive State**: Uses muted background color (`gMeterBackgroundColour`)
- **Background**: Transparent for seamless integration

### Icon Mapping
```
Type 0 (Bluetooth):     "8" → Bluetooth symbol
Type 1 (Alarms):        ":" → Alarm clock
Type 2 (Notifications): "5" → Notification bell
Type 4 (Battery):       Custom graphical meter
```

### Typography
- **Font**: Global icons font (`gIconsFont`)
- **Alignment**: Center-justified both horizontally and vertically
- **Sizing**: Consistent with overall watch face typography

## Performance Characteristics

### Rendering Performance
- **Draw Calls**: 1-3 icon draws + optional battery meter per frame
- **Complexity**: O(n) where n is number of configured indicators
- **Memory**: Minimal - only stores configuration and spacing values

### Resource Usage
- **Memory Footprint**: < 100 bytes per instance
- **CPU Usage**: Negligible during rendering
- **Battery Impact**: Minimal due to efficient icon-based rendering

### Optimization Features
- **Conditional Compilation**: Only includes necessary layout code
- **Array Lookups**: Efficient state evaluation using indexed arrays
- **Early Returns**: Battery indicators bypass icon rendering logic

## Integration Patterns

### Layout Integration
```monkey-c
// In layout.xml
<drawable id="Indicators" class="Indicators">
    <param name="locX">120</param>
    <param name="locY">180</param>
    <param name="spacingY">25</param>
    <param name="batteryWidth">16</param>
</drawable>
```

### Settings Integration
```monkey-c
// User preference handling
function onSettingsChanged() {
    mIndicators.onSettingsChanged();
    // Indicators automatically reload configuration
}
```

### Theme Integration
```monkey-c
// Color coordination
// Indicators automatically use global theme colors
// No additional configuration required
```

## Smart Indicator Logic

### Bluetooth/Notifications Priority
The smart indicator (Type 3) implements intelligent display logic:

```
IF phone_connected AND notifications_exist THEN
    Display: Notification icon (active state)
ELSE
    Display: Bluetooth icon (connection state)
```

### Benefits
- **Information Density**: Shows most relevant status in single indicator
- **User Experience**: Prioritizes actionable information (notifications)
- **Fallback Behavior**: Always provides useful information (connection status)

## Error Handling and Validation

### Settings Validation
- **Null Protection**: Guards against corrupted settings (Issue #123)
- **Type Validation**: Ensures indicator count is numeric
- **Range Checking**: Validates indicator types are within valid range
- **Default Values**: Provides sensible defaults for missing settings

### Graceful Degradation
- **Invalid Types**: Safely handles unknown indicator types
- **Missing Settings**: Uses default configurations
- **Layout Errors**: Falls back to single indicator if spacing fails

## Testing and Validation

### Visual Testing
1. **Indicator States**: Verify all indicators show correct active/inactive states
2. **Layout Positioning**: Confirm proper spacing and alignment for all counts
3. **Color Consistency**: Ensure theme colors are applied correctly
4. **Icon Clarity**: Validate icon visibility and recognition

### Functional Testing
1. **State Changes**: Test indicator updates when device state changes
2. **Settings Changes**: Verify indicators update when user changes preferences
3. **Smart Logic**: Validate Bluetooth/Notifications priority behavior
4. **Battery Meter**: Confirm battery indicator shows accurate levels

### Performance Testing
1. **Render Time**: Measure drawing performance with different indicator counts
2. **Memory Usage**: Monitor memory consumption during operation
3. **Update Frequency**: Test responsiveness to state changes

## Best Practices

### Configuration
✅ **Recommended:**
- Use appropriate spacing for target screen size
- Choose indicator types that provide value to users
- Test with different indicator counts and combinations
- Ensure battery width is proportional to other indicators

❌ **Avoid:**
- Overcrowding indicators in limited space
- Using duplicate indicator types
- Setting spacing too small for icon clarity
- Ignoring layout-specific compilation flags

### Implementation
✅ **Recommended:**
- Handle settings changes gracefully
- Validate user input for indicator configuration
- Use consistent color schemes across indicators
- Test on target device screen sizes

❌ **Avoid:**
- Hardcoding indicator positions
- Ignoring inactive states for visual feedback
- Mixing layout orientations inappropriately
- Bypassing the smart indicator logic

## Troubleshooting

### Common Issues

#### Indicators Not Displaying
**Symptoms:** No indicators visible on watch face
**Causes:**
- IndicatorCount set to 0
- Invalid indicator types configured
- Positioning outside visible area

**Solutions:**
- Verify IndicatorCount > 0 in settings
- Check indicator type values are 0-4
- Validate locX/locY coordinates are on screen

#### Incorrect Spacing
**Symptoms:** Indicators overlapping or too far apart
**Causes:**
- Wrong spacing parameter for layout orientation
- Spacing value too large/small for screen size
- Layout compilation flag mismatch

**Solutions:**
- Use spacingX for horizontal, spacingY for vertical layouts
- Adjust spacing based on screen dimensions
- Verify correct compilation flags for target device

#### Smart Indicator Not Working
**Symptoms:** Bluetooth/Notifications indicator not switching properly
**Causes:**
- Device settings not updating correctly
- Logic conditions not met
- Indicator type not set to 3

**Solutions:**
- Verify indicator type is set to 3 (smart mode)
- Check phone connection and notification states
- Test with known notification states

## Future Enhancements

### Potential Features
- **Additional Indicator Types**: Weather, heart rate, step progress
- **Custom Icons**: User-selectable icon sets
- **Animation Support**: Smooth transitions between states
- **Grouping**: Logical grouping of related indicators

### Layout Improvements
- **Dynamic Spacing**: Automatic spacing based on screen size
- **Curved Layouts**: Support for circular arrangement on round screens
- **Responsive Design**: Adaptive layouts for different screen densities
- **Accessibility**: Enhanced visibility options for different users

## Technical Deep Dive

### Conditional Compilation System

The Indicators class uses Garmin's conditional compilation feature to include only the necessary layout code for each target device:

```monkey-c
(:horizontal_indicators)
function drawIndicators(dc, indicatorCount) {
    // Horizontal layout implementation
}

(:vertical_indicators)
function drawIndicators(dc, indicatorCount) {
    // Vertical layout implementation
}
```

**Benefits:**
- **Memory Efficiency**: Only includes code needed for target device
- **Performance**: Eliminates runtime layout decisions
- **Maintainability**: Separate implementations for different layout needs

### State Evaluation Algorithm

The indicator state evaluation uses array indexing for optimal performance:

```monkey-c
var value = [
    settings.phoneConnected,        // Index 0: Bluetooth
    settings.alarmCount > 0,        // Index 1: Alarms
    settings.notificationCount > 0  // Index 2: Notifications
][indicatorType];
```

**Advantages:**
- **O(1) Lookup**: Constant time state evaluation
- **Memory Efficient**: No conditional branching
- **Extensible**: Easy to add new indicator types

### Smart Indicator Decision Tree

```
Smart Indicator (Type 3) Logic:
├── Phone Connected?
│   ├── Yes → Notifications Exist?
│   │   ├── Yes → Show Notifications (Type 2)
│   │   └── No → Show Bluetooth (Type 0)
│   └── No → Show Bluetooth (Type 0)
```

### Memory Layout Analysis

```
Indicators Instance Memory Usage:
├── Base Drawable: ~32 bytes
├── mSpacing: 4 bytes (Number)
├── mBatteryWidth: 4 bytes (Number)
├── mIndicator1Type: 4 bytes (Number)
├── mIndicator2Type: 4 bytes (Number)
├── mIndicator3Type: 4 bytes (Number)
└── Method Table: ~16 bytes
Total: ~64 bytes per instance
```

### Rendering Pipeline

```
draw() called
├── Validate indicatorCount (Issue #123 protection)
├── Call layout-specific drawIndicators()
├── For each indicator:
│   ├── Check if battery type → drawBatteryMeter()
│   ├── Resolve smart indicator type if needed
│   ├── Evaluate state from device settings
│   ├── Set color based on active/inactive state
│   └── Draw icon with center alignment
└── Complete frame
```

## Code Examples

### Basic Implementation
```monkey-c
class MyWatchFace extends WatchUi.WatchFace {
    private var mIndicators;

    function initialize() {
        WatchFace.initialize();

        var params = {
            :locX => 120,
            :locY => 180,
            :spacingY => 25,
            :batteryWidth => 16
        };
        mIndicators = new Indicators(params);
    }

    function onUpdate(dc) {
        // Draw background
        dc.clear();

        // Draw indicators
        mIndicators.draw(dc);

        // Draw other elements
        drawTime(dc);
    }

    function onSettingsChanged() {
        mIndicators.onSettingsChanged();
    }
}
```

### Custom Indicator Configuration
```monkey-c
// Setting up indicators programmatically
function configureIndicators() {
    // Set indicator count
    setPropertyValue("IndicatorCount", 3);

    // Configure indicator types
    setPropertyValue("Indicator1Type", 0); // Bluetooth
    setPropertyValue("Indicator2Type", 3); // Smart Bluetooth/Notifications
    setPropertyValue("Indicator3Type", 4); // Battery

    // Apply changes
    mIndicators.onSettingsChanged();
}
```

### Dynamic Indicator Management
```monkey-c
function updateIndicatorVisibility() {
    var deviceSettings = System.getDeviceSettings();

    // Hide indicators in low power mode
    if (isLowPowerMode()) {
        setPropertyValue("IndicatorCount", 1);
        setPropertyValue("Indicator1Type", 4); // Battery only
    } else {
        // Full indicator set in normal mode
        setPropertyValue("IndicatorCount", 3);
        setPropertyValue("Indicator1Type", 0); // Bluetooth
        setPropertyValue("Indicator2Type", 2); // Notifications
        setPropertyValue("Indicator3Type", 4); // Battery
    }

    mIndicators.onSettingsChanged();
}
```

### Layout-Specific Positioning
```monkey-c
function calculateIndicatorPosition(screenWidth, screenHeight) {
    var params;

    if (isHorizontalLayout()) {
        // Horizontal layout for landscape screens
        params = {
            :locX => screenWidth / 2,
            :locY => screenHeight - 30,
            :spacingX => 40,
            :spacingY => null,
            :batteryWidth => 20
        };
    } else {
        // Vertical layout for most screens
        params = {
            :locX => screenWidth - 25,
            :locY => screenHeight / 2,
            :spacingX => null,
            :spacingY => 30,
            :batteryWidth => 16
        };
    }

    return new Indicators(params);
}
```

## Advanced Usage Patterns

### Responsive Indicator Sizing
```monkey-c
function getResponsiveIndicatorParams(screenSize) {
    var baseSpacing = screenSize < 200 ? 20 : 30;
    var batteryWidth = screenSize < 200 ? 12 : 16;

    return {
        :locX => screenSize / 2,
        :locY => screenSize - 40,
        :spacingY => baseSpacing,
        :batteryWidth => batteryWidth
    };
}
```

### Theme-Aware Indicator Colors
```monkey-c
function updateIndicatorTheme(themeColor, backgroundColor) {
    // Indicators automatically use global theme colors
    gThemeColour = themeColor;
    gMeterBackgroundColour = backgroundColor;

    // No additional configuration needed
    // Next draw() call will use new colors
}
```

### Performance Monitoring
```monkey-c
function measureIndicatorPerformance() {
    var startTime = System.getTimer();

    mIndicators.draw(dc);

    var endTime = System.getTimer();
    var renderTime = endTime - startTime;

    System.println("Indicator render time: " + renderTime + "ms");
}
```

## Integration with Watch Face Ecosystem

### Settings Integration
```xml
<!-- In settings.xml -->
<setting propertyKey="@Properties.IndicatorCount" title="@Strings.IndicatorCountTitle">
    <settingConfig type="list">
        <listEntry value="1">@Strings.One</listEntry>
        <listEntry value="2">@Strings.Two</listEntry>
        <listEntry value="3">@Strings.Three</listEntry>
    </settingConfig>
</setting>

<setting propertyKey="@Properties.Indicator1Type" title="@Strings.Indicator1TypeTitle">
    <settingConfig type="list">
        <listEntry value="0">@Strings.Bluetooth</listEntry>
        <listEntry value="1">@Strings.Alarms</listEntry>
        <listEntry value="2">@Strings.Notifications</listEntry>
        <listEntry value="3">@Strings.Smart</listEntry>
        <listEntry value="4">@Strings.Battery</listEntry>
    </settingConfig>
</setting>
```

### Layout System Integration
```xml
<!-- In layout.xml -->
<layout id="WatchFace">
    <drawable id="Background" class="Background"/>
    <drawable id="Indicators" class="Indicators">
        <param name="locX">120</param>
        <param name="locY">180</param>
        <param name="spacingY">25</param>
        <param name="batteryWidth">16</param>
    </drawable>
    <drawable id="TimeDisplay" class="TimeDisplay"/>
</layout>
```

### Manifest Configuration
```xml
<!-- In manifest.xml -->
<iq:application>
    <iq:products>
        <iq:product id="vivoactive3">
            <iq:annotation name="layout" value="vertical_indicators"/>
        </iq:product>
        <iq:product id="fenix6">
            <iq:annotation name="layout" value="horizontal_indicators"/>
        </iq:product>
    </iq:products>
</iq:application>
```

This comprehensive documentation provides complete coverage of the Indicators functionality, making it easy for developers to understand, implement, customize, and maintain the indicator system in their watch face projects.
