<?xml version="1.0"?>
<!-- This is a generated file. It is highly recommended that you DO NOT edit this file. -->
<iq:manifest xmlns:iq="http://www.garmin.com/xml/connectiq" version="3">
    <iq:application entry="MadreApp" id="bde5c058244911e8b4670ed5f89f718b" launcherIcon="@Drawables.LauncherIcon" minSdkVersion="1.4.0" name="@Strings.AppName" type="watchface" version="2.7.0">
        <iq:products>
            <iq:product id="approachs60"/>
            <iq:product id="approachs62"/>
            <iq:product id="approachs7042mm"/>
            <iq:product id="approachs7047mm"/>
            <iq:product id="d2air"/>
            <iq:product id="d2airx10"/>
            <iq:product id="d2bravo"/>
            <iq:product id="d2bravo_titanium"/>
            <iq:product id="d2charlie"/>
            <iq:product id="d2delta"/>
            <iq:product id="d2deltapx"/>
            <iq:product id="d2deltas"/>
            <iq:product id="d2mach1"/>
            <iq:product id="descentmk1"/>
            <iq:product id="descentmk2"/>
            <iq:product id="descentmk2s"/>
            <iq:product id="descentmk343mm"/>
            <iq:product id="descentmk351mm"/>
            <iq:product id="enduro"/>
            <iq:product id="enduro3"/>
            <iq:product id="epix2"/>
            <iq:product id="epix2pro42mm"/>
            <iq:product id="epix2pro47mm"/>
            <iq:product id="epix2pro51mm"/>
            <iq:product id="fenix3"/>
            <iq:product id="fenix3_hr"/>
            <iq:product id="fenix5"/>
            <iq:product id="fenix5plus"/>
            <iq:product id="fenix5s"/>
            <iq:product id="fenix5splus"/>
            <iq:product id="fenix5x"/>
            <iq:product id="fenix5xplus"/>
            <iq:product id="fenix6"/>
            <iq:product id="fenix6pro"/>
            <iq:product id="fenix6s"/>
            <iq:product id="fenix6spro"/>
            <iq:product id="fenix6xpro"/>
            <iq:product id="fenix7"/>
            <iq:product id="fenix7pro"/>
            <iq:product id="fenix7pronowifi"/>
            <iq:product id="fenix7s"/>
            <iq:product id="fenix7spro"/>
            <iq:product id="fenix7x"/>
            <iq:product id="fenix7xpro"/>
            <iq:product id="fenix7xpronowifi"/>
            <iq:product id="fenix843mm"/>
            <iq:product id="fenix847mm"/>
            <iq:product id="fenix8solar47mm"/>
            <iq:product id="fenix8solar51mm"/>
            <iq:product id="fenixchronos"/>
            <iq:product id="fenixe"/>
            <iq:product id="fr165"/>
            <iq:product id="fr165m"/>
            <iq:product id="fr230"/>
            <iq:product id="fr235"/>
            <iq:product id="fr245"/>
            <iq:product id="fr245m"/>
            <iq:product id="fr255"/>
            <iq:product id="fr255m"/>
            <iq:product id="fr255s"/>
            <iq:product id="fr255sm"/>
            <iq:product id="fr265"/>
            <iq:product id="fr265s"/>
            <iq:product id="fr45"/>
            <iq:product id="fr55"/>
            <iq:product id="fr630"/>
            <iq:product id="fr645"/>
            <iq:product id="fr645m"/>
            <iq:product id="fr735xt"/>
            <iq:product id="fr745"/>
            <iq:product id="fr920xt"/>
            <iq:product id="fr935"/>
            <iq:product id="fr945"/>
            <iq:product id="fr945lte"/>
            <iq:product id="fr955"/>
            <iq:product id="fr965"/>
            <iq:product id="garminswim2"/>
            <iq:product id="instinct3amoled45mm"/>
            <iq:product id="instinct3amoled50mm"/>
            <iq:product id="legacyherocaptainmarvel"/>
            <iq:product id="legacyherofirstavenger"/>
            <iq:product id="legacysagadarthvader"/>
            <iq:product id="legacysagarey"/>
            <iq:product id="marq2"/>
            <iq:product id="marq2aviator"/>
            <iq:product id="marqadventurer"/>
            <iq:product id="marqathlete"/>
            <iq:product id="marqaviator"/>
            <iq:product id="marqcaptain"/>
            <iq:product id="marqcommander"/>
            <iq:product id="marqdriver"/>
            <iq:product id="marqexpedition"/>
            <iq:product id="marqgolfer"/>
            <iq:product id="venu"/>
            <iq:product id="venu2"/>
            <iq:product id="venu2plus"/>
            <iq:product id="venu2s"/>
            <iq:product id="venu3"/>
            <iq:product id="venu3s"/>
            <iq:product id="venud"/>
            <iq:product id="venusq"/>
            <iq:product id="venusq2"/>
            <iq:product id="venusq2m"/>
            <iq:product id="venusqm"/>
            <iq:product id="vivoactive"/>
            <iq:product id="vivoactive3"/>
            <iq:product id="vivoactive3d"/>
            <iq:product id="vivoactive3m"/>
            <iq:product id="vivoactive3mlte"/>
            <iq:product id="vivoactive4"/>
            <iq:product id="vivoactive4s"/>
            <iq:product id="vivoactive5"/>
            <iq:product id="vivoactive6"/>
            <iq:product id="vivoactive_hr"/>
            <iq:product id="vivolife"/>
        </iq:products>
        <iq:permissions>
            <iq:uses-permission id="Background"/>
            <iq:uses-permission id="Communications"/>
            <iq:uses-permission id="Positioning"/>
            <iq:uses-permission id="SensorHistory"/>
        </iq:permissions>
        <iq:languages>
            <iq:language>ces</iq:language>
            <iq:language>dan</iq:language>
            <iq:language>deu</iq:language>
            <iq:language>dut</iq:language>
            <iq:language>eng</iq:language>
            <iq:language>fin</iq:language>
            <iq:language>fre</iq:language>
            <iq:language>hrv</iq:language>
            <iq:language>hun</iq:language>
            <iq:language>ita</iq:language>
            <iq:language>kor</iq:language>
            <iq:language>nob</iq:language>
            <iq:language>pol</iq:language>
            <iq:language>por</iq:language>
            <iq:language>rus</iq:language>
            <iq:language>slo</iq:language>
            <iq:language>slv</iq:language>
            <iq:language>spa</iq:language>
            <iq:language>swe</iq:language>
            <iq:language>zhs</iq:language>
            <iq:language>zht</iq:language>
        </iq:languages>
        <iq:barrels/>
    </iq:application>
</iq:manifest>